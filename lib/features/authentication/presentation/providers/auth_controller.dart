import 'dart:async';

import 'package:firebase_auth/firebase_auth.dart' as firebase_auth;
import 'package:flutter/foundation.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:selfeng/services/user_cache_service/domain/providers/user_cache_provider.dart';
import 'package:selfeng/services/user_cache_service/domain/repositories/user_cache_repository.dart';
import 'package:selfeng/shared/domain/models/user/user_model.dart' as app_user;
import 'package:selfeng/shared/domain/providers/shared_preferences_storage_service_provider.dart';
import 'package:selfeng/shared/exceptions/unauthorized_exception.dart';
import 'package:selfeng/shared/globals.dart';
import 'package:selfeng/features/setting/presentation/providers/notification_settings_provider.dart';
import 'package:selfeng/features/authentication/domain/repositories/auth_repository.dart';
import 'package:selfeng/features/authentication/domain/repositories/google_sign_in_repository.dart';
import 'package:selfeng/features/authentication/data/factories/auth_repository_factory.dart';
import 'package:selfeng/services/crashlytics_service/domain/providers/crashlytics_service_provider.dart';

import 'state/auth_state.dart';

part 'auth_controller.g.dart';

// Configuration class for AuthController
class AuthConfig {
  final Duration googleSignInTimeout;
  final Duration googleSignInRetryDelay;
  final Duration initializationDelay;
  final bool rethrowGoogleSignInErrors;
  final bool rethrowInitializationErrors;
  final bool rethrowNotificationErrors;

  const AuthConfig({
    this.googleSignInTimeout = const Duration(seconds: 30),
    this.googleSignInRetryDelay = const Duration(milliseconds: 500),
    this.initializationDelay = const Duration(seconds: 3),
    this.rethrowGoogleSignInErrors = false,
    this.rethrowInitializationErrors = false,
    this.rethrowNotificationErrors = false,
  });
}

@riverpod
class AuthController extends _$AuthController {
  // Default constructor required by Riverpod
  AuthController();

  late final UserRepository userRepository;
  late final AuthRepository _authRepository;
  late final GoogleSignInRepository _googleSignInRepository;
  late final AuthConfig _config;

  @override
  Future<AuthState> build() async {
    // Initialize dependencies
    userRepository = ref.watch(userLocalRepositoryProvider);
    _config = const AuthConfig();

    // Use factory methods to create repositories with default instances
    _authRepository = AuthRepositoryFactory.createAuthRepository();
    _googleSignInRepository =
        AuthRepositoryFactory.createGoogleSignInRepository();

    // Initialize the controller
    await _initializeController();

    return const AuthState.loading();
  }

  // Constructor for testing with mocked dependencies
  AuthController.test({
    required this.userRepository,
    required AuthRepository authRepository,
    required GoogleSignInRepository googleSignInRepository,
    AuthConfig? config,
  }) : _authRepository = authRepository,
       _googleSignInRepository = googleSignInRepository,
       _config = config ?? const AuthConfig();

  /// Initialize the controller with all necessary setup
  Future<void> _initializeController() async {
    // Clear any potentially corrupted cached data from previous versions
    await _clearCachedAuthData();

    // Initialize GoogleSignIn - required in v7.x
    await _initializeGoogleSignIn();

    // Set up Google Sign-In authentication events listener
    _setupGoogleSignInListener();

    _setupPersistenceListener();
  }

  Future<void> logout() async {
    // Unsubscribe from all notification topics before logout
    _cleanupNotificationSubscriptions();

    // Sign out from Firebase Auth
    await _authRepository.signOut();

    // Also disconnect from Google Sign-In to ensure complete logout
    // In v7.x, disconnect() is still available and recommended for complete logout
    await _googleSignInRepository.disconnect();

    // Delete user data when signing out
    await userRepository.deleteUser();

    state = const AsyncData(AuthState.signedOut());
  }

  Future<void> loginUser() async {
    try {
      // For mobile, the state will be set by the authentication event handler
      await _handleMobileSignIn();
    } on firebase_auth.FirebaseAuthException catch (e, stackTrace) {
      // Report authentication error
      final crashlytics = ref.read(crashlyticsServiceProvider);
      await crashlytics
          .recordError(
            e,
            stackTrace,
            reason: 'Firebase Authentication Error',
            context: {
              'category': 'authentication',
              'auth_method': 'google_sign_in',
              'error_code': e.code,
            },
            fatal: false,
          )
          .catchError(
            (reportError) =>
                debugPrint('Failed to report auth error: $reportError'),
          );

      state = AsyncError(
        UnauthorizedException(e.message ?? 'Authentication failed'),
        stackTrace,
      );
    } catch (e, stackTrace) {
      // Report general authentication error
      final crashlytics = ref.read(crashlyticsServiceProvider);
      await crashlytics
          .recordError(
            e,
            stackTrace,
            reason: 'Authentication Error',
            context: {
              'category': 'authentication',
              'auth_method': 'google_sign_in',
            },
            fatal: false,
          )
          .catchError(
            (reportError) =>
                debugPrint('Failed to report auth error: $reportError'),
          );

      state = AsyncError(
        UnauthorizedException('An unexpected error occurred: $e'),
        stackTrace,
      );
    }
  }

  Future<void> _handleMobileSignIn() async {
    try {
      // In v7.x, we use authenticate() instead of signIn()
      if (_googleSignInRepository.supportsAuthenticate()) {
        // Add timeout to prevent infinite loading
        await _googleSignInRepository.authenticate().timeout(
          _config.googleSignInTimeout,
          onTimeout: () {
            throw Exception('Google Sign-In authentication timed out');
          },
        );

        // The authentication events listener will handle the rest
      } else {
        // For platforms that don't support authenticate (like web),
        // we would need platform-specific handling
        throw UnsupportedError('Platform does not support authenticate method');
      }
    } catch (e) {
      if (_config.rethrowGoogleSignInErrors) {
        rethrow;
      } else {
        rethrow;
      }
    }
  }

  void _setupGoogleSignInListener() {
    // Listen to Google Sign-In authentication events
    _googleSignInRepository.authenticationEvents.listen(
      _handleGoogleSignInEvent,
      onError: _handleGoogleSignInError,
    );
  }

  Future<void> _handleGoogleSignInEvent(
    GoogleSignInAuthenticationEvent event,
  ) async {
    try {
      switch (event) {
        case GoogleSignInAuthenticationEventSignIn():
          final googleAuth = event.user.authentication;

          if (googleAuth.idToken != null) {
            final credential = firebase_auth.GoogleAuthProvider.credential(
              idToken: googleAuth.idToken,
            );

            final firebaseUserCredential = await _authRepository
                .signInWithCredential(credential);

            // Get the Firebase user and convert to our User model
            final firebaseUser = firebaseUserCredential.user;
            if (firebaseUser != null) {
              // Safely split the display name to extract first and last names
              String firstName = '';
              String lastName = '';
              if (firebaseUser.displayName != null &&
                  firebaseUser.displayName!.isNotEmpty) {
                final nameParts = firebaseUser.displayName!
                    .split(' ')
                    .where((s) => s.isNotEmpty)
                    .toList();
                if (nameParts.isNotEmpty) {
                  firstName = nameParts.first;
                  lastName = nameParts.skip(1).join(' ');
                }
              }

              final user = app_user.User(
                id: int.tryParse(firebaseUser.uid.hashCode.toString()) ?? 0,
                username: firebaseUser.displayName ?? '',
                email: firebaseUser.email ?? '',
                firstName: firstName,
                lastName: lastName,
                image: firebaseUser.photoURL ?? '',
                token:
                    await _authRepository.getIdToken(true) ??
                    '', // Get the Firebase ID token
              );

              // Save user data when signing in
              await userRepository.saveUser(user: user);

              // Update state to signedIn with user data
              state = AsyncData(AuthState.signedIn(user: user));

              // Initialize notification subscriptions after successful authentication
              _initializeNotificationSubscriptions();
            } else {
              throw Exception(
                'Firebase user is null after successful authentication',
              );
            }
          } else {
            throw Exception('No idToken available from Google Sign-In');
          }
          break;

        case GoogleSignInAuthenticationEventSignOut():
          state = const AsyncData(AuthState.signedOut());
          break;
      }
    } catch (e) {
      state = AsyncError(
        UnauthorizedException('Google Sign-In failed: $e'),
        StackTrace.current,
      );
    }
  }

  void _handleGoogleSignInError(Object error) {
    state = AsyncError(
      UnauthorizedException('Google Sign-In stream error: $error'),
      StackTrace.current,
    );
  }

  Future<void> _initializeGoogleSignIn() async {
    try {
      await _googleSignInRepository.initialize();
    } catch (e) {
      // Try to reinitialize after a brief delay
      await Future.delayed(_config.googleSignInRetryDelay);
      try {
        await _googleSignInRepository.initialize();
      } catch (retryError) {
        if (_config.rethrowInitializationErrors) {
          rethrow;
        }
        // Don't throw - let the app continue, but authentication might not work
      }
    }
  }

  Future<void> _clearCachedAuthData() async {
    try {
      // Get storage service to check migration flag
      final storageService = ref.read(storageServiceProvider);

      // Check if we've already performed the Google Sign-In v7.x migration
      final hasMigrated = await storageService.has(
        GOOGLE_SIGNIN_V7_MIGRATION_KEY,
      );

      if (hasMigrated) {
        return;
      }

      // Clear local user data that might be corrupted from previous versions
      await userRepository.deleteUser();

      // Sign out from Firebase Auth to clear any cached tokens
      if (_authRepository.currentUser != null) {
        await _authRepository.signOut();
      }

      // Disconnect from Google Sign-In to clear any cached Google auth data
      try {
        await _googleSignInRepository.disconnect();
      } catch (e) {
        if (_config.rethrowInitializationErrors) {
          rethrow;
        }
        // It's okay if disconnect fails (user might not be signed in)
      }

      // Mark migration as completed
      await storageService.set(GOOGLE_SIGNIN_V7_MIGRATION_KEY, 'true');
    } catch (e) {
      if (_config.rethrowInitializationErrors) {
        rethrow;
      }
      // Don't throw - we want the app to continue even if migration fails
    }
  }

  void _setupPersistenceListener() {
    // In AsyncNotifier, we don't use listenSelf
    // Instead, we handle persistence directly in the methods that change state
  }

  /// Initialize notification subscriptions after successful authentication
  void _initializeNotificationSubscriptions() {
    try {
      // Initialize notification subscriptions based on user preferences
      // This will subscribe to topics that the user has enabled
      ref.read(notificationSettingsProvider.notifier).initializeSubscriptions();
    } catch (e) {
      if (_config.rethrowNotificationErrors) {
        rethrow;
      }
      // Don't throw - notification subscription failure shouldn't break authentication
    }
  }

  /// Cleanup notification subscriptions before logout
  void _cleanupNotificationSubscriptions() {
    try {
      // Disable all notification subscriptions
      ref.read(notificationSettingsProvider.notifier).disableAllNotifications();
    } catch (e) {
      if (_config.rethrowNotificationErrors) {
        rethrow;
      }
      // Don't throw - notification cleanup failure shouldn't break logout
    }
  }
}
