# Bookmark Feature Product Requirements Document (PRD) - REVISED

## 1. Introduction

### 1.1 Purpose
This PRD outlines the requirements for implementing a **completely redesigned** Bookmark feature in the SelfEng mobile application. The feature allows users to view, manage, and filter their bookmarked content from all sections of the app with a clean, unified approach.

### 1.2 Scope
- **In Scope**: Unified bookmark management, Firestore unified collection, infinite scrolling list, search/filter functionality, text-based UI design
- **Out of Scope**: Backward compatibility, image thumbnails, offline support (initial version), bookmark sharing

### 1.3 Background
This is a **complete redesign** of the bookmark system. The existing section-specific implementation will be replaced with a unified, simplified approach that eliminates complexity and improves performance.

### 1.4 Target Users
- Primary: SelfEng users who bookmark educational content for later review
- Secondary: Power users needing quick search and organized access to saved content

## 2. Goals and Objectives

### 2.1 Business Goals
- Simplify bookmark management with unified data structure
- Improve user engagement through better bookmark accessibility
- Reduce development complexity by eliminating section-specific logic
- Enhance performance with text-based, lightweight UI

### 2.2 User Goals
- Access all bookmarked content in one unified view
- Quickly search and filter bookmarks by section or keyword
- Navigate seamlessly to original content from bookmarks
- Experience fast, responsive bookmark interactions

### 2.3 Success Metrics
- User adoption: 30% of active users access bookmarks within first month
- Performance: Load first 20 items < 1 second (improved from 2s)
- User satisfaction: 90% positive feedback on simplified interface

## 3. User Stories

### 3.1 As a User, I Want...
- **US-1**: To access bookmarks from profile settings and see all my saved content in one place
- **US-2**: To see bookmarks sorted by save time (newest first) with clear section indicators
- **US-3**: To scroll infinitely through bookmarks with smooth pagination
- **US-4**: To search bookmarks by title and filter by section
- **US-5**: To tap any bookmark and navigate directly to the original content
- **US-6**: To see an encouraging empty state when I have no bookmarks yet

### 3.2 Acceptance Criteria
- For US-1: Single tap from profile settings opens unified bookmark view
- For US-2: Text-based cards show title, section badge, level, chapter, and timestamp
- For US-3: Auto-loads 20 items per batch, seamless infinite scroll
- For US-4: Real-time search with section filter chips
- For US-5: Deep-linking to exact content location with proper context
- For US-6: Empty state with "Explore Content" call-to-action

## 4. Technical Specifications

### 4.1 Revised Data Model (Simplified)
```dart
@freezed
sealed class Bookmark with _$Bookmark {
  const factory Bookmark({
    required String id,              // Firestore document ID
    required String contentPath,     // Original content path
    required String section,     // 'pronunciation', 'conversation', 'listening', 'speaking'
    required String title,           // Content title for display
    required DateTime timestamp,     // For chronological sorting
    required String level,           // e.g., 'beginner', 'intermediate' 
    required int chapter,            // Chapter number
    @Default(true) bool isBookmarked, // Always true for active bookmarks
  }) = _Bookmark;
  
  factory Bookmark.fromJson(Map<String, dynamic> json) => _$BookmarkFromJson(json);
}
```

### 4.2 Unified Firestore Structure
**New Collection Path:**
```
/user-data/{userID}/bookmarks/{bookmarkId}
```

**Document Structure:**
```json
{
  "id": "auto_generated_firestore_id",
  "contentPath": "pronunciation/beginner/chapter1/part1/content1",
  "section": "pronunciation",
  "title": "Basic Pronunciation Exercise", 
  "timestamp": "2024-01-15T10:30:00Z",
  "level": "beginner",
  "chapter": 1,
  "isBookmarked": true
}
```

**Required Indexes:**
- `timestamp (desc)` + `section (asc)`
- `timestamp (desc)` + `level (asc)`

### 4.3 Clean Architecture
```
lib/features/bookmark/
├── domain/
│   ├── models/bookmark.dart
│   └── repositories/bookmark_repository.dart
├── data/
│   ├── repositories/bookmark_repository_impl.dart
│   └── datasources/bookmark_firestore_datasource.dart
└── presentation/
    ├── providers/bookmark_provider.dart
    ├── screens/bookmark_screen.dart
    └── widgets/
        ├── bookmark_card.dart
        ├── bookmark_search_bar.dart
        └── bookmark_empty_state.dart
```

### 4.4 Repository Interface (Simplified)
```dart
abstract class BookmarkRepository {
  /// Save/toggle bookmark - unified method
  Future<Either<AppException, void>> saveBookmark({
    required String contentPath,
    required String section,
    required String title,
    required String level,
    required int chapter,
  });
  
  /// Get paginated bookmarks for bookmark screen
  Future<Either<AppException, List<Bookmark>>> getBookmarks({
    int limit = 20,
    DocumentSnapshot? startAfter,
    String? section,
  });
  
  /// Check if content is bookmarked (for UI state)
  Future<Either<AppException, bool>> isBookmarked(String contentPath);
  
  /// Remove bookmark
  Future<Either<AppException, void>> deleteBookmark(String contentPath);
}
```

## 5. UI/UX Design (Text-Focused)

### 5.1 Layout Design
Since thumbnails are removed, the design focuses on **clear typography and section indicators**:

```
┌─────────────────────────────────────────┐
│ ← Bookmarks                         🔍  │
├─────────────────────────────────────────┤
│ Search bookmarks...                     │
├─────────────────────────────────────────┤
│ [All] [Pronunciation] [Conversation]    │
├─────────────────────────────────────────┤
│ 🎤 Basic Pronunciation Exercise     2d  │
│    Beginner • Chapter 1                │
├─────────────────────────────────────────┤
│ 💬 Daily Conversation               5d  │
│    Intermediate • Chapter 3             │
├─────────────────────────────────────────┤
│ 🎧 Listening Comprehension          1w  │
│    Advanced • Chapter 2                 │
└─────────────────────────────────────────┘
```

### 5.2 Bookmark Card Components
```dart
Widget _buildBookmarkCard(Bookmark bookmark) {
  return Card(
    child: ListTile(
      leading: _buildSectionIcon(bookmark.section),
      title: Text(bookmark.title),
      subtitle: Text('${bookmark.level.toTitleCase()} • Chapter ${bookmark.chapter}'),
      trailing: Text(_formatTimestamp(bookmark.timestamp)),
      onTap: () => _navigateToContent(bookmark),
    ),
  );
}
```

### 5.3 Section Visual System
```dart
final Map<String, IconData> sectionIcons = {
  'pronunciation': Icons.record_voice_over,
  'conversation': Icons.chat,
  'listening': Icons.headphones,
  'speaking': Icons.mic,
};

final Map<String, Color> sectionColors = {
  'pronunciation': Color(0xFFFF6B6B),
  'conversation': Color(0xFF4ECDC4),
  'listening': Color(0xFF45B7D1),
  'speaking': Color(0xFFE6A742),
};
```

### 5.4 Performance Benefits (No Images)
- **Faster rendering**: Text-only components
- **Reduced memory**: No image caching required
- **Better offline**: All content readable without network
- **Improved accessibility**: High contrast text focus

## 6. Integration Requirements

### 6.1 Controller Updates (All Simplified)
```dart
// Replace complex bookmark logic in all controllers with:
Future<void> saveBookmark() async {
  final content = getCurrentContent();
  
  if (content.isBookmarked) {
    await _repository.deleteBookmark(contentPath: content.path);
  } else {
    await _repository.saveBookmark(
      contentPath: content.path,
      section: getSection(),
      title: content.title,
      level: level,
      chapter: int.parse(chapter),
    );
  }
  
  updateContentBookmarkState(!content.isBookmarked);
}
```

### 6.2 Navigation Integration
```dart
// Add to router configuration
GoRoute(
  path: '/bookmarks',
  builder: (context, state) => const BookmarkScreen(),
)

// Update profile settings (existing TODO)
onTap: () => context.push('/bookmarks'),
```

### 6.3 Required Localization Keys
```dart
"bookmarks": "Bookmarks",
"search_bookmarks": "Search bookmarks...",
"no_bookmarks": "No bookmarks yet",
"bookmark_added": "Bookmark added",
"bookmark_removed": "Bookmark removed",
"explore_content": "Explore Content",
"filter_by_type": "Filter by section",
```

## 7. Implementation Plan

### 7.1 Timeline (1 week total)
```
Day 1: Foundation
├── Create new Bookmark model
├── Set up feature structure
└── Create Firestore indexes

Days 2-3: Backend
├── Implement BookmarkRepository
├── Create Firestore datasource
└── Replace old bookmark methods

Days 4-5: Frontend  
├── Build BookmarkScreen
├── Create text-based bookmark cards
├── Implement search and filters
└── Add navigation integration

Day 6: Integration
├── Update all controllers
├── Remove old bookmark logic
└── Delete old Firestore collections

Day 7: Testing & Polish
├── Unit tests
├── Widget tests
└── Integration testing
```

### 7.2 Migration Strategy (No Backward Compatibility)
1. **Delete** old section-specific collections: `/user-data/{userID}/bookmarks/sections/`
2. **Create** new unified collection: `/user-data/{userID}/bookmarks/`
3. **Recreate** bookmarks as users interact with content
4. **No data migration** required - clean slate approach

## 8. Testing Strategy

### 8.1 Testing Focus
- **Core functionality**: Save, delete, load bookmarks with unified structure
- **UI components**: Text-based cards, search, section filters
- **Performance**: Pagination, infinite scroll, text rendering
- **Navigation**: Deep links to original content locations
- **Error handling**: Network failures, empty states

### 8.2 Test Coverage
```dart
// Unit Tests
- Bookmark model serialization
- Repository method contracts
- Business logic validation

// Widget Tests  
- BookmarkScreen rendering
- Search and filter functionality
- Empty state display

// Integration Tests
- Complete bookmark workflow
- Navigation between bookmark and content
- Cross-section bookmark consistency
```

## 9. Success Criteria

### 9.1 Performance Targets
- **Load time**: < 1 second for first 20 bookmarks
- **Pagination**: < 500ms for additional loads
- **Search**: Real-time filtering without lag
- **Memory usage**: 30% reduction (no image caching)

### 9.2 User Experience Goals
- **Simplicity**: Single tap from any bookmarked content to bookmark screen
- **Clarity**: Clear section identification without thumbnails
- **Responsiveness**: Smooth scrolling and instant search
- **Consistency**: Unified bookmark behavior across all sections

## 10. Risks and Mitigation

### 10.1 Identified Risks
- **Data Loss**: Complete replacement of bookmark system
  - *Mitigation*: User communication about clean slate approach
- **User Adaptation**: New interface without thumbnails
  - *Mitigation*: Enhanced typography and clear section indicators
- **Performance**: Unified queries across all sections
  - *Mitigation*: Proper Firestore indexing and pagination

### 10.2 Dependencies
- **Required**: Existing Firestore, Firebase Auth, Riverpod, GoRouter
- **Assumptions**: Users accept bookmark recreation (no migration)
- **Critical Path**: Repository implementation must be complete before UI work

## 11. Conclusion

This revised bookmark feature represents a **significant simplification** of the existing implementation. By removing backward compatibility requirements and thumbnail dependencies, we achieve:

✅ **Cleaner Architecture**: Unified data model and repository  
✅ **Better Performance**: Text-based UI, simplified queries  
✅ **Improved UX**: Single bookmark view, better search/filter  
✅ **Reduced Complexity**: No section-specific logic or migration concerns  

The 1-week implementation timeline is achievable due to the simplified scope and clean-slate approach.