import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:selfeng/features/bookmark/domain/models/bookmark.dart';
import 'package:selfeng/shared/domain/models/either.dart';
import 'package:selfeng/shared/exceptions/http_exception.dart';

abstract class BookmarkFirestoreDatasource {
  Future<Either<AppException, void>> saveBookmark({
    required String contentPath,
    required String section,
    required String title,
    required String level,
    required int chapter,
    int? stage,
  });

  Future<Either<AppException, List<Bookmark>>> getBookmarks({
    int limit = 20,
    DocumentSnapshot? startAfter,
    String? section,
  });

  Future<Either<AppException, bool>> isBookmarked(
    String contentPath, {
    int? stage,
  });

  Future<Either<AppException, void>> deleteBookmark(
    String contentPath, {
    int? stage,
  });
}
