// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'bookmark_firestore_datasource_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, type=warning

@ProviderFor(bookmarkFirestoreDatasource)
const bookmarkFirestoreDatasourceProvider =
    BookmarkFirestoreDatasourceProvider._();

final class BookmarkFirestoreDatasourceProvider
    extends
        $FunctionalProvider<
          BookmarkFirestoreDatasource,
          BookmarkFirestoreDatasource,
          BookmarkFirestoreDatasource
        >
    with $Provider<BookmarkFirestoreDatasource> {
  const BookmarkFirestoreDatasourceProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'bookmarkFirestoreDatasourceProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$bookmarkFirestoreDatasourceHash();

  @$internal
  @override
  $ProviderElement<BookmarkFirestoreDatasource> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  BookmarkFirestoreDatasource create(Ref ref) {
    return bookmarkFirestoreDatasource(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(BookmarkFirestoreDatasource value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<BookmarkFirestoreDatasource>(value),
    );
  }
}

String _$bookmarkFirestoreDatasourceHash() =>
    r'42fc5fbbe8634c553084d409b1756208e9bf3fa7';
