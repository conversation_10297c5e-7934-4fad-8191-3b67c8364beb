import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:selfeng/features/bookmark/data/datasources/bookmark_firestore_datasource.dart';
import 'package:selfeng/features/bookmark/domain/models/bookmark.dart';
import 'package:selfeng/features/bookmark/domain/repositories/bookmark_repository.dart';
import 'package:selfeng/shared/domain/models/either.dart';
import 'package:selfeng/shared/exceptions/http_exception.dart';

class BookmarkRepositoryImpl implements BookmarkRepository {
  final BookmarkFirestoreDatasource _datasource;

  BookmarkRepositoryImpl(this._datasource);

  @override
  Future<Either<AppException, void>> saveBookmark({
    required String contentPath,
    required String section,
    required String title,
    required String level,
    required int chapter,
    int? stage,
  }) async {
    return await _datasource.saveBookmark(
      contentPath: contentPath,
      section: section,
      title: title,
      level: level,
      chapter: chapter,
      stage: stage,
    );
  }

  @override
  Future<Either<AppException, List<Bookmark>>> getBookmarks({
    int limit = 20,
    DocumentSnapshot? startAfter,
    String? section,
  }) async {
    return await _datasource.getBookmarks(
      limit: limit,
      startAfter: startAfter,
      section: section,
    );
  }

  @override
  Future<Either<AppException, bool>> isBookmarked(
    String contentPath, {
    int? stage,
  }) async {
    return await _datasource.isBookmarked(contentPath, stage: stage);
  }

  @override
  Future<Either<AppException, void>> deleteBookmark(
    String contentPath, {
    int? stage,
  }) async {
    return await _datasource.deleteBookmark(contentPath, stage: stage);
  }
}
