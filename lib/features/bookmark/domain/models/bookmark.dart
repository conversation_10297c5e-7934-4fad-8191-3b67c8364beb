import 'package:freezed_annotation/freezed_annotation.dart';

part 'bookmark.freezed.dart';
part 'bookmark.g.dart';

@freezed
sealed class Bookmark with _$Bookmark {
  const factory Bookmark({
    required String id,
    required String contentPath,
    required String section,
    required String title,
    required DateTime timestamp,
    required String level,
    required int chapter,
    int? stage,
    @Default(true) bool isBookmarked,
  }) = _Bookmark;

  factory Bookmark.fromJson(Map<String, dynamic> json) =>
      _$BookmarkFromJson(json);
}
