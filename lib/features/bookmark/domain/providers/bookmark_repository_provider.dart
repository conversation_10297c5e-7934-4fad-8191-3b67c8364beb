import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:selfeng/features/bookmark/data/providers/bookmark_firestore_datasource_provider.dart';
import 'package:selfeng/features/bookmark/data/repositories/bookmark_repository_impl.dart';
import 'package:selfeng/features/bookmark/domain/repositories/bookmark_repository.dart';

part 'bookmark_repository_provider.g.dart';

@riverpod
BookmarkRepository bookmarkRepository(Ref ref) {
  return BookmarkRepositoryImpl(ref.watch(bookmarkFirestoreDatasourceProvider));
}
