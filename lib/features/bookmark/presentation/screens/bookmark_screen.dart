import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:selfeng/features/bookmark/presentation/providers/bookmark_provider.dart';
import 'package:selfeng/features/bookmark/presentation/widgets/bookmark_card.dart';
import 'package:selfeng/features/bookmark/presentation/widgets/bookmark_empty_state.dart';
import 'package:selfeng/features/bookmark/presentation/widgets/bookmark_search_bar.dart';

class BookmarkScreen extends ConsumerStatefulWidget {
  const BookmarkScreen({super.key});

  @override
  ConsumerState<BookmarkScreen> createState() => _BookmarkScreenState();
}

class _BookmarkScreenState extends ConsumerState<BookmarkScreen> {
  final ScrollController _scrollController = ScrollController();
  final GlobalKey<RefreshIndicatorState> _refreshIndicatorKey =
      GlobalKey<RefreshIndicatorState>();

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_scrollListener);
  }

  @override
  void dispose() {
    _scrollController.removeListener(_scrollListener);
    _scrollController.dispose();
    super.dispose();
  }

  void _scrollListener() {
    if (_scrollController.position.pixels ==
        _scrollController.position.maxScrollExtent) {
      _loadMoreBookmarks();
    }
  }

  void _loadMoreBookmarks() {
    final state = ref.read(bookmarkListProvider);
    if (state.isLoading || state.hasError) return;

    final bookmarks = state.value ?? [];
    if (bookmarks.isEmpty) return;

    final filter = ref.read(bookmarkFilterProvider);
    // Get the last document for pagination
    // In a real implementation, you would store the DocumentSnapshot
    // For simplicity, we're just loading more without actual pagination
    ref
        .read(bookmarkListProvider.notifier)
        .loadBookmarks(section: filter, limit: 20);
  }

  void _onFilterChanged(String? section) {
    ref.read(bookmarkFilterProvider.notifier).setFilter(section);
    ref.read(bookmarkListProvider.notifier).loadBookmarks(section: section);
  }

  void _onSearchChanged(String search) {
    ref.read(bookmarkSearchProvider.notifier).setSearch(search);
    // The search is now handled in the delegate; refresh if needed for main list
    // ref.read(bookmarkListProvider.notifier).refresh();
  }

  @override
  Widget build(BuildContext context) {
    final bookmarkListState = ref.watch(bookmarkListProvider);
    final filter = ref.watch(bookmarkFilterProvider);

    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios),
          onPressed: () => context.pop(),
        ),
        title: const Text('Bookmarks'),
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () {
              // Show search functionality
              final currentBookmarks =
                  ref.read(bookmarkListProvider).value ?? [];
              showSearch(
                context: context,
                delegate: BookmarkSearchDelegate(
                  onSearchChanged: _onSearchChanged,
                  bookmarks: currentBookmarks,
                ),
              );
            },
          ),
        ],
      ),
      body: RefreshIndicator(
        key: _refreshIndicatorKey,
        onRefresh: () async {
          ref.read(bookmarkListProvider.notifier).refresh();
        },
        child: Column(
          children: [
            // Filter chips
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Wrap(
                spacing: 8.0,
                runSpacing: 4.0,
                children: [
                  FilterChip(
                    label: const Text('All'),
                    selected: filter == null,
                    onSelected: (_) => _onFilterChanged(null),
                  ),
                  FilterChip(
                    label: const Text('🎤 Pronunciation'),
                    selected: filter == 'pronunciation',
                    onSelected: (_) => _onFilterChanged('pronunciation'),
                  ),
                  FilterChip(
                    label: const Text('💬 Conversation'),
                    selected: filter == 'conversation',
                    onSelected: (_) => _onFilterChanged('conversation'),
                  ),
                  FilterChip(
                    label: const Text('🎧 Listening'),
                    selected: filter == 'listening',
                    onSelected: (_) => _onFilterChanged('listening'),
                  ),
                  FilterChip(
                    label: const Text('🎤 Speaking'),
                    selected: filter == 'speaking',
                    onSelected: (_) => _onFilterChanged('speaking'),
                  ),
                ],
              ),
            ),
            Expanded(
              child: bookmarkListState.when(
                data: (bookmarks) {
                  if (bookmarks.isEmpty) {
                    return const BookmarkEmptyState();
                  }
                  return ListView.builder(
                    controller: _scrollController,
                    itemCount: bookmarks.length,
                    itemBuilder: (context, index) {
                      final bookmark = bookmarks[index];
                      return BookmarkCard(bookmark: bookmark);
                    },
                  );
                },
                loading: () => const Center(child: CircularProgressIndicator()),
                error: (error, stack) => Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(
                        Icons.error_outline,
                        size: 64,
                        color: Colors.grey,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'Failed to load bookmarks',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        error.toString(),
                        style: Theme.of(context).textTheme.bodySmall,
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: () {
                          ref.read(bookmarkListProvider.notifier).refresh();
                        },
                        child: const Text('Retry'),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
