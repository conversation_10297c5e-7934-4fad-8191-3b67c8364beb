import 'package:flutter/material.dart';
import 'package:selfeng/features/bookmark/domain/models/bookmark.dart';

class BookmarkSearchDelegate extends SearchDelegate<String> {
  final Function(String) onSearchChanged;
  final List<Bookmark> bookmarks;

  BookmarkSearchDelegate({
    required this.onSearchChanged,
    required this.bookmarks,
  });

  @override
  List<Widget> buildActions(BuildContext context) {
    return [
      IconButton(
        icon: const Icon(Icons.clear),
        onPressed: () {
          query = '';
          onSearchChanged('');
        },
      ),
    ];
  }

  @override
  Widget buildLeading(BuildContext context) {
    return IconButton(
      icon: const Icon(Icons.arrow_back),
      onPressed: () {
        close(context, '');
      },
    );
  }

  @override
  Widget buildResults(BuildContext context) {
    final filteredBookmarks = bookmarks
        .where(
          (bookmark) =>
              bookmark.title.toLowerCase().contains(query.toLowerCase()) ||
              bookmark.section.toLowerCase().contains(query.toLowerCase()),
        )
        .toList();

    if (filteredBookmarks.isEmpty) {
      return const Center(child: Text('No results found'));
    }

    return ListView.builder(
      itemCount: filteredBookmarks.length,
      itemBuilder: (context, index) {
        final bookmark = filteredBookmarks[index];
        return ListTile(
          title: Text(bookmark.title),
          subtitle: Text(bookmark.section),
          onTap: () {
            onSearchChanged(bookmark.title);
            close(context, bookmark.title);
          },
        );
      },
    );
  }

  @override
  Widget buildSuggestions(BuildContext context) {
    final filteredBookmarks = bookmarks
        .where(
          (bookmark) =>
              bookmark.title.toLowerCase().contains(query.toLowerCase()) ||
              bookmark.section.toLowerCase().contains(query.toLowerCase()),
        )
        .toList();

    if (filteredBookmarks.isEmpty) {
      return const Center(child: Text('Start typing to search'));
    }

    return ListView.builder(
      itemCount: filteredBookmarks.length,
      itemBuilder: (context, index) {
        final bookmark = filteredBookmarks[index];
        return ListTile(
          title: Text(bookmark.title),
          subtitle: Text(bookmark.section),
          onTap: () {
            onSearchChanged(bookmark.title);
            close(context, bookmark.title);
          },
        );
      },
    );
  }
}
