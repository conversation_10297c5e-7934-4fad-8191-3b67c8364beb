import 'certificate_level_model.dart';
import '../../domain/models/certificate.dart';

class CertificateModel extends Certificate {
  CertificateModel({
    required super.id,
    required super.title,
    required super.description,
    required super.dateIssued,
    required super.certificateUrl,
    super.certificateUrlPage2,
    required super.level,
    super.scores,
    super.predicates,
  });

  factory CertificateModel.fromJson(Map<String, dynamic> json) {
    return CertificateModel(
      id: json['id'],
      title: json['title'],
      description: json['description'],
      dateIssued: DateTime.parse(json['dateIssued']),
      certificateUrl: json['certificateUrl'],
      certificateUrlPage2: json['certificateUrlPage2'],
      level: CertificateLevelModel.fromJson(json['level']),
      scores: json['scores'] != null
          ? CertificateScoresModel.fromJson(json['scores'])
          : null,
      predicates: json['predicates'] != null
          ? CertificatePredicatesModel.fromJson(json['predicates'])
          : null,
    );
  }
}

class CertificateScoresModel extends CertificateScores {
  CertificateScoresModel({
    super.pronunciation,
    super.listening,
    super.speaking,
  });

  factory CertificateScoresModel.fromJson(Map<String, dynamic> json) {
    return CertificateScoresModel(
      pronunciation: json['pronunciation']?.toDouble(),
      listening: json['listening']?.toDouble(),
      speaking: json['speaking']?.toDouble(),
    );
  }
}

class CertificatePredicatesModel extends CertificatePredicates {
  CertificatePredicatesModel({
    super.pronunciation,
    super.listening,
    super.speaking,
  });

  factory CertificatePredicatesModel.fromJson(Map<String, dynamic> json) {
    return CertificatePredicatesModel(
      pronunciation: json['pronunciation'],
      listening: json['listening'],
      speaking: json['speaking'],
    );
  }
}
