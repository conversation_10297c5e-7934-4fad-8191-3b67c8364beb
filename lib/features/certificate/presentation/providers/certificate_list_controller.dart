import 'package:collection/collection.dart';
import 'package:flutter/foundation.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../../domain/models/certificate.dart';
import '../../domain/models/certificate_level.dart';
import '../../domain/providers/certificate_provider.dart';
import '../../domain/repositories/certificate_repository.dart';
import 'state/certificate_list_state.dart';
import 'package:selfeng/services/crashlytics_service/domain/providers/crashlytics_service_provider.dart';

part 'certificate_list_controller.g.dart';

/// This controller is an [AsyncNotifier] that holds and handles certificate list state
@riverpod
class CertificateListController extends _$CertificateListController {
  late CertificateRepository certificateRepository;

  @override
  FutureOr<CertificateListState> build() {
    certificateRepository = ref.watch(certificateRepositoryProvider);
    return init();
  }

  FutureOr<CertificateListState> init() async {
    state = const AsyncLoading();

    try {
      final result = await certificateRepository.getCertificates();

      return result.fold(
        (error) {
          // Report repository error to Crashlytics
          final crashlytics = ref.read(crashlyticsServiceProvider);
          crashlytics
              .recordError(
                error,
                StackTrace.current,
                reason: 'Certificate Repository Fetch Failed',
                context: {
                  'category': 'certificate_management',
                  'operation': 'get_certificates',
                  'feature': 'certificate_list',
                  'error_type': error.runtimeType.toString(),
                  'error_message': error.message,
                },
                fatal: false,
              )
              .catchError((e) => debugPrint('Failed to report error: $e'));

          state = AsyncError(error.message, StackTrace.current);
          return const CertificateListState();
        },
        (certificates) {
          try {
            final groupedCertificates = _groupAndSortCertificates(certificates);
            final newState = CertificateListState(
              groupedCertificates: groupedCertificates,
            );
            state = AsyncData(newState);
            return newState;
          } catch (error, stackTrace) {
            // Report grouping error to Crashlytics
            final crashlytics = ref.read(crashlyticsServiceProvider);
            crashlytics
                .recordError(
                  error,
                  stackTrace,
                  reason: 'Certificate Grouping Failed',
                  context: {
                    'category': 'business_logic',
                    'feature': 'certificate_list',
                    'operation': 'group_certificates',
                    'certificates_count': certificates.length,
                    'error_type': error.runtimeType.toString(),
                  },
                  fatal: false,
                )
                .catchError(
                  (e) => debugPrint('Failed to report grouping error: $e'),
                );

            state = AsyncError(
              'Failed to process certificates',
              StackTrace.current,
            );
            return const CertificateListState();
          }
        },
      );
    } catch (error, stackTrace) {
      // Report unexpected error to Crashlytics
      final crashlytics = ref.read(crashlyticsServiceProvider);
      crashlytics
          .recordError(
            error,
            stackTrace,
            reason: 'Unexpected Error in Certificate Init',
            context: {
              'category': 'certificate_management',
              'operation': 'init',
              'feature': 'certificate_list',
              'error_type': error.runtimeType.toString(),
            },
            fatal: false,
          )
          .catchError(
            (e) => debugPrint('Failed to report unexpected error: $e'),
          );

      state = AsyncError('An unexpected error occurred', StackTrace.current);
      return const CertificateListState();
    }
  }

  /// Refresh the certificate list
  Future<void> refresh() async {
    try {
      state = const AsyncLoading();
      await init();
    } catch (error, stackTrace) {
      // Report refresh error to Crashlytics
      final crashlytics = ref.read(crashlyticsServiceProvider);
      crashlytics
          .recordError(
            error,
            stackTrace,
            reason: 'Certificate List Refresh Failed',
            context: {
              'category': 'certificate_management',
              'operation': 'refresh',
              'feature': 'certificate_list',
              'error_type': error.runtimeType.toString(),
            },
            fatal: false,
          )
          .catchError((e) => debugPrint('Failed to report refresh error: $e'));

      state = AsyncError('Failed to refresh certificates', StackTrace.current);
    }
  }

  Map<CertificateLevel, List<Certificate>> _groupAndSortCertificates(
    List<Certificate> certificates,
  ) {
    // Group certificates by their level's ID to handle potential object inequality.
    final groupedByLevelId = groupBy(certificates, (cert) => cert.level.id);

    final Map<CertificateLevel, List<Certificate>> tempGroupedCerts = {};

    // Process each group
    groupedByLevelId.forEach((levelId, certs) {
      if (certs.isNotEmpty) {
        // All certificates in this group have the same level, so we can take the level from the first certificate.
        final level = certs.first.level;

        // Sort the certificates in this group by date, newest first.
        certs.sort((a, b) => b.dateIssued.compareTo(a.dateIssued));

        tempGroupedCerts[level] = certs;
      }
    });

    return tempGroupedCerts;
  }
}
