// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'certificate_list_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, type=warning
/// This controller is an [AsyncNotifier] that holds and handles certificate list state

@ProviderFor(CertificateListController)
const certificateListControllerProvider = CertificateListControllerProvider._();

/// This controller is an [AsyncNotifier] that holds and handles certificate list state
final class CertificateListController<PERSON>rovider
    extends
        $AsyncNotifierProvider<
          CertificateListController,
          CertificateListState
        > {
  /// This controller is an [AsyncNotifier] that holds and handles certificate list state
  const CertificateListControllerProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'certificateListControllerProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$certificateListControllerHash();

  @$internal
  @override
  CertificateListController create() => CertificateListController();
}

String _$certificateListControllerHash() =>
    r'750d8b1043399b662285779a745e11aa084bd6e6';

/// This controller is an [AsyncNotifier] that holds and handles certificate list state

abstract class _$CertificateListController
    extends $AsyncNotifier<CertificateListState> {
  FutureOr<CertificateListState> build();
  @$mustCallSuper
  @override
  void runBuild() {
    final created = build();
    final ref =
        this.ref
            as $Ref<AsyncValue<CertificateListState>, CertificateListState>;
    final element =
        ref.element
            as $ClassProviderElement<
              AnyNotifier<
                AsyncValue<CertificateListState>,
                CertificateListState
              >,
              AsyncValue<CertificateListState>,
              Object?,
              Object?
            >;
    element.handleValue(ref, created);
  }
}
