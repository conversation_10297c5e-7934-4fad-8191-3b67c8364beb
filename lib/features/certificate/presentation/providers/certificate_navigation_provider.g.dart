// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'certificate_navigation_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, type=warning
/// Provider to manage certificate navigation data
/// This allows passing complex objects through GoRouter by storing them in state

@ProviderFor(CertificateNavigationController)
const certificateNavigationControllerProvider =
    CertificateNavigationControllerProvider._();

/// Provider to manage certificate navigation data
/// This allows passing complex objects through GoRouter by storing them in state
final class CertificateNavigationControllerProvider
    extends
        $NotifierProvider<
          CertificateNavigationController,
          CertificateNavigationData?
        > {
  /// Provider to manage certificate navigation data
  /// This allows passing complex objects through GoRouter by storing them in state
  const CertificateNavigationControllerProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'certificateNavigationControllerProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$certificateNavigationControllerHash();

  @$internal
  @override
  CertificateNavigationController create() => CertificateNavigationController();

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(CertificateNavigationData? value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<CertificateNavigationData?>(value),
    );
  }
}

String _$certificateNavigationControllerHash() =>
    r'fae6acdb16f17948b5fbe9b910a55878729c32e0';

/// Provider to manage certificate navigation data
/// This allows passing complex objects through GoRouter by storing them in state

abstract class _$CertificateNavigationController
    extends $Notifier<CertificateNavigationData?> {
  CertificateNavigationData? build();
  @$mustCallSuper
  @override
  void runBuild() {
    final created = build();
    final ref =
        this.ref
            as $Ref<CertificateNavigationData?, CertificateNavigationData?>;
    final element =
        ref.element
            as $ClassProviderElement<
              AnyNotifier<
                CertificateNavigationData?,
                CertificateNavigationData?
              >,
              CertificateNavigationData?,
              Object?,
              Object?
            >;
    element.handleValue(ref, created);
  }
}
