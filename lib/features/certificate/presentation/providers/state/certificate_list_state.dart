import 'package:freezed_annotation/freezed_annotation.dart';
import '../../../domain/models/certificate.dart';
import '../../../domain/models/certificate_level.dart';

part 'certificate_list_state.freezed.dart';

@freezed
sealed class CertificateListState with _$CertificateListState {
  const factory CertificateListState({
    @Default({}) Map<CertificateLevel, List<Certificate>> groupedCertificates,
  }) = _CertificateListState;
}
