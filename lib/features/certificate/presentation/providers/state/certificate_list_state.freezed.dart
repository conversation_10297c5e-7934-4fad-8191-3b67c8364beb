// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'certificate_list_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$CertificateListState {

 Map<CertificateLevel, List<Certificate>> get groupedCertificates;
/// Create a copy of CertificateListState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$CertificateListStateCopyWith<CertificateListState> get copyWith => _$CertificateListStateCopyWithImpl<CertificateListState>(this as CertificateListState, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is CertificateListState&&const DeepCollectionEquality().equals(other.groupedCertificates, groupedCertificates));
}


@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(groupedCertificates));

@override
String toString() {
  return 'CertificateListState(groupedCertificates: $groupedCertificates)';
}


}

/// @nodoc
abstract mixin class $CertificateListStateCopyWith<$Res>  {
  factory $CertificateListStateCopyWith(CertificateListState value, $Res Function(CertificateListState) _then) = _$CertificateListStateCopyWithImpl;
@useResult
$Res call({
 Map<CertificateLevel, List<Certificate>> groupedCertificates
});




}
/// @nodoc
class _$CertificateListStateCopyWithImpl<$Res>
    implements $CertificateListStateCopyWith<$Res> {
  _$CertificateListStateCopyWithImpl(this._self, this._then);

  final CertificateListState _self;
  final $Res Function(CertificateListState) _then;

/// Create a copy of CertificateListState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? groupedCertificates = null,}) {
  return _then(_self.copyWith(
groupedCertificates: null == groupedCertificates ? _self.groupedCertificates : groupedCertificates // ignore: cast_nullable_to_non_nullable
as Map<CertificateLevel, List<Certificate>>,
  ));
}

}


/// Adds pattern-matching-related methods to [CertificateListState].
extension CertificateListStatePatterns on CertificateListState {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _CertificateListState value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _CertificateListState() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _CertificateListState value)  $default,){
final _that = this;
switch (_that) {
case _CertificateListState():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _CertificateListState value)?  $default,){
final _that = this;
switch (_that) {
case _CertificateListState() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( Map<CertificateLevel, List<Certificate>> groupedCertificates)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _CertificateListState() when $default != null:
return $default(_that.groupedCertificates);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( Map<CertificateLevel, List<Certificate>> groupedCertificates)  $default,) {final _that = this;
switch (_that) {
case _CertificateListState():
return $default(_that.groupedCertificates);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( Map<CertificateLevel, List<Certificate>> groupedCertificates)?  $default,) {final _that = this;
switch (_that) {
case _CertificateListState() when $default != null:
return $default(_that.groupedCertificates);case _:
  return null;

}
}

}

/// @nodoc


class _CertificateListState implements CertificateListState {
  const _CertificateListState({final  Map<CertificateLevel, List<Certificate>> groupedCertificates = const {}}): _groupedCertificates = groupedCertificates;
  

 final  Map<CertificateLevel, List<Certificate>> _groupedCertificates;
@override@JsonKey() Map<CertificateLevel, List<Certificate>> get groupedCertificates {
  if (_groupedCertificates is EqualUnmodifiableMapView) return _groupedCertificates;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(_groupedCertificates);
}


/// Create a copy of CertificateListState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$CertificateListStateCopyWith<_CertificateListState> get copyWith => __$CertificateListStateCopyWithImpl<_CertificateListState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _CertificateListState&&const DeepCollectionEquality().equals(other._groupedCertificates, _groupedCertificates));
}


@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(_groupedCertificates));

@override
String toString() {
  return 'CertificateListState(groupedCertificates: $groupedCertificates)';
}


}

/// @nodoc
abstract mixin class _$CertificateListStateCopyWith<$Res> implements $CertificateListStateCopyWith<$Res> {
  factory _$CertificateListStateCopyWith(_CertificateListState value, $Res Function(_CertificateListState) _then) = __$CertificateListStateCopyWithImpl;
@override @useResult
$Res call({
 Map<CertificateLevel, List<Certificate>> groupedCertificates
});




}
/// @nodoc
class __$CertificateListStateCopyWithImpl<$Res>
    implements _$CertificateListStateCopyWith<$Res> {
  __$CertificateListStateCopyWithImpl(this._self, this._then);

  final _CertificateListState _self;
  final $Res Function(_CertificateListState) _then;

/// Create a copy of CertificateListState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? groupedCertificates = null,}) {
  return _then(_CertificateListState(
groupedCertificates: null == groupedCertificates ? _self._groupedCertificates : groupedCertificates // ignore: cast_nullable_to_non_nullable
as Map<CertificateLevel, List<Certificate>>,
  ));
}


}

// dart format on
