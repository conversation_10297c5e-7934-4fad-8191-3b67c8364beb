import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:selfeng/shared/helpers/navigation_helper.dart';
import 'package:selfeng/configs/routes/core_router_name.dart';
import 'package:selfeng/shared/localization/app_localizations_context.dart';
import '../providers/certificate_navigation_provider.dart';

class CertificateScreen extends ConsumerStatefulWidget {
  final String level;

  const CertificateScreen({super.key, required this.level});

  @override
  ConsumerState<CertificateScreen> createState() => _CertificateScreenState();
}

class _CertificateScreenState extends ConsumerState<CertificateScreen> {
  final Map<int, PageController> _pageControllers = {};
  final Map<int, int> _currentPages = {};

  @override
  void dispose() {
    for (final controller in _pageControllers.values) {
      controller.dispose();
    }
    super.dispose();
  }

  Widget _buildCertificateCard(dynamic certificate, int index, dynamic level) {
    final formattedDate = DateFormat(
      'd MMMM yyyy',
      Localizations.localeOf(context).toString(),
    ).format(certificate.dateIssued);
    final hasSecondPage = certificate.certificateUrlPage2 != null;

    // Initialize page controller for this certificate if not exists
    if (!_pageControllers.containsKey(index)) {
      _pageControllers[index] = PageController();
      _currentPages[index] = 0;
    }

    final pageController = _pageControllers[index]!;
    final currentPage = _currentPages[index] ?? 0;

    return Container(
      margin: const EdgeInsets.only(bottom: 20.0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.0),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(20),
            blurRadius: 20,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header section
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.blue.withAlpha(25),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(
                    Icons.workspace_premium,
                    color: Colors.blue,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        certificate.title,
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 18,
                          color: Colors.black87,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          Icon(
                            Icons.calendar_today,
                            size: 14,
                            color: Colors.grey[600],
                          ),
                          const SizedBox(width: 4),
                          Text(
                            context.loc.issued_on(formattedDate),
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontSize: 14,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),

            // Certificate preview section
            Container(
              height: 250,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12.0),
                border: Border.all(color: Colors.grey[200]!),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(12.0),
                child: Stack(
                  children: [
                    PageView(
                      controller: pageController,
                      onPageChanged: (page) {
                        setState(() {
                          _currentPages[index] = page;
                        });
                      },
                      children: [
                        _buildCertificatePage(
                          certificate.certificateUrl,
                          context.loc.page_1,
                        ),
                        if (hasSecondPage)
                          _buildCertificatePage(
                            certificate.certificateUrlPage2!,
                            context.loc.page_2,
                          ),
                      ],
                    ),

                    // Page indicators
                    if (hasSecondPage)
                      Positioned(
                        bottom: 12,
                        left: 0,
                        right: 0,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            for (int i = 0; i < (hasSecondPage ? 2 : 1); i++)
                              Container(
                                margin: const EdgeInsets.symmetric(
                                  horizontal: 4,
                                ),
                                width: 8,
                                height: 8,
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  color: currentPage == i
                                      ? Colors.blue
                                      : Colors.white.withAlpha(127),
                                ),
                              ),
                          ],
                        ),
                      ),

                    // Page navigation arrows
                    if (hasSecondPage) ...[
                      if (currentPage > 0)
                        Positioned(
                          left: 8,
                          top: 0,
                          bottom: 0,
                          child: Center(
                            child: GestureDetector(
                              onTap: () {
                                pageController.previousPage(
                                  duration: const Duration(milliseconds: 300),
                                  curve: Curves.easeInOut,
                                );
                              },
                              child: Container(
                                padding: const EdgeInsets.all(8),
                                decoration: BoxDecoration(
                                  color: Colors.black.withAlpha(127),
                                  shape: BoxShape.circle,
                                ),
                                child: const Icon(
                                  Icons.arrow_back_ios,
                                  color: Colors.white,
                                  size: 16,
                                ),
                              ),
                            ),
                          ),
                        ),
                      if (currentPage < 1)
                        Positioned(
                          right: 8,
                          top: 0,
                          bottom: 0,
                          child: Center(
                            child: GestureDetector(
                              onTap: () {
                                pageController.nextPage(
                                  duration: const Duration(milliseconds: 300),
                                  curve: Curves.easeInOut,
                                );
                              },
                              child: Container(
                                padding: const EdgeInsets.all(8),
                                decoration: BoxDecoration(
                                  color: Colors.black.withAlpha(127),
                                  shape: BoxShape.circle,
                                ),
                                child: const Icon(
                                  Icons.arrow_forward_ios,
                                  color: Colors.white,
                                  size: 16,
                                ),
                              ),
                            ),
                          ),
                        ),
                    ],
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // View Details Button
            SizedBox(
              width: double.infinity,
              height: 48,
              child: ElevatedButton.icon(
                onPressed: () {
                  // Set the selected certificate ID in the provider while preserving the full certificate list
                  ref
                      .read(certificateNavigationControllerProvider.notifier)
                      .setSelectedCertificate(certificate.id);

                  // Navigate to certificate detail screen
                  customNav(
                    context,
                    RouterName.certificateDetailScreen,
                    params: {'level': level.name},
                  );
                },
                icon: const Icon(Icons.visibility, size: 18),
                label: Text(context.loc.view_details),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue[600],
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  elevation: 2,
                ),
              ),
            ),

            const SizedBox(height: 4),
          ],
        ),
      ),
    );
  }

  Widget _buildCertificatePage(String imageUrl, String pageLabel) {
    return Container(
      width: double.infinity,
      height: double.infinity,
      color: Colors.grey[50],
      child: Stack(
        children: [
          Center(
            child: SvgPicture.network(
              imageUrl,
              fit: BoxFit.contain,
              placeholderBuilder: (context) => Center(
                child: CircularProgressIndicator(color: Colors.grey[400]),
              ),
            ),
          ),
          Positioned(
            top: 8,
            right: 8,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.black.withAlpha(178),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                pageLabel,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final certificateData = ref.watch(certificateNavigationControllerProvider);

    // If no data is available, show error or navigate back
    if (certificateData == null) {
      return Scaffold(
        appBar: AppBar(title: Text(context.loc.certificate)),
        body: Center(child: Text(context.loc.no_certificate_data_available)),
      );
    }

    final level = certificateData.level;
    final certificates = certificateData.certificates;

    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: Colors.black54),
          onPressed: () => context.pop(),
        ),
        title: Text(
          level.name,
          style: const TextStyle(
            color: Colors.black87,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: Colors.white,
        elevation: 0,
        centerTitle: true,
      ),
      body: Stack(
        children: [
          ListView.builder(
            padding: const EdgeInsets.all(16.0),
            itemCount: certificates.length,
            itemBuilder: (context, index) {
              final certificate = certificates[index];
              return _buildCertificateCard(certificate, index, level);
            },
          ),
        ],
      ),
    );
  }
}
