import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:selfeng/features/dashboard/presentation/providers/state/dashboard_state.dart';
import 'package:selfeng/features/main_lesson/domain/providers/main_lesson_provider.dart';
import 'package:selfeng/features/main_lesson/domain/repositories/main_lesson_repository.dart';
import 'package:selfeng/features/main_lesson/presentation/providers/main_lesson_provider.dart';
import 'package:selfeng/services/user_data_service/domain/providers/user_data_service_provider.dart';
import 'package:selfeng/services/user_data_service/domain/repositories/user_data_service_repository.dart';
import 'package:selfeng/shared/domain/models/user-data/user_data.dart';

import 'package:selfeng/services/crashlytics_service/utils/error_handler.dart';
import 'package:selfeng/services/crashlytics_service/domain/providers/crashlytics_service_provider.dart';

part 'dashboard_controller.g.dart';

@riverpod
class DashboardController extends _$DashboardController {
  late final UserDataServiceRepository userDataServiceRepository;
  late final MainLessonRepository mainLessonRepository;
  late final MainLessonNotifier mainLessonNotifier;
  ErrorHandler? errorHandler;

  @override
  FutureOr<DashboardState> build() {
    userDataServiceRepository = ref.watch(userDataServiceProvider);
    mainLessonRepository = ref.watch(mainLessonRepositoryProvider);
    mainLessonNotifier = ref.watch(mainLessonProvider.notifier);

    // Initialize crashlytics and error handler after build phase
    _initializeCrashlyticsAsync();

    init();
    return DashboardState();
  }

  Future<void> init() async {
    if (FirebaseAuth.instance.currentUser == null) return;

    // Log initialization asynchronously to avoid setState during build
    Future.microtask(() {
      try {
        ref.read(crashlyticsServiceProvider).log('Initializing dashboard');
      } catch (e) {
        debugPrint('Failed to log dashboard initialization: $e');
      }
    });

    // Use a fallback error handler if crashlytics isn't initialized yet
    try {
      await _loadUserData();
    } catch (e, stackTrace) {
      debugPrint('Dashboard initialization error: $e');
      // Try to report error if errorHandler is available
      if (errorHandler != null) {
        await errorHandler!.reportError(
          e,
          stackTrace,
          context: 'Dashboard Initialization',
          fatal: false,
        );
      }
    }
  }

  Future<void> _loadUserData() async {
    final result =
        await (errorHandler?.handleAsync(
              () => userDataServiceRepository.getUserData(),
              context: 'Loading User Data',
              fatal: false,
            ) ??
            userDataServiceRepository.getUserData());

    if (result == null) return;

    result.fold(
      (failure) => state = AsyncError(failure.message, StackTrace.empty),
      (data) async {
        final updatedState = state.value!.copyWith(
          lastPronunciation: data.lastPronunciation,
          lastConversation: data.lastConversation,
          lastListening: data.lastListening,
          lastSpeaking: data.lastSpeaking,
          afterTest: data.afterTest,
        );
        state = AsyncData(updatedState);

        // Set user context for crash reporting
        final crashlytics = ref.read(crashlyticsServiceProvider);
        await crashlytics.setUserId(FirebaseAuth.instance.currentUser!.uid);
        await crashlytics.setUserEmail(data.email);
        crashlytics.log('User data loaded successfully');

        final lastCourse = await _processLastCourses(data);

        if (lastCourse.isNotEmpty) {
          lastCourse.sort(
            (a, b) => b.info.accessTime.compareTo(a.info.accessTime),
          );
          state = AsyncData(updatedState.copyWith(lastCourse: lastCourse));
        } else {
          await getFirstLesson();
        }
      },
    );
  }

  Future<List<LastCourseInfo>> _processLastCourses(UserData data) async {
    final List<LastCourseInfo> lastCourse = [];
    final mainLessonNotifier = ref.watch(mainLessonProvider.notifier);

    final futures = <Future<void>>[];
    final coursesToProcess = [
      (data.lastPronunciation, mainLessonNotifier.updateLastPronunciation),
      (data.lastConversation, mainLessonNotifier.updateLastConversation),
      (data.lastListening, mainLessonNotifier.updateLastListening),
      (data.lastSpeaking, mainLessonNotifier.updateLastSpeaking),
    ];

    for (final (course, updateFunction) in coursesToProcess) {
      if (course != null) {
        futures.add(
          getLastCourseData(course).then((res) {
            if (res != null) {
              lastCourse.add(res);
              updateFunction(course);
            }
          }),
        );
      }
    }

    await Future.wait(futures);
    return lastCourse;
  }

  Future<LastCourseInfo?> getLastCourseData(LastCourse lastCourse) async {
    final result =
        await (errorHandler?.handleAsync(
              () => switch (lastCourse.section) {
                SectionType.conversation =>
                  mainLessonRepository.getConversation(path: lastCourse.path),
                SectionType.listening => mainLessonRepository.getListening(
                  path: lastCourse.path,
                ),
                SectionType.speaking => mainLessonRepository.getSpeaking(
                  path: lastCourse.path,
                ),
                SectionType.pronunciation =>
                  mainLessonRepository.getPronunciation(path: lastCourse.path),
              },
              context: 'Get Last Course Data',
              fatal: false,
            ) ??
            switch (lastCourse.section) {
              SectionType.conversation => mainLessonRepository.getConversation(
                path: lastCourse.path,
              ),
              SectionType.listening => mainLessonRepository.getListening(
                path: lastCourse.path,
              ),
              SectionType.speaking => mainLessonRepository.getSpeaking(
                path: lastCourse.path,
              ),
              SectionType.pronunciation =>
                mainLessonRepository.getPronunciation(path: lastCourse.path),
            });

    if (result == null) return null;

    return result.fold((failure) {
      state = AsyncError(failure.message, StackTrace.empty);
      return null;
    }, (data) => LastCourseInfo(info: lastCourse, data: data));
  }

  Future<void> getFirstLesson() async {
    final result =
        await (errorHandler?.handleAsync(
              () => mainLessonRepository.getFirstLesson(),
              context: 'Get First Lesson',
              fatal: false,
            ) ??
            mainLessonRepository.getFirstLesson());

    if (result == null) return;

    result.fold(
      (failure) => state = AsyncError(failure.message, StackTrace.empty),
      (data) => state = AsyncData(state.value!.copyWith(firstLesson: data)),
    );
  }

  void changeisFromLastCourse(bool fromLastCourse) {
    mainLessonNotifier.updateFromLastCourse(fromLastCourse);
  }

  /// Initialize crashlytics service asynchronously to avoid setState during build
  void _initializeCrashlyticsAsync() {
    // Use microtask to defer initialization until after build phase
    Future.microtask(() async {
      try {
        final crashlytics = ref.read(crashlyticsServiceProvider);
        errorHandler = ErrorHandler(crashlytics);

        // Set custom keys for dashboard context
        await crashlytics.setCustomKey('feature', 'dashboard');
        await crashlytics.setCustomKey('controller', 'DashboardController');
      } catch (e) {
        // Don't let crashlytics initialization errors break the app
        debugPrint(
          'Failed to initialize crashlytics in DashboardController: $e',
        );
      }
    });
  }
}
