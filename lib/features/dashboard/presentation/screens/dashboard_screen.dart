import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:selfeng/features/dashboard/presentation/providers/state/dashboard_state.dart';
import 'package:selfeng/features/dashboard/presentation/widgets/home_page.dart';
import 'package:selfeng/features/games/presentation/screens/games_screen.dart';
import 'package:selfeng/features/library/presentation/screens/library_screen.dart';
import 'package:selfeng/features/search/presentation/screens/search_screen.dart';
import 'package:selfeng/features/setting/presentation/providers/setting_controller.dart';
import 'package:selfeng/shared/localization/app_localizations_context.dart';
import 'package:selfeng/features/dashboard/presentation/providers/dashboard_controller.dart';
import 'package:selfeng/services/in_app_update_service/presentation/widgets/install_update_prompt.dart';
// import 'package:selfeng/features/games/presentation/screens/games_screen.dart';

class DashboardScreen extends ConsumerStatefulWidget {
  static const String routeName = 'DashboardScreen';

  const DashboardScreen({super.key});

  @override
  ConsumerState<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends ConsumerState<DashboardScreen> {
  late AsyncValue<DashboardState> dashboardState;
  late DashboardController dashboardController;
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.maxScrollExtent ==
        _scrollController.offset) {
      // Handle scroll end logic here
    }
  }

  @override
  Widget build(BuildContext context) {
    dashboardState = ref.watch(
      dashboardControllerProvider.select((state) => state),
    );
    dashboardController = ref.read(dashboardControllerProvider.notifier);
    final currentIndex = ref.watch(
      settingControllerProvider.select((state) => state.value?.tabIndex ?? 0),
    );

    return Scaffold(
      backgroundColor: Colors.white,
      bottomNavigationBar: const DashboardBottomNavBar(),
      body: RefreshIndicator(
        onRefresh: () => dashboardController.init(),
        child: _buildBody(currentIndex),
      ),
    );
  }

  Widget _buildBody(int currentIndex) {
    return Column(
      children: [
        // Global install update prompt - visible across all tabs
        const InstallUpdatePrompt(),

        // Main content
        Expanded(
          child: Stack(
            children: [
              Offstage(offstage: currentIndex != 0, child: const HomePage()),
              Offstage(
                offstage: currentIndex != 1,
                child: const LibraryScreen(),
              ),
              Offstage(offstage: currentIndex != 2, child: const GamesScreen()),
              Offstage(
                offstage: currentIndex != 3,
                child: const SearchScreen(),
              ),
            ],
          ),
        ),
      ],
    );
  }
}

class DashboardBottomNavBar extends ConsumerWidget {
  const DashboardBottomNavBar({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentIndex = ref.watch(
      settingControllerProvider.select((state) => state.value?.tabIndex ?? 0),
    );
    final settingController = ref.watch(settingControllerProvider.notifier);

    return BottomNavigationBar(
      currentIndex: currentIndex,
      selectedItemColor: Colors.red,
      type: BottomNavigationBarType.fixed,
      onTap: settingController.selectTab,
      items: [
        _buildNavBarItem(Icons.home_filled, context.loc.home),
        _buildNavBarItem(Icons.local_library, context.loc.library),
        _buildNavBarItem(Icons.sports_esports, context.loc.games),
        _buildNavBarItem(Icons.search, context.loc.search),
      ],
    );
  }

  BottomNavigationBarItem _buildNavBarItem(IconData icon, String label) {
    return BottomNavigationBarItem(icon: Icon(icon, size: 30), label: label);
  }
}
