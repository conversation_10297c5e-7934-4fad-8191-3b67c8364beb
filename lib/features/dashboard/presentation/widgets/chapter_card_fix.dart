import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:selfeng/shared/domain/models/level.dart';
import 'package:selfeng/shared/widgets/loading_circle.dart';

class ChapterCardFix extends StatelessWidget {
  final int chapter;
  final String title;
  final String imageUrl;
  final Level level;
  final String route;
  final VoidCallback onTap;

  const ChapterCardFix({
    required this.chapter,
    required this.title,
    required this.imageUrl,
    required this.level,
    required this.route,
    required this.onTap,
    super.key,
  });

  // --- Constants copied from ChapterCard for visual consistency ---
  static const _shadowStyle = Shadow(
    offset: Offset(2.0, 2.0),
    blurRadius: 5.0,
    color: Color.fromARGB(255, 0, 0, 0),
  );

  static const _textStyle = TextStyle(
    fontWeight: FontWeight.bold,
    color: Colors.white,
    shadows: [_shadowStyle],
  );

  static const _boxShadow = BoxShadow(
    color: Color.fromARGB(64, 189, 186, 186),
    spreadRadius: 0,
    blurRadius: 10.3,
    offset: Offset(0, 1),
  );

  static const _padding = EdgeInsets.all(8.0);
  static const _titlePadding = EdgeInsets.only(left: 10, right: 10, top: 14);
  static const _footerPadding = EdgeInsets.only(
    left: 10,
    right: 10,
    bottom: 14,
  );
  static const _containerSize = Size(240, 180);
  static const _borderRadius = BorderRadius.all(Radius.circular(10));
  static const _iconSize = 16.0;
  static const _titleSpacing = SizedBox(height: 8);
  // --- End of copied constants ---

  @override
  Widget build(BuildContext context) {
    // Use RepaintBoundary for performance optimization
    return RepaintBoundary(
      // Apply the outer padding
      child: Padding(
        padding: _padding,
        // Container for applying the box shadow
        child: Container(
          decoration: const BoxDecoration(
            // Transparent background as the image will provide color
            color: Colors.transparent,
            borderRadius: _borderRadius, // Match radius for shadow shape
            boxShadow: [_boxShadow],
          ),
          // InkWell for tap interaction, placed inside shadow container
          child: InkWell(
            onTap: onTap,
            borderRadius:
                _borderRadius, // Ensure ripple effect respects border radius
            // ClipRRect to enforce rounded corners on the content (Stack)
            child: ClipRRect(
              borderRadius: _borderRadius,
              // SizedBox to constrain the size of the card
              child: SizedBox(
                width: _containerSize.width,
                height: _containerSize.height,
                // Stack to layer the image and the text content
                child: Stack(
                  fit: StackFit.expand, // Ensure children fill the Stack
                  children: [
                    // --- Background Image ---
                    CachedNetworkImage(
                      imageUrl: imageUrl,
                      width: _containerSize.width,
                      // height: _containerSize.height,
                      memCacheWidth: _containerSize.width.toInt(),
                      // memCacheHeight: _containerSize.height.toInt(),
                      fit: BoxFit
                          .cover, // Cover ensures the image fills the bounds
                      // Placeholder while loading
                      placeholder: (context, url) => Container(
                        color: Colors.grey[300], // Placeholder background
                        child: const Center(child: LoadingCircle()),
                      ),
                      // Error widget if image fails to load
                      errorWidget: (context, url, error) => Container(
                        color: Colors.grey[300], // Error background
                        child: const Center(child: Icon(Icons.error)),
                      ),
                    ),
                    // --- Darkening Overlay ---
                    Container(
                      decoration: BoxDecoration(
                        color: Colors.black.withValues(
                          alpha: 0.3,
                        ), // Apply darkening effect
                        borderRadius: BorderRadius.circular(
                          16.0,
                        ), // Match clipping
                      ),
                    ),
                    // --- Foreground Content (Title and Footer) ---
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [_buildTitle(), _buildFooter()],
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  // --- Helper methods copied from ChapterCard ---
  Widget _buildTitle() {
    return Container(
      padding: _titlePadding,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _titleSpacing,
          Text(title, style: _textStyle.copyWith(fontSize: 20)),
        ],
      ),
    );
  }

  Widget _buildFooter() {
    return Container(
      padding: _footerPadding,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          _buildInfoRow(Icons.access_time, level.name),
          _buildInfoRow(Icons.book, 'Chapter $chapter'),
        ],
      ),
    );
  }

  Widget _buildInfoRow(IconData icon, String text) {
    return Row(
      children: [
        Icon(
          icon,
          size: _iconSize,
          color: Colors.white,
          shadows: const [_shadowStyle],
        ),
        const SizedBox(width: 4), // Spacing between icon and text
        Text(text, style: _textStyle),
      ],
    );
  }

  // --- End of copied helper methods ---
}
