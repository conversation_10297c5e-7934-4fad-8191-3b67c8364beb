import 'package:flutter/material.dart';
import 'package:selfeng/configs/routes/core_router_name.dart';
import 'package:selfeng/features/dashboard/presentation/providers/learning_material_controller.dart';
import 'package:selfeng/features/dashboard/presentation/widgets/chapter_card_fix.dart';
import 'package:selfeng/shared/domain/models/level.dart';
import 'package:selfeng/shared/helpers/navigation_helper.dart';
import 'package:selfeng/shared/localization/app_localizations_context.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class LearningMaterialsSection extends ConsumerWidget {
  const LearningMaterialsSection({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final materialState = ref.watch(learningMaterialControllerProvider);

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
            child: Text(
              context.loc.learning_material,
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.w700),
              softWrap: true,
            ),
          ),
          materialState.when(
            data: (state) => Column(
              children: [
                SizedBox(
                  height: 200,
                  child: ListView.builder(
                    scrollDirection: Axis.horizontal,
                    itemCount: state.chapters.length,
                    padding: EdgeInsets.zero,
                    addAutomaticKeepAlives: false,
                    addRepaintBoundaries: false,
                    itemBuilder: (context, index) {
                      final chapter = state.chapters[index];
                      return ChapterCardFix(
                        key: ValueKey('fix_${chapter.chapter}'),
                        chapter: chapter.chapter,
                        title: chapter.label,
                        imageUrl: chapter.imageUrl,
                        level: state
                            .fetchedLevel, // Use the fetchedLevel from state
                        route: RouterName.chapterTitle,
                        onTap: () => _navigateToChapter(
                          context,
                          ref,
                          chapter,
                          state.fetchedLevel,
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
            loading: () => const Center(child: CircularProgressIndicator()),
            error: (error, _) => Center(child: Text(error.toString())),
          ),
        ],
      ),
    );
  }

  void _navigateToChapter(
    BuildContext context,
    WidgetRef ref,
    dynamic chapter,
    Level fetchedLevel, // Add fetchedLevel parameter
  ) {
    ref
        .read(learningMaterialControllerProvider.notifier)
        .changeisFromLastCourse(false);

    customNav(
      context,
      RouterName.chapterTitle,
      params: {
        'level':
            fetchedLevel.name, // Use fetchedLevel instead of hardcoded Level.a1
        'chapter': chapter.chapter.toString(),
      },
    );
  }
}
