import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:selfeng/configs/routes/core_router_name.dart';
import 'package:selfeng/shared/helpers/navigation_helper.dart';
import 'package:selfeng/shared/globals.dart';

class ProfileAvatar extends StatelessWidget {
  const ProfileAvatar({super.key});

  @override
  Widget build(BuildContext context) {
    final user = FirebaseAuth.instance.currentUser;
    return GestureDetector(
      onTap: () => customNav(context, RouterName.profilesettingScreen),
      child: CachedNetworkImage(
        imageUrl: user?.photoURL ?? '',
        placeholder: (context, url) => const CircularProgressIndicator(),
        errorWidget: (context, url, error) => const Icon(Icons.error),
        imageBuilder: (context, imageProvider) => CircleAvatar(
          backgroundImage: const AssetImage(
            '$assetImageDashboard/3d_avatar_30.png',
          ),
          foregroundImage: imageProvider,
        ),
      ),
    );
  }
}
