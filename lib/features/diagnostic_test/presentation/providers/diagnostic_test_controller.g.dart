// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'diagnostic_test_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, type=warning
/// This controller is an [AsyncNotifier] that holds and handles our diagnostic test state

@ProviderFor(DiagnosticTestController)
const diagnosticTestControllerProvider = DiagnosticTestControllerProvider._();

/// This controller is an [AsyncNotifier] that holds and handles our diagnostic test state
final class DiagnosticTestControllerProvider
    extends
        $AsyncNotifierProvider<DiagnosticTestController, DiagnosticTestState> {
  /// This controller is an [AsyncNotifier] that holds and handles our diagnostic test state
  const DiagnosticTestControllerProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'diagnosticTestControllerProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$diagnosticTestControllerHash();

  @$internal
  @override
  DiagnosticTestController create() => DiagnosticTestController();
}

String _$diagnosticTestControllerHash() =>
    r'3a9c3c296fc232bac6d75f37517380d197adb17a';

/// This controller is an [AsyncNotifier] that holds and handles our diagnostic test state

abstract class _$DiagnosticTestController
    extends $AsyncNotifier<DiagnosticTestState> {
  FutureOr<DiagnosticTestState> build();
  @$mustCallSuper
  @override
  void runBuild() {
    final created = build();
    final ref =
        this.ref as $Ref<AsyncValue<DiagnosticTestState>, DiagnosticTestState>;
    final element =
        ref.element
            as $ClassProviderElement<
              AnyNotifier<AsyncValue<DiagnosticTestState>, DiagnosticTestState>,
              AsyncValue<DiagnosticTestState>,
              Object?,
              Object?
            >;
    element.handleValue(ref, created);
  }
}
