import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:selfeng/configs/routes/core_router_name.dart';
import 'package:selfeng/shared/domain/models/default_model/default_model.dart';
import 'package:selfeng/shared/helpers/navigation_helper.dart';
import 'package:selfeng/shared/localization/app_localizations_context.dart';
import 'package:selfeng/shared/globals.dart';

class DiagnosticTestOnboardingScreen extends ConsumerStatefulWidget {
  const DiagnosticTestOnboardingScreen({super.key});

  @override
  ConsumerState<DiagnosticTestOnboardingScreen> createState() =>
      _DiagnosticTestOnboardingScreenState();
}

class _DiagnosticTestOnboardingScreenState
    extends ConsumerState<DiagnosticTestOnboardingScreen>
    with TickerProviderStateMixin {
  late final TabController _tabController;
  late int _index;
  late Size _size;
  late List<DefaultModel> _listImage;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this)
      ..addListener(() {
        setState(() {
          _index = _tabController.index;
        });
      });
    _index = 0;
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void init() {
    _size = MediaQuery.of(context).size;

    _listImage = [
      DefaultModel(
        title: context.loc.questionnaireOnboarding1,
        image: '$assetImageDiagnosticTest/Frame 92.png',
      ),
      DefaultModel(
        title: context.loc.questionnaireOnboarding2,
        image: '$assetImageDiagnosticTest/Frame 93.png',
      ),
      DefaultModel(
        title: context.loc.questionnaireOnboarding3,
        image: '$assetImageDiagnosticTest/Frame 94.png',
      ),
    ];
  }

  @override
  Widget build(BuildContext context) {
    init();
    return Scaffold(
      body: Stack(
        children: [
          TabBarView(
            controller: _tabController,
            children: _listImage
                .map<Widget>((item) => _pageSlide(item))
                .toList(),
          ),
          Column(
            children: [
              const SizedBox(height: 52),
              Container(
                padding: const EdgeInsets.symmetric(
                  vertical: 32,
                  horizontal: 16,
                ),
                child: LinearProgressIndicator(
                  value: (_tabController.index + 1) / _tabController.length,
                  backgroundColor: Theme.of(context).primaryColor,
                  valueColor: const AlwaysStoppedAnimation(Color(0xffFFDAD2)),
                ),
              ),
            ],
          ),
          Positioned(
            bottom: 72,
            child: Column(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  width: _size.width,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      _navigationButton(
                        onTap: () async {
                          if (_index != 0) {
                            setState(() {
                              _index--;
                            });
                            _tabController.animateTo(_index);
                          }
                        },
                        icon: Icons.chevron_left_rounded,
                      ),
                      _navigationButton(
                        onTap: () async {
                          if (_index < _listImage.length) {
                            setState(() {
                              _index++;
                            });
                            _tabController.animateTo(_index);
                          }
                        },
                        icon: Icons.chevron_right_rounded,
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 36),
                Visibility(
                  visible: _index == 2,
                  child: InkWell(
                    onTap: () async {
                      customNav(
                        context,
                        isReplace: true,
                        RouterName.diagnosticTestInstScreen,
                      );
                    },
                    child: Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(16),
                        gradient: const LinearGradient(
                          colors: [Color(0xff802115), Color(0xffEB1A19)],
                          begin: Alignment.centerLeft,
                          end: Alignment.centerRight,
                        ),
                        border: Border.all(
                          color: const Color(0xffFFDAD6),
                          width: 2,
                        ),
                      ),
                      width: _size.width - 40,
                      padding: const EdgeInsets.symmetric(vertical: 11),
                      margin: const EdgeInsets.symmetric(horizontal: 16),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            softWrap: true,
                            context.loc.start,
                            style: Theme.of(context).textTheme.headlineSmall
                                ?.copyWith(color: Colors.white),
                          ),
                          const SizedBox(width: 8),
                          const Icon(
                            Icons.arrow_circle_right_outlined,
                            color: Colors.white,
                            size: 26,
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  InkWell _navigationButton({onTap, icon}) => InkWell(
    onTap: onTap,
    child: Container(
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: const Color(0xffFFB3AC).withValues(alpha: .5),
        border: Border.all(
          color: Colors.white.withValues(alpha: .5),
          width: 1.6,
        ),
      ),
      height: 50,
      width: 50,
      child: Icon(icon, size: 46, color: Colors.black.withValues(alpha: .5)),
    ),
  );

  Container _pageSlide(DefaultModel item) => Container(
    decoration: BoxDecoration(
      gradient: LinearGradient(
        colors: _index == 1
            ? [
                const Color(0xffFE754C),
                const Color(0xffE21F29),
                const Color(0xffC3151F),
              ]
            : [
                const Color(0xffC3151F),
                const Color(0xffE21F29),
                const Color(0xffFE754C),
              ],
        begin: Alignment.bottomRight,
        end: Alignment.topLeft,
      ),
    ),
    child: Stack(
      children: [
        Align(
          alignment: FractionalOffset.bottomCenter,
          child: Image.asset(
            item.image,
            fit: BoxFit.fitWidth,
            width: _size.width,
          ),
        ),
        Positioned(
          top: 108,
          left: 16,
          child: SizedBox(
            width: MediaQuery.of(context).size.width - 32,
            child: Wrap(
              children: [
                Text(
                  softWrap: true,
                  item.title,
                  style: Theme.of(
                    context,
                  ).textTheme.titleLarge?.copyWith(color: Colors.white),
                ),
              ],
            ),
          ),
        ),
      ],
    ),
  );
}
