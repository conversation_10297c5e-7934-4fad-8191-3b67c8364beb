import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:selfeng/configs/routes/core_router_name.dart';
import 'package:selfeng/features/diagnostic_test/presentation/providers/diagnostic_test_controller.dart';
import 'package:selfeng/features/diagnostic_test/presentation/providers/state/diagnostic_test_state.dart';
import 'package:selfeng/features/setting/presentation/providers/setting_controller.dart';
import 'package:selfeng/features/setting/presentation/providers/state/setting_state.dart';
import 'package:selfeng/shared/globals.dart';
import 'package:selfeng/shared/helpers/navigation_helper.dart';
import 'package:selfeng/shared/localization/app_localizations_context.dart';
import 'package:selfeng/shared/widgets/widgets.dart';

class DiagnosticTestResultScreen extends ConsumerStatefulWidget {
  const DiagnosticTestResultScreen({super.key});

  @override
  ConsumerState<DiagnosticTestResultScreen> createState() =>
      _DiagnosticTestResultScreenState();
}

class _DiagnosticTestResultScreenState
    extends ConsumerState<DiagnosticTestResultScreen> {
  late AsyncValue<DiagnosticTestState> viewState;
  late DiagnosticTestController viewModel;
  late AsyncValue<SettingState> settingState;

  @override
  Widget build(BuildContext context) {
    viewState = ref.watch(diagnosticTestControllerProvider);
    viewModel = ref.watch(diagnosticTestControllerProvider.notifier);
    settingState = ref.watch(settingControllerProvider);

    ref.listen(diagnosticTestControllerProvider.select((value) => value), ((
      previous,
      next,
    ) {
      //show Snackbar on failure
      next.maybeWhen(
        error: (error, track) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(SnackBar(content: Text(error.toString())));
        },
        orElse: () {},
      );
    }));

    return switch (viewState) {
      AsyncData() => Scaffold(
        body: Container(
          child: Stack(
            children: [
              ListView(
                children: [
                  const SizedBox(height: 108),
                  Image.asset(
                    '$assetImageDiagnosticTest/BG.png',
                    width: 254,
                    height: 254,
                  ),
                  const SizedBox(height: 20),
                  Text(
                    textAlign: TextAlign.center,
                    context.loc.finallyResult,
                    style: Theme.of(context).textTheme.bodyLarge,
                  ),
                  const SizedBox(height: 40),
                  Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: const Color(0xffE82329),
                        width: 0.4,
                      ),
                      color: Colors.white,
                    ),
                    margin: const EdgeInsets.symmetric(horizontal: 16),
                    child: Column(
                      children: [
                        Container(
                          padding: const EdgeInsets.symmetric(
                            vertical: 20,
                            horizontal: 16,
                          ),
                          child: Column(
                            children: [
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    context.loc.point,
                                    style: Theme.of(
                                      context,
                                    ).textTheme.bodyMedium,
                                  ),
                                  Text(
                                    '${viewState.value!.correctCount}',
                                    style: Theme.of(
                                      context,
                                    ).textTheme.bodyMedium,
                                  ),
                                ],
                              ),
                              const SizedBox(height: 16),
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    context.loc.level,
                                    style: Theme.of(
                                      context,
                                    ).textTheme.bodyMedium,
                                  ),
                                  Text(
                                    viewState.value!.result?.level?[settingState
                                            .value!
                                            .getLanguageCode] ??
                                        '',
                                    style: Theme.of(
                                      context,
                                    ).textTheme.bodyMedium,
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                        InkWell(
                          onTap: () => viewModel.expandResult(),
                          child: Container(
                            decoration: BoxDecoration(
                              gradient: const LinearGradient(
                                colors: [Color(0xff682000), Color(0xff490206)],
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                              ),
                              borderRadius: viewState.value!.expandedResult
                                  ? null
                                  : const BorderRadius.only(
                                      bottomLeft: Radius.circular(8),
                                      bottomRight: Radius.circular(8),
                                    ),
                            ),
                            padding: const EdgeInsets.symmetric(
                              vertical: 8,
                              horizontal: 16,
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  context.loc.information,
                                  style: Theme.of(context).textTheme.bodyMedium
                                      ?.copyWith(color: Colors.white),
                                ),
                                Row(
                                  children: [
                                    // Text(
                                    //   viewState.value.result.level[settingState.value.getLanguageCode],
                                    //   style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: Colors.white),
                                    // ),
                                    // const SizedBox(width: 4,),
                                    Icon(
                                      viewState.value!.expandedResult
                                          ? Icons.expand_less
                                          : Icons.expand_more_rounded,
                                      color: Colors.white,
                                      size: 20,
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ),
                        if (viewState.value!.expandedResult)
                          Container(
                            padding: const EdgeInsets.symmetric(
                              vertical: 8,
                              horizontal: 16,
                            ),
                            child: Text(
                              viewState.value!.result?.description?[settingState
                                      .value!
                                      .getLanguageCode] ??
                                  '',
                              style: Theme.of(context).textTheme.titleSmall
                                  ?.copyWith(color: const Color(0xff8A1C00)),
                            ),
                          ),
                      ],
                    ),
                  ),
                ],
              ),
              Align(
                alignment: Alignment.bottomCenter,
                child: Container(
                  padding: EdgeInsets.symmetric(vertical: 32, horizontal: 16),
                  child: VButtonGradient(
                    title: context.loc.continue1,
                    onTap: () {
                      customNav(
                        context,
                        RouterName.diagnosticTestCongratulationScreen,
                        isReplace: true,
                      );
                    },
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
      AsyncError(:final error) => Text('error: $error'),
      AsyncLoading() => AppLoading(),
      _ => const Text('loading'),
    };
  }
}
