import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:selfeng/configs/routes/core_router_name.dart';
import 'package:selfeng/features/diagnostic_test/presentation/providers/diagnostic_test_controller.dart';
import 'package:selfeng/features/diagnostic_test/presentation/providers/state/diagnostic_test_state.dart';
import 'package:selfeng/features/diagnostic_test/presentation/widgets/select_answer.dart';
import 'package:selfeng/services/timer_cache_service/presentation/providers/timer_controller.dart';
import 'package:selfeng/shared/globals.dart';
import 'package:selfeng/shared/helpers/navigation_helper.dart';
import 'package:selfeng/shared/localization/app_localizations_context.dart';
import 'package:selfeng/shared/widgets/loading_circle.dart';
import 'package:selfeng/shared/widgets/widgets.dart';

class DiagnosticTestInstructionScreen extends ConsumerStatefulWidget {
  const DiagnosticTestInstructionScreen({super.key});

  @override
  ConsumerState<DiagnosticTestInstructionScreen> createState() =>
      _DiagnosticTestInstructionScreenState();
}

class _DiagnosticTestInstructionScreenState
    extends ConsumerState<DiagnosticTestInstructionScreen> {
  late AsyncValue<DiagnosticTestState> viewState;
  late DiagnosticTestController viewModel;
  late AsyncValue viewTimerState;
  late TimerController viewTimerModel;
  late Size _size;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) => _ready());
  }

  @override
  Widget build(BuildContext context) {
    _initializeProviders();
    _size = MediaQuery.of(context).size;

    _listenToStateChanges();

    return switch (viewState) {
      AsyncData() => _buildMainScaffold(),
      AsyncError(:final error) => Text('error: $error'),
      AsyncLoading() => const AppLoading(),
      _ => const Text('loading'),
    };
  }

  void _initializeProviders() {
    viewState = ref.watch(diagnosticTestControllerProvider);
    viewModel = ref.watch(diagnosticTestControllerProvider.notifier);
    viewTimerState = ref.watch(timerControllerProvider);
    viewTimerModel = ref.watch(timerControllerProvider.notifier);
  }

  void _listenToStateChanges() {
    ref.listen(diagnosticTestControllerProvider.select((value) => value), ((
      previous,
      next,
    ) {
      next.maybeWhen(
        error: (error, track) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(SnackBar(content: Text(error.toString())));
        },
        orElse: () {},
      );
    }));
  }

  Scaffold _buildMainScaffold() {
    return Scaffold(
      body: viewTimerState.value.totalSecond == 0 ? _timesUp() : _questions(),
    );
  }

  Widget _timesUp() {
    return Scaffold(
      body: Stack(
        children: [
          ListView(
            children: [
              const SizedBox(height: 226),
              Center(
                child: Image.asset(
                  '$assetImageDiagnosticTest/Belum selesai1.png',
                ),
              ),
              const SizedBox(height: 20),
              _buildTimesUpText(),
            ],
          ),
          Positioned(
            bottom: 68,
            left: 0,
            right: 0,
            child: Center(
              child: VButtonGradient(
                title: context.loc.continue1,
                onTap: () {
                  viewModel.saveAnswer();
                  customNav(
                    context,
                    RouterName.diagnosticTestResultScreen,
                    isReplace: true,
                  );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTimesUpText() => Container(
    margin: const EdgeInsets.symmetric(horizontal: 78),
    child: RichText(
      textAlign: TextAlign.center,
      text: TextSpan(
        text: context.loc.timesUpStudyGroup1,
        style: Theme.of(context).textTheme.bodyLarge,
        children: [
          TextSpan(
            text: context.loc.timesUpStudyGroup2,
            style: Theme.of(
              context,
            ).textTheme.bodyLarge?.copyWith(fontWeight: FontWeight.w900),
          ),
          TextSpan(
            text: context.loc.timesUpStudyGroup3,
            style: Theme.of(context).textTheme.bodyLarge,
          ),
        ],
      ),
    ),
  );

  Widget _questions() => Stack(
    children: [
      ListView(
        children: [
          const SizedBox(height: 24),
          _buildHeader(),
          const SizedBox(height: 24),
          _buildQuestionsList(),
          const SizedBox(height: 20),
          _buildNavigationButtons(),
        ],
      ),
    ],
  );

  Widget _buildHeader() => Container(
    padding: const EdgeInsets.symmetric(horizontal: 16),
    child: Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          context.loc.diagnosticTests,
          style: Theme.of(context).textTheme.titleLarge,
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 24),
        _buildTimer(),
      ],
    ),
  );

  Widget _buildTimer() => Container(
    decoration: BoxDecoration(
      borderRadius: BorderRadius.circular(18),
      color: const Color(0xffA90013),
    ),
    padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
    child: Row(
      children: [
        Image.asset(
          '$assetImageDiagnosticTest/Time Remaining.png',
          fit: BoxFit.fitWidth,
          width: 28,
          height: 32,
        ),
        const SizedBox(width: 4),
        Text(
          '${viewTimerModel.getMinute().toString().padLeft(2, '0')}:${viewTimerModel.getSecond().toString().padLeft(2, '0')}',
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.w800,
          ),
        ),
      ],
    ),
  );

  Widget _buildQuestionsList() => Container(
    height: 632,
    margin: const EdgeInsets.symmetric(horizontal: 8),
    child: ListView.builder(
      primary: false,
      physics: const NeverScrollableScrollPhysics(),
      controller: viewState.value!.controllerScroll,
      itemCount: viewState.value!.questions.length,
      scrollDirection: Axis.horizontal,
      itemBuilder: (context, index) => _buildQuestionCard(index),
    ),
  );

  Widget _buildQuestionCard(int index) => Container(
    decoration: BoxDecoration(
      borderRadius: BorderRadius.circular(8),
      border: Border.all(color: Color(0xffFFB3AC), width: 0.6),
      color: Colors.white,
    ),
    width: _size.width - 32,
    padding: const EdgeInsets.symmetric(vertical: 20, horizontal: 24),
    margin: const EdgeInsets.symmetric(horizontal: 4),
    child: Column(
      children: [
        Text(
          viewState.value!.questions[index].question,
          style: Theme.of(context).textTheme.titleMedium,
        ),
        const SizedBox(height: 20),
        Expanded(child: _buildAnswersList(index)),
        _buildQuestionImage(index),
      ],
    ),
  );

  Widget _buildAnswersList(int questionIndex) => ListView.builder(
    itemCount: viewState.value!.questions[questionIndex].choices.length,
    itemBuilder: (context, choiceIndex) => Padding(
      padding: const EdgeInsets.only(bottom: 24),
      child: SelectAnswer(
        title:
            viewState.value!.questions[questionIndex].choices[choiceIndex].text,
        number: viewState
            .value!
            .questions[questionIndex]
            .choices[choiceIndex]
            .value,
        value: viewState.value!.questions[questionIndex].choices[choiceIndex],
        groupValue: viewState.value!.questions[questionIndex].answer,
        onTap: (val) => viewModel.selectAnser(index: questionIndex, value: val),
      ),
    ),
  );

  Widget _buildQuestionImage(int index) => CachedNetworkImage(
    imageUrl: viewState.value!.questions[index].image,
    placeholder: (context, url) => const LoadingCircle(),
    errorWidget: (context, url, error) => const Icon(Icons.error),
  );

  Widget _buildNavigationButtons() => RepeatNextButton(
    onTapRepeat: () async {
      await viewModel.backPage(_size.width);
    },
    onTapNext: () async {
      if (viewState.value!.questions[viewState.value!.currentPage].answer !=
          null) {
        await viewModel.nextPage(context, _size.width);
        if (viewState.value!.currentPage == viewState.value!.questionLength) {
          _finished();
        }
      }
    },
    leftTitle: context.loc.back,
    leftActive: viewState.value!.currentPage != 0,
    rightActive:
        viewState.value!.questions[viewState.value!.currentPage].answer != null,
  );

  Future<void> _ready() async {
    viewTimerModel.setTotalSecond(minutes: 15);
    return showModalBottomSheet(
      context: context,
      isDismissible: false,
      builder: (context) => _buildReadyDialog(),
    );
  }

  Widget _buildReadyDialog() => Container(
    height: 358,
    decoration: const BoxDecoration(
      borderRadius: BorderRadius.vertical(top: Radius.circular(10)),
      color: Colors.white,
    ),
    padding: const EdgeInsets.symmetric(vertical: 32, horizontal: 16),
    child: Column(
      children: [
        const SizedBox(height: 36),
        Text(
          context.loc.areYouReady,
          style: Theme.of(context).textTheme.titleLarge,
        ),
        const SizedBox(height: 28),
        VButtonGradient(
          title: context.loc.yes,
          onTap: () {
            viewModel.setTimer();
            context.pop();
          },
        ),
      ],
    ),
  );

  Future<void> _finished() {
    return showModalBottomSheet(
      context: context,
      builder: (context) => _buildFinishedDialog(),
    );
  }

  Widget _buildFinishedDialog() => Container(
    height: 358,
    decoration: const BoxDecoration(
      borderRadius: BorderRadius.vertical(top: Radius.circular(10)),
      color: Colors.white,
    ),
    padding: const EdgeInsets.symmetric(vertical: 32, horizontal: 16),
    child: Column(
      children: [
        const SizedBox(height: 36),
        Text(
          context.loc.areYouSure,
          style: Theme.of(context).textTheme.titleLarge,
        ),
        const SizedBox(height: 28),
        VButtonGradient(
          title: context.loc.yes,
          onTap: () {
            viewModel.saveAnswer();
            viewTimerModel.stopTimer();
            customNav(
              context,
              RouterName.diagnosticTestResultScreen,
              isReplace: true,
            );
          },
        ),
        const SizedBox(height: 23),
        InkWell(
          onTap: () => context.pop(),
          child: Text(
            context.loc.no,
            style: Theme.of(context).textTheme.titleMedium,
          ),
        ),
      ],
    ),
  );
}
