part of 'package:selfeng/configs/routes/core_routes.dart';

@TypedGoRoute<DiagnosticTestInstructionRoute>(
  path: '/diagnostic-test/instruction',
  name: RouterName.diagnosticTestInstScreen,
)
class DiagnosticTestInstructionRoute extends GoRouteData
    with $DiagnosticTestInstructionRoute {
  const DiagnosticTestInstructionRoute();
  @override
  Widget build(BuildContext context, GoRouterState state) {
    return DeferredRoute(
      diagnostic_test_inst.loadLibrary,
      () => diagnostic_test_inst.DiagnosticTestInstructionScreen(),
    );
  }
}

@TypedGoRoute<DiagnosticTestRoute>(
  path: '/diagnostic-test',
  name: RouterName.diagnosticTestScreen,
)
class DiagnosticTestRoute extends GoRouteData with $DiagnosticTestRoute {
  const DiagnosticTestRoute();
  @override
  Widget build(BuildContext context, GoRouterState state) {
    return DeferredRoute(
      diagnostic_test.loadLibrary,
      () => diagnostic_test.DiagnosticTestInstructionScreen(),
    );
  }
}

@TypedGoRoute<DiagnosticTestResultRoute>(
  path: '/diagnostic-test-result',
  name: RouterName.diagnosticTestResultScreen,
)
class DiagnosticTestResultRoute extends GoRouteData
    with $DiagnosticTestResultRoute {
  const DiagnosticTestResultRoute();
  @override
  Widget build(BuildContext context, GoRouterState state) {
    return DeferredRoute(
      diagnostic_test_result.loadLibrary,
      () => diagnostic_test_result.DiagnosticTestResultScreen(),
    );
  }
}

@TypedGoRoute<DiagnosticTestCongratulationRoute>(
  path: '/diagnostic-test-congratulation',
  name: RouterName.diagnosticTestCongratulationScreen,
)
class DiagnosticTestCongratulationRoute extends GoRouteData
    with $DiagnosticTestCongratulationRoute {
  const DiagnosticTestCongratulationRoute();
  @override
  Widget build(BuildContext context, GoRouterState state) {
    return DeferredRoute(
      diagnostic_test_congratulation.loadLibrary,
      () => diagnostic_test_congratulation.DiagnosticTestCongratulationScreen(),
    );
  }
}

@TypedGoRoute<DiagnosticTestOnboardRoute>(
  path: '/diagnostic-test-onboarding',
  name: RouterName.diagnosticTestOnboardScreen,
)
class DiagnosticTestOnboardRoute extends GoRouteData
    with $DiagnosticTestOnboardRoute {
  const DiagnosticTestOnboardRoute();
  @override
  Widget build(BuildContext context, GoRouterState state) {
    return DeferredRoute(
      diagnostic_test_onboarding.loadLibrary,
      () => diagnostic_test_onboarding.DiagnosticTestOnboardingScreen(),
    );
  }
}
