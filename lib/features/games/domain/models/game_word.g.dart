// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'game_word.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_WordObject _$WordObjectFromJson(Map<String, dynamic> json) => _WordObject(
  word: json['word'] as String,
  verticalPosition: (json['verticalPosition'] as num?)?.toDouble(),
  horizontalPosition: (json['horizontalPosition'] as num?)?.toDouble(),
  speed: (json['speed'] as num?)?.toDouble(),
  selected: json['selected'] as bool? ?? false,
);

Map<String, dynamic> _$WordObjectToJson(_WordObject instance) =>
    <String, dynamic>{
      'word': instance.word,
      'verticalPosition': instance.verticalPosition,
      'horizontalPosition': instance.horizontalPosition,
      'speed': instance.speed,
      'selected': instance.selected,
    };

_MemoryFlashTopic _$MemoryFlashTopicFromJson(Map<String, dynamic> json) =>
    _MemoryFlashTopic(
      order: (json['order'] as num?)?.toInt(),
      title: json['title'] as String?,
      icon: json['icon_url'] as String?,
      image: json['image_url'] as String?,
      references: json['references'] as String?,
    );

Map<String, dynamic> _$MemoryFlashTopicToJson(_MemoryFlashTopic instance) =>
    <String, dynamic>{
      'order': instance.order,
      'title': instance.title,
      'icon_url': instance.icon,
      'image_url': instance.image,
      'references': instance.references,
    };

_MemoryFlash _$MemoryFlashFromJson(Map<String, dynamic> json) => _MemoryFlash(
  order: (json['order'] as num?)?.toInt(),
  topic: json['topic'] as String?,
  countdown: (json['countdown'] as num?)?.toInt(),
  references: json['references'] as String?,
  listText: json['list_text'] == null
      ? const []
      : fromMapToString(json['list_text']),
);

Map<String, dynamic> _$MemoryFlashToJson(_MemoryFlash instance) =>
    <String, dynamic>{
      'order': instance.order,
      'topic': instance.topic,
      'countdown': instance.countdown,
      'references': instance.references,
      'list_text': instance.listText,
    };
