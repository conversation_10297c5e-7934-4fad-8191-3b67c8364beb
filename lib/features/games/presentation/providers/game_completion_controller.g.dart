// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'game_completion_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, type=warning
/// A provider to handle game completion state across different game screens
/// This replaces the need to return values through navigation

@ProviderFor(GameCompletionController)
const gameCompletionControllerProvider = GameCompletionControllerProvider._();

/// A provider to handle game completion state across different game screens
/// This replaces the need to return values through navigation
final class GameCompletionControllerProvider
    extends $NotifierProvider<GameCompletionController, bool> {
  /// A provider to handle game completion state across different game screens
  /// This replaces the need to return values through navigation
  const GameCompletionControllerProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'gameCompletionControllerProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$gameCompletionControllerHash();

  @$internal
  @override
  GameCompletionController create() => GameCompletionController();

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$gameCompletionControllerHash() =>
    r'3cccd0665649d3161ab5187629d79b49a7913f74';

/// A provider to handle game completion state across different game screens
/// This replaces the need to return values through navigation

abstract class _$GameCompletionController extends $Notifier<bool> {
  bool build();
  @$mustCallSuper
  @override
  void runBuild() {
    final created = build();
    final ref = this.ref as $Ref<bool, bool>;
    final element =
        ref.element
            as $ClassProviderElement<
              AnyNotifier<bool, bool>,
              bool,
              Object?,
              Object?
            >;
    element.handleValue(ref, created);
  }
}
