// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'memory_flash_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, type=warning

@ProviderFor(MemoryFlashController)
const memoryFlashControllerProvider = MemoryFlashControllerFamily._();

final class MemoryFlashControllerProvider
    extends $AsyncNotifierProvider<MemoryFlashController, MemoryFlashState> {
  const MemoryFlashControllerProvider._({
    required MemoryFlashControllerFamily super.from,
    required String? super.argument,
  }) : super(
         retry: null,
         name: r'memoryFlashControllerProvider',
         isAutoDispose: true,
         dependencies: null,
         $allTransitiveDependencies: null,
       );

  @override
  String debugGetCreateSourceHash() => _$memoryFlashControllerHash();

  @override
  String toString() {
    return r'memoryFlashControllerProvider'
        ''
        '($argument)';
  }

  @$internal
  @override
  MemoryFlashController create() => MemoryFlashController();

  @override
  bool operator ==(Object other) {
    return other is MemoryFlashControllerProvider && other.argument == argument;
  }

  @override
  int get hashCode {
    return argument.hashCode;
  }
}

String _$memoryFlashControllerHash() =>
    r'1de6b14ce709af921b170766dc5762ea7dea7550';

final class MemoryFlashControllerFamily extends $Family
    with
        $ClassFamilyOverride<
          MemoryFlashController,
          AsyncValue<MemoryFlashState>,
          MemoryFlashState,
          FutureOr<MemoryFlashState>,
          String?
        > {
  const MemoryFlashControllerFamily._()
    : super(
        retry: null,
        name: r'memoryFlashControllerProvider',
        dependencies: null,
        $allTransitiveDependencies: null,
        isAutoDispose: true,
      );

  MemoryFlashControllerProvider call({String? topic}) =>
      MemoryFlashControllerProvider._(argument: topic, from: this);

  @override
  String toString() => r'memoryFlashControllerProvider';
}

abstract class _$MemoryFlashController
    extends $AsyncNotifier<MemoryFlashState> {
  late final _$args = ref.$arg as String?;
  String? get topic => _$args;

  FutureOr<MemoryFlashState> build({String? topic});
  @$mustCallSuper
  @override
  void runBuild() {
    final created = build(topic: _$args);
    final ref =
        this.ref as $Ref<AsyncValue<MemoryFlashState>, MemoryFlashState>;
    final element =
        ref.element
            as $ClassProviderElement<
              AnyNotifier<AsyncValue<MemoryFlashState>, MemoryFlashState>,
              AsyncValue<MemoryFlashState>,
              Object?,
              Object?
            >;
    element.handleValue(ref, created);
  }
}
