// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'topic_game_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, type=warning

@ProviderFor(TopicGameController)
const topicGameControllerProvider = TopicGameControllerProvider._();

final class TopicGameControllerProvider
    extends $AsyncNotifierProvider<TopicGameController, TopicGameState> {
  const TopicGameControllerProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'topicGameControllerProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$topicGameControllerHash();

  @$internal
  @override
  TopicGameController create() => TopicGameController();
}

String _$topicGameControllerHash() =>
    r'f6fe8a9b05672e39ca4751882ee57666da64ce4a';

abstract class _$TopicGameController extends $AsyncNotifier<TopicGameState> {
  FutureOr<TopicGameState> build();
  @$mustCallSuper
  @override
  void runBuild() {
    final created = build();
    final ref = this.ref as $Ref<AsyncValue<TopicGameState>, TopicGameState>;
    final element =
        ref.element
            as $ClassProviderElement<
              AnyNotifier<AsyncValue<TopicGameState>, TopicGameState>,
              AsyncValue<TopicGameState>,
              Object?,
              Object?
            >;
    element.handleValue(ref, created);
  }
}
