import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:selfeng/shared/domain/models/level.dart';

part 'level_info.freezed.dart';
part 'level_info.g.dart';

@freezed
sealed class LevelInfo with _$LevelInfo {
  @JsonSerializable(explicitToJson: true)
  factory LevelInfo({
    required Level level,
    required String image,
    required LevelLocalizeContent title,
    required LevelLocalizeContent description,
    required int startChapter,
    required int endChapter,
  }) = _LevelInfo;
  factory LevelInfo.fromJson(Map<String, dynamic> json) =>
      _$LevelInfoFromJson(json);
}

@freezed
sealed class LevelLocalizeContent with _$LevelLocalizeContent {
  factory LevelLocalizeContent({required String en, required String id}) =
      _LevelLocalizeContent;
  factory LevelLocalizeContent.fromJson(Map<String, dynamic> json) =>
      _$LevelLocalizeContentFromJson(json);
  // Allow custom getters / setters
  const LevelLocalizeContent._();

  String getByLocale(String locale) {
    switch (locale) {
      case 'en':
        return en;
      case 'id':
        return id;
      default:
        return en;
    }
  }
}
