// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'level_info.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_LevelInfo _$LevelInfoFromJson(Map<String, dynamic> json) => _LevelInfo(
  level: $enumDecode(_$LevelEnumMap, json['level']),
  image: json['image'] as String,
  title: LevelLocalizeContent.fromJson(json['title'] as Map<String, dynamic>),
  description: LevelLocalizeContent.fromJson(
    json['description'] as Map<String, dynamic>,
  ),
  startChapter: (json['startChapter'] as num).toInt(),
  endChapter: (json['endChapter'] as num).toInt(),
);

Map<String, dynamic> _$LevelInfoToJson(_LevelInfo instance) =>
    <String, dynamic>{
      'level': _$LevelEnumMap[instance.level]!,
      'image': instance.image,
      'title': instance.title.toJson(),
      'description': instance.description.toJson(),
      'startChapter': instance.startChapter,
      'endChapter': instance.endChapter,
    };

const _$LevelEnumMap = {
  Level.a1: 'a1',
  Level.a2: 'a2',
  Level.b1: 'b1',
  Level.b2: 'b2',
  Level.c1: 'c1',
  Level.c2: 'c2',
};

_LevelLocalizeContent _$LevelLocalizeContentFromJson(
  Map<String, dynamic> json,
) => _LevelLocalizeContent(en: json['en'] as String, id: json['id'] as String);

Map<String, dynamic> _$LevelLocalizeContentToJson(
  _LevelLocalizeContent instance,
) => <String, dynamic>{'en': instance.en, 'id': instance.id};
