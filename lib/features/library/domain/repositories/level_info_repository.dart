import 'package:selfeng/features/library/domain/models/level_info.dart';
import 'package:selfeng/shared/domain/models/level.dart';

abstract class LevelInfoRepository {
  Future<List<LevelInfo>> getAllLevelInfo();
  Future<LevelInfo> getLevelInfo(Level level);
}

class LevelInfoRepositoryImpl implements LevelInfoRepository {
  // Singleton instance
  static final LevelInfoRepositoryImpl _instance =
      LevelInfoRepositoryImpl._internal();
  factory LevelInfoRepositoryImpl() => _instance;
  LevelInfoRepositoryImpl._internal();

  // Cache the level info to avoid rebuilding
  List<LevelInfo>? _cachedLevels;

  @override
  Future<List<LevelInfo>> getAllLevelInfo() async {
    // Return cached data if available
    if (_cachedLevels != null) {
      return _cachedLevels!;
    }

    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 10));

    _cachedLevels = [
      LevelInfo(
        level: Level.a1,
        image: 'assets/images/main_lesson/a1.png',
        title: LevelLocalizeContent(en: 'Beginner', id: 'Pemula'),
        description: LevelLocalizeContent(
          en: 'Engage in basic conversations, introduce oneself, ask and answer simple questions, and make basic plans or purchases.',
          id: 'Berbicara dasar, perkenalan, tanya jawab sederhana, serta membuat rencana atau transaksi dasar.',
        ),
        startChapter: 1,
        endChapter: 8,
      ),
      LevelInfo(
        level: Level.a2,
        image: 'assets/images/main_lesson/a2.png',
        title: LevelLocalizeContent(en: 'Elementary', id: 'Dasar'),
        description: LevelLocalizeContent(
          en: 'Discuss hobbies, describe past experiences, navigate travel scenarios, and handle social invitations with confidence.',
          id: 'Membahas hobi, pengalaman, perjalanan, dan undangan sosial dengan percaya diri.',
        ),
        startChapter: 9,
        endChapter: 16,
      ),
      LevelInfo(
        level: Level.b1,
        image: 'assets/images/main_lesson/b1.png',
        title: LevelLocalizeContent(en: 'Intermediate', id: 'Menengah'),
        description: LevelLocalizeContent(
          en: 'Communicate about work, studies, and relationships, express opinions, give advice, and discuss current events.',
          id: 'Berkomunikasi tentang pekerjaan, studi, hubungan, memberi pendapat, saran, dan membahas isu terkini.',
        ),
        startChapter: 17,
        endChapter: 24,
      ),
      LevelInfo(
        level: Level.b2,
        image: 'assets/images/main_lesson/b2.png',
        title: LevelLocalizeContent(
          en: 'Upper Intermediate',
          id: 'Menengah Atas',
        ),
        description: LevelLocalizeContent(
          en: 'Debate, analyze abstract topics, summarize complex information, and enhance presentation skills.',
          id: 'Berdebat, menganalisis topik abstrak, merangkum informasi kompleks, dan meningkatkan presentasi.',
        ),
        startChapter: 25,
        endChapter: 32,
      ),
      LevelInfo(
        level: Level.c1,
        image: 'assets/images/main_lesson/c1.png',
        title: LevelLocalizeContent(en: 'Advanced', id: 'Mahir'),
        description: LevelLocalizeContent(
          en: 'Lead meetings, write reports, and navigate cross-cultural professional communication effectively.',
          id: 'Memimpin rapat, menulis laporan, dan berkomunikasi lintas budaya secara profesional.',
        ),
        startChapter: 33,
        endChapter: 40,
      ),
      LevelInfo(
        level: Level.c2,
        image: 'assets/images/main_lesson/c2.png',
        title: LevelLocalizeContent(en: 'Proficient', id: 'Fasih'),
        description: LevelLocalizeContent(
          en: 'Engage in sophisticated debates, analyze complex issues, and deliver research-based presentations with clarity.',
          id: 'Berdebat secara mendalam, menganalisis isu kompleks, dan menyampaikan presentasi berbasis riset dengan jelas.',
        ),
        startChapter: 41,
        endChapter: 47,
      ),
    ];

    return _cachedLevels!;
  }

  @override
  Future<LevelInfo> getLevelInfo(Level level) async {
    final levels = await getAllLevelInfo();
    return levels.firstWhere((info) => info.level == level);
  }
}
