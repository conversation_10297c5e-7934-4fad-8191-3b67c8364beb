import 'package:selfeng/services/firestore_service_service/domain/repositories/firestore_service_repository.dart';
import 'package:selfeng/shared/domain/models/chapter/chapter_data.dart';
import 'package:selfeng/shared/domain/models/either.dart';
import 'package:selfeng/shared/exceptions/http_exception.dart';

abstract class LibraryRepository {
  Future<Either<AppException, List<ChapterData>>> getChapters(String level);
  Future<Either<AppException, ChapterData>> getChapterData(
    String level,
    String chapter,
  );
}

class LibraryRepositoryImpl extends LibraryRepository {
  // final Firebase Firestore dataSource;
  final FirestoreServiceRepository dataSource;

  LibraryRepositoryImpl(this.dataSource);

  @override
  Future<Either<AppException, List<ChapterData>>> getChapters(
    String level,
  ) async {
    try {
      final snapshot = await dataSource.fireStore
          .collection('newcourse-content')
          .doc(level)
          .collection('chapters')
          .orderBy('chapter')
          .get();

      final docs = snapshot.docs
          .map((doc) => ChapterData.fromJson(doc.data()))
          .toList();

      return Right(docs);
    } catch (e) {
      return Left(
        AppException(
          identifier: 'Fetch chapters data',
          statusCode: 0,
          message: e.toString(),
        ),
      );
    }
  }

  @override
  Future<Either<AppException, ChapterData>> getChapterData(
    String level,
    String chapter,
  ) async {
    try {
      final doc = await dataSource.fireStore
          .collection('newcourse-content')
          .doc(level)
          .collection('chapters')
          .where('chapter', isEqualTo: int.parse(chapter))
          .get();

      if (doc.docs.isNotEmpty) {
        final docData = doc.docs.first.data();
        return Right(ChapterData.fromJson(docData));
      } else {
        return Left(
          AppException(
            identifier: 'Fetch chapter data',
            statusCode: 0,
            message: 'Chapter not found',
          ),
        );
      }
    } catch (e) {
      return Left(
        AppException(
          identifier: 'Fetch chapter data',
          statusCode: 0,
          message: e.toString(),
        ),
      );
    }
  }
}
