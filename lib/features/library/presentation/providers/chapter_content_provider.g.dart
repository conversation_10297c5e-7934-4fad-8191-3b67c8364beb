// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'chapter_content_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, type=warning

@ProviderFor(ChapterContentNotifier)
const chapterContentProvider = ChapterContentNotifierProvider._();

final class ChapterContentNotifierProvider
    extends $NotifierProvider<ChapterContentNotifier, ChapterContentState> {
  const ChapterContentNotifierProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'chapterContentProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$chapterContentNotifierHash();

  @$internal
  @override
  ChapterContentNotifier create() => ChapterContentNotifier();

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(ChapterContentState value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<ChapterContentState>(value),
    );
  }
}

String _$chapterContentNotifierHash() =>
    r'd99d2a474045cf7ff14d21220f0c97559da704e9';

abstract class _$ChapterContentNotifier extends $Notifier<ChapterContentState> {
  ChapterContentState build();
  @$mustCallSuper
  @override
  void runBuild() {
    final created = build();
    final ref = this.ref as $Ref<ChapterContentState, ChapterContentState>;
    final element =
        ref.element
            as $ClassProviderElement<
              AnyNotifier<ChapterContentState, ChapterContentState>,
              ChapterContentState,
              Object?,
              Object?
            >;
    element.handleValue(ref, created);
  }
}
