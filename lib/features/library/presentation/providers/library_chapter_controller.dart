import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:selfeng/features/library/domain/models/level_info.dart';
import 'package:selfeng/features/library/domain/providers/level_info_provider.dart';
import 'package:selfeng/features/library/domain/providers/library_provider.dart';
import 'package:selfeng/features/library/presentation/providers/state/library_chapter_state.dart';
import 'package:selfeng/services/crashlytics_service/utils/error_handler.dart';
import 'package:selfeng/services/crashlytics_service/domain/providers/crashlytics_service_provider.dart';
import 'package:selfeng/shared/domain/models/level.dart';

part 'library_chapter_controller.g.dart';

@riverpod
class LibraryChapterController extends _$LibraryChapterController {
  // Cache states by level to avoid rebuilding during transitions
  final Map<String, LibraryChapterState> _stateCache = {};

  @override
  Future<LibraryChapterState> build(String level) async {
    try {
      // Return cached state if available
      if (_stateCache.containsKey(level)) {
        return _stateCache[level]!;
      }

      // Get repositories
      final libraryRepository = ref.watch(libraryRepositoryProvider);
      final levelInfo = ref.watch(levelInfoRepositoryProvider);

      // Get level info and chapters in parallel
      final levelEnum = Level.values.byName(level.toLowerCase());
      final levelInfoFuture = levelInfo.getLevelInfo(levelEnum);
      final chaptersFuture = libraryRepository.getChapters(level);

      // Wait for both to complete
      final results = await Future.wait([levelInfoFuture, chaptersFuture]);
      final levelInfoData = results[0] as LevelInfo;
      final chaptersResult = results[1];

      // Create state with both level info and chapters
      final state = (chaptersResult as dynamic).fold(
        (failure) => throw failure.message,
        (chapters) =>
            LibraryChapterState(levelInfo: levelInfoData, chapters: chapters),
      );

      // Cache the state
      _stateCache[level] = state;
      return state;
    } catch (e, stackTrace) {
      // Clear cache if there's an error
      _stateCache.remove(level);

      // Set context for Crashlytics
      final crashlyticsService = ref.read(crashlyticsServiceProvider);
      await crashlyticsService.setCustomKey('current_level', level);
      await crashlyticsService.setCustomKey('feature', 'library_chapter');

      // Report error to Crashlytics
      final errorHandler = ErrorHandler(crashlyticsService);
      await errorHandler.reportBusinessLogicError(
        e,
        stackTrace,
        feature: 'library_chapter',
        operation: 'fetch_chapter_data',
        businessContext: {
          'level': level,
          'cached': _stateCache.containsKey(level),
        },
      );

      rethrow;
    }
  }
}
