// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'library_chapter_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, type=warning

@ProviderFor(LibraryChapterController)
const libraryChapterControllerProvider = LibraryChapterControllerFamily._();

final class LibraryChapterControllerProvider
    extends
        $AsyncNotifierProvider<LibraryChapterController, LibraryChapterState> {
  const LibraryChapterControllerProvider._({
    required LibraryChapterControllerFamily super.from,
    required String super.argument,
  }) : super(
         retry: null,
         name: r'libraryChapterControllerProvider',
         isAutoDispose: true,
         dependencies: null,
         $allTransitiveDependencies: null,
       );

  @override
  String debugGetCreateSourceHash() => _$libraryChapterControllerHash();

  @override
  String toString() {
    return r'libraryChapterControllerProvider'
        ''
        '($argument)';
  }

  @$internal
  @override
  LibraryChapterController create() => LibraryChapterController();

  @override
  bool operator ==(Object other) {
    return other is LibraryChapterControllerProvider &&
        other.argument == argument;
  }

  @override
  int get hashCode {
    return argument.hashCode;
  }
}

String _$libraryChapterControllerHash() =>
    r'a9b11d8da1e849df264984a3014663206f9f408c';

final class LibraryChapterControllerFamily extends $Family
    with
        $ClassFamilyOverride<
          LibraryChapterController,
          AsyncValue<LibraryChapterState>,
          LibraryChapterState,
          FutureOr<LibraryChapterState>,
          String
        > {
  const LibraryChapterControllerFamily._()
    : super(
        retry: null,
        name: r'libraryChapterControllerProvider',
        dependencies: null,
        $allTransitiveDependencies: null,
        isAutoDispose: true,
      );

  LibraryChapterControllerProvider call(String level) =>
      LibraryChapterControllerProvider._(argument: level, from: this);

  @override
  String toString() => r'libraryChapterControllerProvider';
}

abstract class _$LibraryChapterController
    extends $AsyncNotifier<LibraryChapterState> {
  late final _$args = ref.$arg as String;
  String get level => _$args;

  FutureOr<LibraryChapterState> build(String level);
  @$mustCallSuper
  @override
  void runBuild() {
    final created = build(_$args);
    final ref =
        this.ref as $Ref<AsyncValue<LibraryChapterState>, LibraryChapterState>;
    final element =
        ref.element
            as $ClassProviderElement<
              AnyNotifier<AsyncValue<LibraryChapterState>, LibraryChapterState>,
              AsyncValue<LibraryChapterState>,
              Object?,
              Object?
            >;
    element.handleValue(ref, created);
  }
}
