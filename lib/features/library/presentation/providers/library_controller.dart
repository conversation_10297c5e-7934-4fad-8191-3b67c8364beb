import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:selfeng/features/library/domain/providers/level_info_provider.dart';
import 'package:selfeng/features/library/presentation/providers/state/library_state.dart';
import 'package:selfeng/services/crashlytics_service/utils/error_handler.dart';
import 'package:selfeng/services/crashlytics_service/domain/providers/crashlytics_service_provider.dart';

part 'library_controller.g.dart';

@Riverpod(keepAlive: false)
class LibraryController extends _$LibraryController {
  LibraryState? _cachedState;

  @override
  Future<LibraryState> build() async {
    if (_cachedState != null) {
      return _cachedState!;
    }

    final errorHandler = ErrorHandler(ref.read(crashlyticsServiceProvider));

    try {
      final levelInfoRepository = ref.watch(levelInfoRepositoryProvider);
      final levels = await levelInfoRepository.getAllLevelInfo();
      _cachedState = LibraryState(levels: levels);
      return _cachedState!;
    } catch (error, stackTrace) {
      await errorHandler.reportBusinessLogicError(
        error,
        stackTrace,
        feature: 'library',
        operation: 'fetch_levels',
      );
      rethrow;
    }
  }
}
