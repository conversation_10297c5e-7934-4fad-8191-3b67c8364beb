// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'library_scroll_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, type=warning
/// Provider that manages the scroll position for the Library screen.
///
/// This provider persists the scroll position across navigation,
/// allowing users to return to the same position in the library list
/// after navigating to other screens.

@ProviderFor(LibraryScrollPosition)
const libraryScrollPositionProvider = LibraryScrollPositionProvider._();

/// Provider that manages the scroll position for the Library screen.
///
/// This provider persists the scroll position across navigation,
/// allowing users to return to the same position in the library list
/// after navigating to other screens.
final class LibraryScrollPositionProvider
    extends $NotifierProvider<LibraryScrollPosition, double> {
  /// Provider that manages the scroll position for the Library screen.
  ///
  /// This provider persists the scroll position across navigation,
  /// allowing users to return to the same position in the library list
  /// after navigating to other screens.
  const LibraryScrollPositionProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'libraryScrollPositionProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$libraryScrollPositionHash();

  @$internal
  @override
  LibraryScrollPosition create() => LibraryScrollPosition();

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(double value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<double>(value),
    );
  }
}

String _$libraryScrollPositionHash() =>
    r'b36aec1a572d8be3303be17003586c913d871e0c';

/// Provider that manages the scroll position for the Library screen.
///
/// This provider persists the scroll position across navigation,
/// allowing users to return to the same position in the library list
/// after navigating to other screens.

abstract class _$LibraryScrollPosition extends $Notifier<double> {
  double build();
  @$mustCallSuper
  @override
  void runBuild() {
    final created = build();
    final ref = this.ref as $Ref<double, double>;
    final element =
        ref.element
            as $ClassProviderElement<
              AnyNotifier<double, double>,
              double,
              Object?,
              Object?
            >;
    element.handleValue(ref, created);
  }
}
