import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:selfeng/features/library/presentation/providers/library_chapter_controller.dart';
import 'package:selfeng/features/library/presentation/widgets/library_chapter_card.dart';
import 'package:selfeng/shared/domain/models/level.dart';
import 'package:selfeng/shared/widgets/loading_circle.dart';

class LibraryChapterScreen extends ConsumerStatefulWidget {
  final String level;
  final String levelName;

  const LibraryChapterScreen({
    super.key,
    required this.level,
    required this.levelName,
  });

  @override
  ConsumerState<LibraryChapterScreen> createState() =>
      _LibraryChapterScreenState();
}

class _LibraryChapterScreenState extends ConsumerState<LibraryChapterScreen> {
  late final Level _levelEnum = Level.values.byName(widget.level.toLowerCase());
  late final _provider = libraryChapterControllerProvider(widget.level);

  @override
  Widget build(BuildContext context) {
    final libraryChapterState = ref.watch(_provider);

    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5), // Light gray background
      appBar: AppBar(
        title: Text(
          // '${widget.level.toUpperCase()} - ${libraryChapterState.value?.levelInfo?.title.getByLocale(locale) ?? ''}',
          '${widget.level.toUpperCase()} - ${widget.levelName}',
          style: const TextStyle(fontWeight: FontWeight.bold),
          textAlign: TextAlign.left,
        ),
        centerTitle: false,
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          onPressed: () => context.pop(),
          icon: const Icon(
            Icons.arrow_back_ios,
            // size: 36,
            // color: Colors.black,
          ),
        ),
      ),
      body: libraryChapterState.when(
        data: (state) => Column(
          children: [
            Expanded(
              child: state.chapters.isEmpty
                  ? const LoadingCircle()
                  : ListView.builder(
                      padding: const EdgeInsets.only(
                        top: 16,
                        left: 16,
                        right: 16,
                      ), // Add horizontal padding
                      itemCount: state.chapters.length,
                      itemBuilder: (context, index) => Padding(
                        padding: const EdgeInsets.only(
                          bottom: 16,
                        ), // Add spacing between cards
                        child: LibraryChapterCard(
                          chapter: state.chapters[index],
                          level: _levelEnum,
                        ),
                      ),
                    ),
            ),
          ],
        ),
        loading: () => Column(
          children: [
            const SizedBox(height: 32),
            // Show a shimmer effect for the level card
            Container(
              margin: const EdgeInsets.symmetric(horizontal: 16),
              height: 180, // Match the new card height
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(20),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
            ),
            const Expanded(child: Center(child: LoadingCircle())),
          ],
        ),
        error: (error, stack) => Center(child: Text(error.toString())),
      ),
    );
  }
}
