import 'package:flutter/material.dart';
import 'package:selfeng/features/library/presentation/widgets/library_header.dart';
import 'package:selfeng/features/library/presentation/widgets/library_levels_list.dart';

class LibraryScreen extends StatelessWidget {
  const LibraryScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return const Scaffold(
      backgroundColor: Color(
        0xFFF5F5F5,
      ), // Light gray background to match library_chapter_screen
      body: Column(
        children: [
          LibraryHeader(),
          Expanded(child: LibraryLevelsList()),
        ],
      ),
    );
  }
}
