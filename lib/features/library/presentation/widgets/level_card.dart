import 'package:flutter/material.dart';
import 'package:selfeng/configs/routes/core_router_name.dart';
import 'package:selfeng/features/library/domain/models/level_info.dart';
import 'package:selfeng/shared/helpers/navigation_helper.dart';
import 'package:selfeng/shared/localization/app_localizations_context.dart';

class LevelCard extends StatelessWidget {
  final LevelInfo data;
  final bool tapable;
  static const double _imageWidth = 0.3;

  const LevelCard({super.key, required this.data, this.tapable = true});

  void _handleTap(BuildContext context, String locale) {
    if (!tapable) return;
    customNav(
      context,
      RouterName.libraryChapter,
      params: {
        'level': data.level.toString(),
        'levelName': data.title.getByLocale(locale),
      },
    );
  }

  Widget _buildLevelNumber() {
    return Container(
      width: 50,
      height: 50,
      decoration: BoxDecoration(
        color: Colors.white,
        shape: BoxShape.circle,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.15),
            blurRadius: 6,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Center(
        child: Text(
          data.level.name,
          style: const TextStyle(
            fontSize: 22,
            fontWeight: FontWeight.bold,
            color: Color(0xFFA50012), // Use app's primary color
          ),
        ),
      ),
    );
  }

  Widget _buildImage(double screenWidth) {
    return ClipRRect(
      borderRadius: const BorderRadius.only(
        topLeft: Radius.circular(12),
        bottomLeft: Radius.circular(12),
      ),
      child: Image.asset(
        data.image,
        width: screenWidth * _imageWidth,
        height: 180, // Match the card height
        fit: BoxFit.cover,
        alignment: Alignment.centerLeft, // Ensure image starts from left side
      ),
    );
  }

  Widget _buildContent(
    BuildContext context,
    String locale,
    TextTheme textTheme,
  ) {
    return Expanded(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.start,
          mainAxisSize: MainAxisSize.max,
          children: [
            Text(
              data.title.getByLocale(locale),
              style: textTheme.titleMedium?.copyWith(
                color: const Color(0xFFA50012), // Use primary color for title
                fontSize: 18,
                fontWeight: FontWeight.bold,
                height: 1.2,
              ),
              maxLines: 3,
              overflow: TextOverflow.visible,
              softWrap: true,
            ),
            const SizedBox(height: 8),
            Expanded(
              child: Text(
                data.description.getByLocale(locale),
                style: textTheme.bodyMedium?.copyWith(
                  color: Colors.grey[600], // Muted color for description
                  fontSize: 13,
                  height: 1.3,
                ),
                overflow: TextOverflow.visible,
                softWrap: true,
                textAlign: TextAlign.start,
              ),
            ),
            const SizedBox(height: 6),
            Text(
              '${context.loc.chapters_range} ${data.startChapter} - ${data.endChapter}',
              style: textTheme.bodySmall?.copyWith(
                color: Colors.grey[600], // Same color as description
                fontSize: 12,
                height: 1.2,
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final locale = Localizations.localeOf(context).languageCode;
    final textTheme = Theme.of(context).textTheme;

    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: tapable ? () => _handleTap(context, locale) : null,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          height: 180, // Match the card height from library_chapter_card
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.08),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            children: [
              // Image section
              SizedBox(
                width: screenWidth * _imageWidth,
                child: Stack(
                  children: [
                    _buildImage(screenWidth),
                    if (tapable)
                      Positioned(
                        bottom: 12,
                        left: (screenWidth * _imageWidth * 0.5) - 25,
                        child: _buildLevelNumber(),
                      ),
                  ],
                ),
              ),
              // Content section
              _buildContent(context, locale, textTheme),
            ],
          ),
        ),
      ),
    );
  }
}
