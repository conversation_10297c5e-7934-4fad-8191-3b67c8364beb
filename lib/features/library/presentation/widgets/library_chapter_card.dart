import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:selfeng/configs/routes/core_router_name.dart';
import 'package:selfeng/shared/domain/models/chapter/chapter_data.dart';
import 'package:selfeng/shared/domain/models/level.dart';
import 'package:selfeng/shared/helpers/navigation_helper.dart';
import 'package:selfeng/shared/widgets/loading_circle.dart';

class LibraryChapterCard extends StatelessWidget {
  final ChapterData chapter;
  final Level level;
  final bool tapable;
  static const double _imageWidth = 0.3;

  const LibraryChapterCard({
    super.key,
    required this.chapter,
    required this.level,
    this.tapable = true,
  });

  void _handleTap(BuildContext context) {
    if (!tapable) return;
    customNav(
      context,
      RouterName.libraryChapterContent,
      params: {'level': level.name, 'chapter': chapter.chapter.toString()},
    );
  }

  Widget _buildChapterNumber() {
    return Container(
      width: 50,
      height: 50,
      decoration: BoxDecoration(
        color: Colors.white,
        shape: BoxShape.circle,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.15),
            blurRadius: 6,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Center(
        child: Text(
          chapter.chapter.toString(),
          style: const TextStyle(
            fontSize: 22,
            fontWeight: FontWeight.bold,
            color: Color(0xFFA50012), // Use app's primary color
          ),
        ),
      ),
    );
  }

  Widget _buildImage(double screenWidth) {
    return ClipRRect(
      borderRadius: const BorderRadius.only(
        topLeft: Radius.circular(12),
        bottomLeft: Radius.circular(12),
      ),
      child: CachedNetworkImage(
        placeholder: (context, url) => Container(
          width: screenWidth * _imageWidth,
          height: 180, // Increased height to prevent overflow
          color: Colors.grey[200],
          child: const Center(child: LoadingCircle()),
        ),
        errorWidget: (context, url, error) => Container(
          width: screenWidth * _imageWidth,
          height: 180, // Increased height to prevent overflow
          color: Colors.grey[200],
          child: const Icon(Icons.error, color: Colors.grey),
        ),
        fit: BoxFit.cover,
        imageUrl: chapter.imageUrl,
        width: screenWidth * _imageWidth,
        height: 180, // Increased height to prevent overflow
        memCacheWidth: (screenWidth * _imageWidth).toInt(),
      ),
    );
  }

  Widget _buildContent(
    BuildContext context,
    String locale,
    TextTheme textTheme,
  ) {
    return Expanded(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment:
              MainAxisAlignment.start, // Changed from center to start
          mainAxisSize: MainAxisSize.max, // Take full available height
          children: [
            Text(
              chapter.label,
              style: textTheme.titleMedium?.copyWith(
                color: const Color(0xFFA50012), // Use primary color for title
                fontSize: 18,
                fontWeight: FontWeight.bold,
                height: 1.2, // Better line height for title
              ),
              maxLines: 3, // Increased from 2 to allow more wrapping
              overflow:
                  TextOverflow.visible, // Changed from ellipsis to visible
              softWrap: true, // Ensure proper wrapping
            ),
            const SizedBox(height: 8),
            Expanded(
              // Changed from Flexible to Expanded for better space usage
              child: Text(
                locale == 'en'
                    ? chapter.description.en
                    : chapter.description.id,
                style: textTheme.bodyMedium?.copyWith(
                  color: Colors.grey[600], // Muted color for description
                  fontSize: 13, // Slightly smaller font to fit more content
                  height: 1.3, // Line height for better readability
                ),
                overflow:
                    TextOverflow.visible, // Changed from ellipsis to visible
                softWrap: true,
                textAlign: TextAlign.start, // Ensure left alignment
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final locale = Localizations.localeOf(context).languageCode;
    final textTheme = Theme.of(context).textTheme;

    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: tapable ? () => _handleTap(context) : null,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          height: 180, // Increased height to prevent overflow
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.08),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            children: [
              // Image section
              SizedBox(
                width: screenWidth * _imageWidth,
                child: Stack(
                  children: [
                    _buildImage(screenWidth),
                    if (tapable)
                      Positioned(
                        top: 12,
                        left: (screenWidth * _imageWidth * 0.5) - 25,
                        child: _buildChapterNumber(),
                      ),
                  ],
                ),
              ),
              // Content section
              _buildContent(context, locale, textTheme),
            ],
          ),
        ),
      ),
    );
  }
}
