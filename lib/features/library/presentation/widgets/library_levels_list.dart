import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:selfeng/features/library/presentation/providers/library_controller.dart';
import 'package:selfeng/features/library/presentation/providers/library_scroll_provider.dart';
import 'package:selfeng/features/library/presentation/widgets/level_card.dart';
import 'package:selfeng/shared/widgets/loading_circle.dart';

/// A scrollable list of library levels that preserves scroll position
/// across navigation.
///
/// This widget automatically saves the user's scroll position when they
/// navigate away and restores it when they return to the library screen.
class LibraryLevelsList extends ConsumerStatefulWidget {
  const LibraryLevelsList({super.key});

  @override
  ConsumerState<LibraryLevelsList> createState() => _LibraryLevelsListState();
}

class _LibraryLevelsListState extends ConsumerState<LibraryLevelsList> {
  late ScrollController _scrollController;
  bool _isScrollRestored = false;

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();

    // Listen to scroll changes and update the position in the provider
    _scrollController.addListener(() {
      if (_scrollController.hasClients && _isScrollRestored) {
        ref
            .read(libraryScrollPositionProvider.notifier)
            .updatePosition(_scrollController.offset);
      }
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _restoreScrollPosition(double position) {
    if (!_isScrollRestored) {
      if (position > 0) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (_scrollController.hasClients && mounted) {
            final maxScroll = _scrollController.position.maxScrollExtent;
            final targetPosition = position.clamp(0.0, maxScroll);
            _scrollController.jumpTo(targetPosition);
          }
          _isScrollRestored = true;
        });
      } else {
        // Mark as restored even if position is 0 to avoid unnecessary calls
        _isScrollRestored = true;
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final libraryState = ref.watch(libraryControllerProvider);
    final scrollPosition = ref.watch(libraryScrollPositionProvider);

    return libraryState.when(
      data: (state) {
        final levels = state.levels;

        // Restore scroll position when data is available
        _restoreScrollPosition(scrollPosition);

        return Column(
          children: [
            const SizedBox(
              height: 16,
            ), // Reduced spacing for better proportions
            Expanded(
              child: levels.isEmpty
                  ? const LoadingCircle()
                  : ListView.builder(
                      controller: _scrollController,
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                      ), // Add horizontal padding
                      itemCount: levels.length,
                      itemBuilder: (context, index) => Padding(
                        padding: const EdgeInsets.only(
                          bottom: 16,
                        ), // Add spacing between cards
                        child: LevelCard(data: levels[index]),
                      ),
                    ),
            ),
          ],
        );
      },
      loading: () => Column(
        children: [
          const SizedBox(height: 32),
          // Show a shimmer effect for the level card
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 16),
            height: 180, // Match the new card height
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.08),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
          ),
          const Expanded(child: Center(child: LoadingCircle())),
        ],
      ),
      error: (error, stackTrace) => Center(child: Text('Error: $error')),
    );
  }
}
