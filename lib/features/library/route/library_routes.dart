part of 'package:selfeng/configs/routes/core_routes.dart';

@TypedGoRoute<LibraryRoute>(path: '/library', name: RouterName.library)
class LibraryRoute extends GoRouteData with $LibraryRoute {
  const LibraryRoute();

  @override
  Page<void> buildPage(BuildContext context, GoRouterState state) {
    // Prefetch next possible routes
    library_chapter.loadLibrary();

    return CustomPageTransition(
      child: DeferredRoute(
        library_screen.loadLibrary,
        () => library_screen.LibraryScreen(),
      ),
    );
  }
}

@TypedGoRoute<LibraryChapterRoute>(
  path: '/library-chapter/:level/:levelName',
  name: RouterName.libraryChapter,
)
class LibraryChapterRoute extends GoRouteData with $LibraryChapterRoute {
  const LibraryChapterRoute(this.level, this.levelName);
  final String level;
  final String levelName;

  @override
  Page<void> buildPage(BuildContext context, GoRouterState state) {
    // Prefetch next possible route
    library_chapter_content.loadLibrary();

    return CustomPageTransition(
      child: DeferredRoute(
        library_chapter.loadLibrary,
        () => library_chapter.LibraryChapterScreen(
          level: state.pathParameters['level'] ?? 'A1',
          levelName: state.pathParameters['levelName'] ?? 'A1',
        ),
      ),
    );
  }
}

@TypedGoRoute<LibraryChapterContentRoute>(
  path: '/library-chapter-content/:level/:chapter',
  name: RouterName.libraryChapterContent,
)
class LibraryChapterContentRoute extends GoRouteData
    with $LibraryChapterContentRoute {
  const LibraryChapterContentRoute(this.level, this.chapter);
  final String level;
  final String chapter;

  @override
  Page<void> buildPage(BuildContext context, GoRouterState state) {
    // No need to prefetch since this is the last screen in this flow
    return CustomPageTransition(
      child: DeferredRoute(
        library_chapter_content.loadLibrary,
        () => library_chapter_content.LibraryChapterContentScreen(
          level: state.pathParameters['level'] ?? 'A1',
          chapter: state.pathParameters['chapter'] ?? '1',
        ),
      ),
    );
  }
}
