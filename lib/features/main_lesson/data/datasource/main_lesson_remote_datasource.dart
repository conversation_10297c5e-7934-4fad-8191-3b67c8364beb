import 'dart:convert';
import 'dart:io';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:selfeng/features/main_lesson/domain/models/main_lesson.dart';
import 'package:selfeng/services/firestore_service_service/domain/repositories/firestore_service_repository.dart';
import 'package:selfeng/shared/domain/models/chapter/chapter_data.dart';
import 'package:selfeng/shared/domain/models/either.dart';
import 'package:selfeng/shared/domain/models/level.dart';
import 'package:selfeng/shared/domain/models/pronunciation/pronunciation_result_formatted.dart';
import 'package:selfeng/shared/domain/models/user-data/user_data.dart';
import 'package:selfeng/shared/exceptions/http_exception.dart';
import 'package:selfeng/shared/helpers/deep_cast_helper.dart';

abstract class MainLessonRemoteDatasource {
  Future<Either<AppException, List<ChapterIndexData>>> getChapterIndex({
    required String level,
  });
  Future<Either<AppException, List<ContentIndexData>>> getPathIndex({
    required String level,
    required String chapter,
    required SectionType section,
  });
  Future<Either<AppException, PronunciationSubPart>> getPronunciation({
    required String path,
  });

  Future<Either<AppException, ConversationPart>> getConversation({
    required String path,
  });

  Future<Either<AppException, ListeningPart>> getListening({
    required String path,
  });

  Future<Either<AppException, SpeakingPart>> getSpeaking({
    required String path,
  });

  Future<Either<AppException, List<ConversationPart>>> getConversationList(
    List<String> paths,
  );

  Future<Either<AppException, List<ListeningPart>>> getListeningList(
    List<String> paths,
  );

  Future<Either<AppException, List<SpeakingPart>>> getSpeakingList(
    List<String> paths,
  );

  Future<Either<AppException, List<ChapterData>>> getChapters(Level level);
  Future<Either<AppException, ChapterData>> getChapterData(
    String level,
    String chapter,
  );

  Future<Either<AppException, dynamic>> saveAnswer(answer);
  Future<Either<AppException, dynamic>> uploadAudio({required String path});
  Future<Either<AppException, PronunciationResultFormatted>>
  checkPronunciation({
    required AudioPath audio,
    required String text,
    bool autoDelete = true,
  });

  Future<Either<AppException, ContentIndexData>> getFirstLesson();
}

class MainLessonRemoteDatasourceImpl extends MainLessonRemoteDatasource {
  // final Firebase Firestore dataSource;
  final FirestoreServiceRepository dataSource;

  MainLessonRemoteDatasourceImpl(this.dataSource);

  @override
  Future<Either<AppException, List<ChapterIndexData>>> getChapterIndex({
    required String level,
  }) async {
    try {
      final QuerySnapshot<Map<String, dynamic>> result = await dataSource
          .fireStore
          .collection('course-content-index')
          .doc(level)
          .collection('chapters')
          .orderBy('chapter')
          .get();

      final docs = result.docs.map((doc) {
        final docData = doc.data();
        return ChapterIndexData.fromJson(docData);
      }).toList();

      return Right(docs);
    } catch (e) {
      return Left(
        AppException(
          identifier: 'Fetch Chapter Index',
          statusCode: 0,
          message: e.toString(),
        ),
      );
    }
  }

  @override
  Future<Either<AppException, List<ContentIndexData>>> getPathIndex({
    required String level,
    required String chapter,
    required SectionType section,
  }) async {
    try {
      final DocumentSnapshot<Map<String, dynamic>> doc = await dataSource
          .fireStore
          .collection('course-content-index')
          .doc(level)
          .collection('chapters')
          .doc(chapter)
          .collection('section')
          .doc(section.name)
          .get();

      List<ContentIndexData> result = (doc.data()!['data'] as List)
          .map(
            (item) => ContentIndexData.fromJson(item as Map<String, dynamic>),
          )
          .toList();

      return Right<AppException, List<ContentIndexData>>(result);
    } catch (e) {
      return Left(
        AppException(
          identifier: 'Fetch Main Content Index',
          statusCode: 0,
          message: e.toString(),
        ),
      );
    }
  }

  @override
  Future<Either<AppException, PronunciationSubPart>> getPronunciation({
    required String path,
  }) async {
    try {
      final a = await dataSource.fireStore
          .collection('newcourse-content')
          .doc(path)
          .get();

      PronunciationSubPart data = PronunciationSubPart.fromJson(a.data()!);

      return Right(data);
    } catch (e) {
      return Left(
        AppException(
          identifier: 'fetch content data',
          statusCode: 0,
          message: e.toString(),
        ),
      );
    }
  }

  @override
  Future<Either<AppException, ConversationPart>> getConversation({
    required String path,
  }) async {
    try {
      final a = await dataSource.fireStore
          .collection('newcourse-content')
          .doc(path)
          .get();
      ConversationPart data = ConversationPart.fromJson(a.data()!);
      return Right(data);
    } catch (e) {
      return Left(
        AppException(
          identifier: 'fetch content data',
          statusCode: 0,
          message: e.toString(),
        ),
      );
    }
  }

  @override
  Future<Either<AppException, ListeningPart>> getListening({
    required String path,
  }) async {
    try {
      final a = await dataSource.fireStore
          .collection('newcourse-content')
          .doc(path)
          .get();
      ListeningPart data = ListeningPart.fromJson(a.data()!);
      return Right(data);
    } catch (e) {
      return Left(
        AppException(
          identifier: 'fetch content data',
          statusCode: 0,
          message: e.toString(),
        ),
      );
    }
  }

  @override
  Future<Either<AppException, SpeakingPart>> getSpeaking({
    required String path,
  }) async {
    try {
      final a = await dataSource.fireStore
          .collection('newcourse-content')
          .doc(path)
          .get();
      SpeakingPart data = SpeakingPart.fromJson(a.data()!);
      return Right(data);
    } catch (e) {
      return Left(
        AppException(
          identifier: 'fetch content data',
          statusCode: 0,
          message: e.toString(),
        ),
      );
    }
  }

  @override
  Future<Either<AppException, List<ConversationPart>>> getConversationList(
    List<String> paths,
  ) async {
    try {
      final futures = paths.map(
        (path) => dataSource.fireStore
            .collection('newcourse-content')
            .doc(path)
            .get(),
      );

      final snapshots = await Future.wait(futures);

      final docs = snapshots.map((doc) {
        final docData = doc.data()!;
        docData['video_controller'] = docData['video_url'];
        return docData;
      }).toList();

      final conversationParts = docs.map(ConversationPart.fromJson).toList();

      return Right(conversationParts);
    } catch (e) {
      return Left(
        AppException(
          identifier: 'fetch conversation list',
          statusCode: 0,
          message: e.toString(),
        ),
      );
    }
  }

  @override
  Future<Either<AppException, List<ListeningPart>>> getListeningList(
    List<String> paths,
  ) async {
    try {
      final futures = paths.map(
        (path) => dataSource.fireStore
            .collection('newcourse-content')
            .doc(path)
            .get(),
      );

      final snapshots = await Future.wait(futures);

      final docs = snapshots.map((doc) {
        final docData = doc.data()!;
        return docData;
      }).toList();

      final listeningParts = docs.map(ListeningPart.fromJson).toList();

      return Right(listeningParts);
    } catch (e) {
      return Left(
        AppException(
          identifier: 'fetch listening list',
          statusCode: 0,
          message: e.toString(),
        ),
      );
    }
  }

  @override
  Future<Either<AppException, List<SpeakingPart>>> getSpeakingList(
    List<String> paths,
  ) async {
    try {
      final futures = paths.map(
        (path) => dataSource.fireStore
            .collection('newcourse-content')
            .doc(path)
            .get(),
      );

      final snapshots = await Future.wait(futures);

      final docs = snapshots.map((doc) {
        final docData = doc.data()!;
        return docData;
      }).toList();

      final speakingParts = docs.map(SpeakingPart.fromJson).toList();

      return Right(speakingParts);
    } catch (e) {
      return Left(
        AppException(
          identifier: 'fetch speaking list',
          statusCode: 0,
          message: e.toString(),
        ),
      );
    }
  }

  @override
  Future<Either<AppException, List<ChapterData>>> getChapters(
    Level level,
  ) async {
    try {
      final snapshot = await dataSource.fireStore
          .collection('newcourse-content')
          .doc(level.name)
          .collection('chapters')
          .orderBy('chapter')
          .get();

      final docs = snapshot.docs
          .map((doc) => ChapterData.fromJson(doc.data()))
          .toList();

      return Right(docs);
    } catch (e) {
      return Left(
        AppException(
          identifier: 'Fetch chapters data',
          statusCode: 0,
          message: e.toString(),
        ),
      );
    }
  }

  @override
  Future<Either<AppException, ChapterData>> getChapterData(
    String level,
    String chapter,
  ) async {
    try {
      final doc = await dataSource.fireStore
          .collection('newcourse-content')
          .doc(level)
          .collection('chapters')
          .where('chapter', isEqualTo: int.parse(chapter))
          .get();

      if (doc.docs.isNotEmpty) {
        final docData = doc.docs.first.data();
        return Right(ChapterData.fromJson(docData));
      } else {
        return Left(
          AppException(
            identifier: 'Fetch chapter data',
            statusCode: 0,
            message: 'Chapter not found',
          ),
        );
      }
    } catch (e) {
      return Left(
        AppException(
          identifier: 'Fetch chapter data',
          statusCode: 0,
          message: e.toString(),
        ),
      );
    }
  }

  @override
  Future<Either<AppException, dynamic>> saveAnswer(answer) async {
    try {
      answer.forEach(
        (item) => dataSource
            .dataUser()
            .collection('testResult')
            .doc('selfAssesment')
            .collection('answers')
            .add(item),
      );
      return const Right(null);
    } catch (e) {
      return Left(
        AppException(
          identifier: 'save questionnaire',
          statusCode: 0,
          message: e.toString(),
        ),
      );
    }
  }

  @override
  Future<Either<AppException, dynamic>> uploadAudio({
    required String path,
  }) async {
    try {
      File file = File(path);
      String refPath = 'audio/temp/${path.split('/').last}';
      final audioRef = dataSource.firebaseStorage.ref().child(refPath);
      await audioRef.putFile(file);
      String url = await audioRef.getDownloadURL();
      return Right(AudioPath(path: path, refPath: refPath, url: url));
    } catch (e) {
      return Left(
        AppException(
          identifier: 'upload audio',
          statusCode: 0,
          message: e.toString(),
        ),
      );
    }
  }

  // Helper method for non-blocking file deletion
  Future<void> _deleteAudioFileInBackground(File file) async {
    try {
      if (await file.exists()) {
        await file.delete();
        // Optional: Log successful deletion, e.g., using a logger or print statement
        // print('File ${file.path} deleted successfully in background.');
      } else {
        // Optional: Log file not found for deletion
        // print('File ${file.path} not found for background deletion (already deleted or never existed).');
      }
    } catch (deleteError) {
      // Log this error. It should not affect the application's main flow.
      // In a real application, use a proper logging mechanism.
      print('Error deleting file ${file.path} in background: $deleteError');
    }
  }

  @override
  Future<Either<AppException, PronunciationResultFormatted>>
  checkPronunciation({
    required AudioPath audio,
    required String text,
    bool autoDelete = true,
  }) async {
    File file = File(audio.path);

    try {
      // 1. Check if the file exists before attempting to read it.
      if (!await file.exists()) {
        return Left(
          AppException(
            identifier: 'checkPronunciation - file_not_found',
            statusCode: 0, // Consider a specific status code for file not found
            message: 'Audio file not found at path: ${audio.path}',
          ),
        );
      }

      // Read file bytes asynchronously.
      final List<int> audioBytes = await file.readAsBytes();
      final String base64Audio = base64Encode(audioBytes);

      // Call the Firebase Function.
      final result = await dataSource.firebaseFunctions
          .httpsCallable('checkPronunciationViaBuffer')
          .call({'audioData': base64Audio, 'text': text});

      // If the Firebase function call is successful, handle auto-deletion.
      // The deletion process is made non-blocking.
      if (autoDelete) {
        _deleteAudioFileInBackground(file); // Fire and forget this future.
      }

      // Parse and return the successful result.
      return Right(
        PronunciationResultFormatted.fromJson(
          Map<String, dynamic>.from(deepCast(result.data)),
        ),
      );
    } catch (e) {
      // This block catches errors from file operations (exists, readAsBytes),
      // Firebase function call, or result parsing.

      // If autoDelete is true, attempt to clean up the file even if an error occurred
      // during the main process (e.g., API call failed after file was read).
      if (autoDelete) {
        _deleteAudioFileInBackground(file); // Fire and forget this future.
      }

      return Left(
        AppException(
          identifier: 'failed check pronunciation assesment',
          statusCode: 0,
          message: 'Please try again, we are unable to hear your voice.',
          // For debugging purposes, you might want to log 'e.toString()' or 'e.runtimeType'
          // on the server or via a developer logging mechanism.
        ),
      );
    }
  }

  @override
  Future<Either<AppException, ContentIndexData>> getFirstLesson() async {
    try {
      final result = await dataSource.fireStore
          .collection('course-content-index')
          .doc(Level.a1.name)
          .collection('chapters')
          .doc('1')
          .collection('section')
          .doc(SectionType.pronunciation.name)
          .get();

      final docData = ContentIndexData.fromJson(result.data()!['data'][0]);

      return Right(docData);
    } catch (e) {
      return Left(
        AppException(
          identifier: 'failed get first lesson index',
          statusCode: 0,
          message: 'Unable to fetch the first lesson index',
        ),
      );
    }
  }
}
