// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'main_lesson.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_ChapterIndexData _$ChapterIndexDataFromJson(Map<String, dynamic> json) =>
    _ChapterIndexData(
      chapter: (json['chapter'] as num).toInt(),
      level: json['level'] as String,
      path: json['path'] as String,
    );

Map<String, dynamic> _$ChapterIndexDataToJson(_ChapterIndexData instance) =>
    <String, dynamic>{
      'chapter': instance.chapter,
      'level': instance.level,
      'path': instance.path,
    };

_ContentIndexData _$ContentIndexDataFromJson(Map<String, dynamic> json) =>
    _ContentIndexData(
      title: json['content_title'] as String? ?? '',
      contentPath: json['content_path'] as String? ?? '',
      contentOrder: (json['content_order'] as num?)?.toInt() ?? 1,
      partOrder: (json['part_order'] as num?)?.toInt(),
      partTitle: json['part_title'] as String?,
      subpartOrder: (json['subpart_order'] as num?)?.toInt(),
      subpartTitle: json['subpart_title'] as String?,
      hasResult: json['hasResult'] as bool? ?? false,
      isBookmarked: json['isBookmarked'] as bool? ?? false,
      firstStage: json['firstStage'] as bool? ?? false,
      secondStage: json['secondStage'] as bool? ?? false,
      thirdStage: json['thirdStage'] as bool? ?? false,
    );

Map<String, dynamic> _$ContentIndexDataToJson(_ContentIndexData instance) =>
    <String, dynamic>{
      'content_title': instance.title,
      'content_path': instance.contentPath,
      'content_order': instance.contentOrder,
      'part_order': instance.partOrder,
      'part_title': instance.partTitle,
      'subpart_order': instance.subpartOrder,
      'subpart_title': instance.subpartTitle,
      'hasResult': instance.hasResult,
      'isBookmarked': instance.isBookmarked,
      'firstStage': instance.firstStage,
      'secondStage': instance.secondStage,
      'thirdStage': instance.thirdStage,
    };

_MainLesson _$MainLessonFromJson(Map<String, dynamic> json) => _MainLesson(
  state:
      $enumDecodeNullable(_$QuestionConcreteStateEnumMap, json['state']) ??
      QuestionConcreteState.initial,
  questionId: json['question_id'] as String?,
  order: (json['order'] as num?)?.toInt() ?? 0,
  correctAnswer: json['correct_answer'] as String? ?? '',
  question: json['question'] as String? ?? '',
  answer: json['answer'] as String? ?? '',
  isCorrect: json['is_correct'] as bool? ?? false,
);

Map<String, dynamic> _$MainLessonToJson(_MainLesson instance) =>
    <String, dynamic>{
      'question_id': instance.questionId,
      'answer': instance.answer,
      'is_correct': instance.isCorrect,
    };

const _$QuestionConcreteStateEnumMap = {
  QuestionConcreteState.initial: 'initial',
  QuestionConcreteState.answered: 'answered',
};

_PronunciationSubPart _$PronunciationSubPartFromJson(
  Map<String, dynamic> json,
) => _PronunciationSubPart(
  audio: json['audio_url'] as String,
  image: json['image_url'] as String,
  caption: json['caption'] as String,
  translation: json['translation'] as String,
  description: json['description'] as String?,
  order: (json['order'] as num).toInt(),
  isBookmarked: json['isBookmarked'] as bool? ?? false,
);

Map<String, dynamic> _$PronunciationSubPartToJson(
  _PronunciationSubPart instance,
) => <String, dynamic>{
  'audio_url': instance.audio,
  'image_url': instance.image,
  'caption': instance.caption,
  'translation': instance.translation,
  'description': instance.description,
  'order': instance.order,
  'isBookmarked': instance.isBookmarked,
};

_ConversationPart _$ConversationPartFromJson(Map<String, dynamic> json) =>
    _ConversationPart(
      order: (json['order'] as num?)?.toInt(),
      title: json['title'] as String?,
      video: json['video_url'] as String?,
      subtitles: (json['subtitle'] as List<dynamic>?)
          ?.map((e) => Subtitle.fromJson(e as Map<String, dynamic>))
          .toList(),
      videoController: videoControllerFromJson(json['video_controller']),
      videoMeta: json['video_meta'] == null
          ? null
          : VideoMeta.fromJson(json['video_meta'] as Map<String, dynamic>),
      image: json['img_url'] as String?,
      isBookmarked: json['isBookmarked'] as bool? ?? false,
    );

Map<String, dynamic> _$ConversationPartToJson(_ConversationPart instance) =>
    <String, dynamic>{
      'order': instance.order,
      'title': instance.title,
      'video_url': instance.video,
      'subtitle': instance.subtitles,
      'video_controller': instance.videoController,
      'video_meta': instance.videoMeta,
      'img_url': instance.image,
      'isBookmarked': instance.isBookmarked,
    };

_VideoMeta _$VideoMetaFromJson(Map<String, dynamic> json) => _VideoMeta(
  duration: durationFromJson(json['duration']),
  isPlayed: json['isPlayed'] as bool? ?? false,
);

Map<String, dynamic> _$VideoMetaToJson(_VideoMeta instance) =>
    <String, dynamic>{
      'duration': instance.duration?.inMicroseconds,
      'isPlayed': instance.isPlayed,
    };

_Subtitle _$SubtitleFromJson(Map<String, dynamic> json) => _Subtitle(
  lang: json['lang'] as String? ?? '',
  subtitle: json['subtitle'] as String? ?? '',
  subtitleUrl: json['subtitle_url'] as String? ?? '',
  isPlayed: json['isPlayed'] as bool? ?? false,
);

Map<String, dynamic> _$SubtitleToJson(_Subtitle instance) => <String, dynamic>{
  'lang': instance.lang,
  'subtitle': instance.subtitle,
  'subtitle_url': instance.subtitleUrl,
  'isPlayed': instance.isPlayed,
};

_AudioPath _$AudioPathFromJson(Map<String, dynamic> json) => _AudioPath(
  path: json['path'] as String? ?? '',
  refPath: json['refPath'] as String? ?? '',
  url: json['url'] as String? ?? '',
);

Map<String, dynamic> _$AudioPathToJson(_AudioPath instance) =>
    <String, dynamic>{
      'path': instance.path,
      'refPath': instance.refPath,
      'url': instance.url,
    };

_ListeningPart _$ListeningPartFromJson(Map<String, dynamic> json) =>
    _ListeningPart(
      main: json['audio_url'] as String?,
      order: (json['order'] as num?)?.toInt(),
      title: json['title'] as String?,
      image: json['image_url'] as String?,
      isBookmarked: json['isBookmarked'] as bool? ?? false,
      questions:
          (json['questions'] as List<dynamic>?)
              ?.map((e) => Question.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
    );

Map<String, dynamic> _$ListeningPartToJson(_ListeningPart instance) =>
    <String, dynamic>{
      'audio_url': instance.main,
      'order': instance.order,
      'title': instance.title,
      'image_url': instance.image,
      'isBookmarked': instance.isBookmarked,
      'questions': instance.questions,
    };

_Question _$QuestionFromJson(Map<String, dynamic> json) => _Question(
  collapse: json['collapse'] as bool?,
  order: (json['order'] as num?)?.toInt(),
  question: json['question'] as String?,
  answer: json['answer'] as String?,
  isCorrect: json['isCorrect'] as bool?,
  choices:
      (json['choices'] as List<dynamic>?)
          ?.map((e) => Choice.fromJson(e as Map<String, dynamic>))
          .toList() ??
      const [],
);

Map<String, dynamic> _$QuestionToJson(_Question instance) => <String, dynamic>{
  'collapse': instance.collapse,
  'order': instance.order,
  'question': instance.question,
  'answer': instance.answer,
  'isCorrect': instance.isCorrect,
  'choices': instance.choices,
};

_Choice _$ChoiceFromJson(Map<String, dynamic> json) => _Choice(
  isCorrect: json['is_correct'] as bool?,
  text: json['text'] as String?,
);

Map<String, dynamic> _$ChoiceToJson(_Choice instance) => <String, dynamic>{
  'is_correct': instance.isCorrect,
  'text': instance.text,
};

_SpeakingPart _$SpeakingPartFromJson(Map<String, dynamic> json) =>
    _SpeakingPart(
      answer: Answer.fromJson(json['answer'] as Map<String, dynamic>),
      question: Answer.fromJson(json['question'] as Map<String, dynamic>),
      image: json['image_url'] as String,
      imageB: json['imageB_url'] as String,
      order: (json['order'] as num).toInt(),
      title: json['title'] as String,
      isBookmarked: json['isBookmarked'] as bool? ?? false,
    );

Map<String, dynamic> _$SpeakingPartToJson(_SpeakingPart instance) =>
    <String, dynamic>{
      'answer': instance.answer,
      'question': instance.question,
      'image_url': instance.image,
      'imageB_url': instance.imageB,
      'order': instance.order,
      'title': instance.title,
      'isBookmarked': instance.isBookmarked,
    };

_Answer _$AnswerFromJson(Map<String, dynamic> json) => _Answer(
  audio: json['audio_url'] as String,
  text: json['text'] as String,
  translation: json['translation'] as String? ?? '',
  isActive: json['isActive'] as bool? ?? false,
);

Map<String, dynamic> _$AnswerToJson(_Answer instance) => <String, dynamic>{
  'audio_url': instance.audio,
  'text': instance.text,
  'translation': instance.translation,
  'isActive': instance.isActive,
};
