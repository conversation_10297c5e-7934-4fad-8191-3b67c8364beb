// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'chapter_title_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, type=warning

@ProviderFor(ChapterTitleController)
const chapterTitleControllerProvider = ChapterTitleControllerFamily._();

final class ChapterTitleControllerProvider
    extends $AsyncNotifierProvider<ChapterTitleController, ChapterTitleState> {
  const ChapterTitleControllerProvider._({
    required ChapterTitleControllerFamily super.from,
    required (String, String) super.argument,
  }) : super(
         retry: null,
         name: r'chapterTitleControllerProvider',
         isAutoDispose: true,
         dependencies: null,
         $allTransitiveDependencies: null,
       );

  @override
  String debugGetCreateSourceHash() => _$chapterTitleControllerHash();

  @override
  String toString() {
    return r'chapterTitleControllerProvider'
        ''
        '$argument';
  }

  @$internal
  @override
  ChapterTitleController create() => ChapterTitleController();

  @override
  bool operator ==(Object other) {
    return other is ChapterTitleControllerProvider &&
        other.argument == argument;
  }

  @override
  int get hashCode {
    return argument.hashCode;
  }
}

String _$chapterTitleControllerHash() =>
    r'1cc64644a36ee790bacae436a2bd73c0a5edfc9f';

final class ChapterTitleControllerFamily extends $Family
    with
        $ClassFamilyOverride<
          ChapterTitleController,
          AsyncValue<ChapterTitleState>,
          ChapterTitleState,
          FutureOr<ChapterTitleState>,
          (String, String)
        > {
  const ChapterTitleControllerFamily._()
    : super(
        retry: null,
        name: r'chapterTitleControllerProvider',
        dependencies: null,
        $allTransitiveDependencies: null,
        isAutoDispose: true,
      );

  ChapterTitleControllerProvider call(String level, String chapter) =>
      ChapterTitleControllerProvider._(argument: (level, chapter), from: this);

  @override
  String toString() => r'chapterTitleControllerProvider';
}

abstract class _$ChapterTitleController
    extends $AsyncNotifier<ChapterTitleState> {
  late final _$args = ref.$arg as (String, String);
  String get level => _$args.$1;
  String get chapter => _$args.$2;

  FutureOr<ChapterTitleState> build(String level, String chapter);
  @$mustCallSuper
  @override
  void runBuild() {
    final created = build(_$args.$1, _$args.$2);
    final ref =
        this.ref as $Ref<AsyncValue<ChapterTitleState>, ChapterTitleState>;
    final element =
        ref.element
            as $ClassProviderElement<
              AnyNotifier<AsyncValue<ChapterTitleState>, ChapterTitleState>,
              AsyncValue<ChapterTitleState>,
              Object?,
              Object?
            >;
    element.handleValue(ref, created);
  }
}
