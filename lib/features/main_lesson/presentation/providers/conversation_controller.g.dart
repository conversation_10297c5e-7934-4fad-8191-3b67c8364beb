// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'conversation_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, type=warning
/// This controller is an [AsyncNotifier] that holds and handles our authentication state

@ProviderFor(ConversationController)
const conversationControllerProvider = ConversationControllerFamily._();

/// This controller is an [AsyncNotifier] that holds and handles our authentication state
final class ConversationControllerProvider
    extends $AsyncNotifierProvider<ConversationController, ConversationState> {
  /// This controller is an [AsyncNotifier] that holds and handles our authentication state
  const ConversationControllerProvider._({
    required ConversationControllerFamily super.from,
    required (String, String, String, String) super.argument,
  }) : super(
         retry: null,
         name: r'conversationControllerProvider',
         isAutoDispose: true,
         dependencies: null,
         $allTransitiveDependencies: null,
       );

  @override
  String debugGetCreateSourceHash() => _$conversationControllerHash();

  @override
  String toString() {
    return r'conversationControllerProvider'
        ''
        '$argument';
  }

  @$internal
  @override
  ConversationController create() => ConversationController();

  @override
  bool operator ==(Object other) {
    return other is ConversationControllerProvider &&
        other.argument == argument;
  }

  @override
  int get hashCode {
    return argument.hashCode;
  }
}

String _$conversationControllerHash() =>
    r'c2c4842460643586ed48b58edf4e8bb00846244f';

/// This controller is an [AsyncNotifier] that holds and handles our authentication state

final class ConversationControllerFamily extends $Family
    with
        $ClassFamilyOverride<
          ConversationController,
          AsyncValue<ConversationState>,
          ConversationState,
          FutureOr<ConversationState>,
          (String, String, String, String)
        > {
  const ConversationControllerFamily._()
    : super(
        retry: null,
        name: r'conversationControllerProvider',
        dependencies: null,
        $allTransitiveDependencies: null,
        isAutoDispose: true,
      );

  /// This controller is an [AsyncNotifier] that holds and handles our authentication state

  ConversationControllerProvider call(
    String level,
    String chapter,
    String path,
    String localName,
  ) => ConversationControllerProvider._(
    argument: (level, chapter, path, localName),
    from: this,
  );

  @override
  String toString() => r'conversationControllerProvider';
}

/// This controller is an [AsyncNotifier] that holds and handles our authentication state

abstract class _$ConversationController
    extends $AsyncNotifier<ConversationState> {
  late final _$args = ref.$arg as (String, String, String, String);
  String get level => _$args.$1;
  String get chapter => _$args.$2;
  String get path => _$args.$3;
  String get localName => _$args.$4;

  FutureOr<ConversationState> build(
    String level,
    String chapter,
    String path,
    String localName,
  );
  @$mustCallSuper
  @override
  void runBuild() {
    final created = build(_$args.$1, _$args.$2, _$args.$3, _$args.$4);
    final ref =
        this.ref as $Ref<AsyncValue<ConversationState>, ConversationState>;
    final element =
        ref.element
            as $ClassProviderElement<
              AnyNotifier<AsyncValue<ConversationState>, ConversationState>,
              AsyncValue<ConversationState>,
              Object?,
              Object?
            >;
    element.handleValue(ref, created);
  }
}
