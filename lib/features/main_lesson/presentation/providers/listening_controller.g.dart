// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'listening_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, type=warning

@ProviderFor(ListeningController)
const listeningControllerProvider = ListeningControllerFamily._();

final class ListeningControllerProvider
    extends $AsyncNotifierProvider<ListeningController, ListeningState> {
  const ListeningControllerProvider._({
    required ListeningControllerFamily super.from,
    required (String, String, String) super.argument,
  }) : super(
         retry: null,
         name: r'listeningControllerProvider',
         isAutoDispose: true,
         dependencies: null,
         $allTransitiveDependencies: null,
       );

  @override
  String debugGetCreateSourceHash() => _$listeningControllerHash();

  @override
  String toString() {
    return r'listeningControllerProvider'
        ''
        '$argument';
  }

  @$internal
  @override
  ListeningController create() => ListeningController();

  @override
  bool operator ==(Object other) {
    return other is ListeningControllerProvider && other.argument == argument;
  }

  @override
  int get hashCode {
    return argument.hashCode;
  }
}

String _$listeningControllerHash() =>
    r'8cb90ffe953c839f5e9778c9fd5887cbf8c854cb';

final class ListeningControllerFamily extends $Family
    with
        $ClassFamilyOverride<
          ListeningController,
          AsyncValue<ListeningState>,
          ListeningState,
          FutureOr<ListeningState>,
          (String, String, String)
        > {
  const ListeningControllerFamily._()
    : super(
        retry: null,
        name: r'listeningControllerProvider',
        dependencies: null,
        $allTransitiveDependencies: null,
        isAutoDispose: true,
      );

  ListeningControllerProvider call(String level, String chapter, String path) =>
      ListeningControllerProvider._(
        argument: (level, chapter, path),
        from: this,
      );

  @override
  String toString() => r'listeningControllerProvider';
}

abstract class _$ListeningController extends $AsyncNotifier<ListeningState> {
  late final _$args = ref.$arg as (String, String, String);
  String get level => _$args.$1;
  String get chapter => _$args.$2;
  String get path => _$args.$3;

  FutureOr<ListeningState> build(String level, String chapter, String path);
  @$mustCallSuper
  @override
  void runBuild() {
    final created = build(_$args.$1, _$args.$2, _$args.$3);
    final ref = this.ref as $Ref<AsyncValue<ListeningState>, ListeningState>;
    final element =
        ref.element
            as $ClassProviderElement<
              AnyNotifier<AsyncValue<ListeningState>, ListeningState>,
              AsyncValue<ListeningState>,
              Object?,
              Object?
            >;
    element.handleValue(ref, created);
  }
}
