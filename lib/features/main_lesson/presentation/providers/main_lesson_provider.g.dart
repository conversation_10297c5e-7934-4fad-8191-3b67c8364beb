// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'main_lesson_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, type=warning

@ProviderFor(MainLessonNotifier)
const mainLessonProvider = MainLessonNotifierProvider._();

final class MainLessonNotifierProvider
    extends $NotifierProvider<MainLessonNotifier, MainLessonState> {
  const MainLessonNotifierProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'mainLessonProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$mainLessonNotifierHash();

  @$internal
  @override
  MainLessonNotifier create() => MainLessonNotifier();

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(MainLessonState value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<MainLessonState>(value),
    );
  }
}

String _$mainLessonNotifierHash() =>
    r'0277141e6ecc4c28a39fd9c7e6e2c59e8b624795';

abstract class _$MainLessonNotifier extends $Notifier<MainLessonState> {
  MainLessonState build();
  @$mustCallSuper
  @override
  void runBuild() {
    final created = build();
    final ref = this.ref as $Ref<MainLessonState, MainLessonState>;
    final element =
        ref.element
            as $ClassProviderElement<
              AnyNotifier<MainLessonState, MainLessonState>,
              MainLessonState,
              Object?,
              Object?
            >;
    element.handleValue(ref, created);
  }
}
