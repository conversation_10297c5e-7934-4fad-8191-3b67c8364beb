// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'pronunciation_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, type=warning
/// This controller is an [AsyncNotifier] that holds and handles our authentication state

@ProviderFor(PronunciationController)
const pronunciationControllerProvider = PronunciationControllerFamily._();

/// This controller is an [AsyncNotifier] that holds and handles our authentication state
final class PronunciationController<PERSON>rovider
    extends
        $AsyncNotifierProvider<PronunciationController, PronunciationState> {
  /// This controller is an [AsyncNotifier] that holds and handles our authentication state
  const PronunciationControllerProvider._({
    required PronunciationControllerFamily super.from,
    required (String, String, String) super.argument,
  }) : super(
         retry: null,
         name: r'pronunciationControllerProvider',
         isAutoDispose: true,
         dependencies: null,
         $allTransitiveDependencies: null,
       );

  @override
  String debugGetCreateSourceHash() => _$pronunciationControllerHash();

  @override
  String toString() {
    return r'pronunciationControllerProvider'
        ''
        '$argument';
  }

  @$internal
  @override
  PronunciationController create() => PronunciationController();

  @override
  bool operator ==(Object other) {
    return other is PronunciationControllerProvider &&
        other.argument == argument;
  }

  @override
  int get hashCode {
    return argument.hashCode;
  }
}

String _$pronunciationControllerHash() =>
    r'6d0b1242d892b706f9a075a74fd3a47e4ff1655d';

/// This controller is an [AsyncNotifier] that holds and handles our authentication state

final class PronunciationControllerFamily extends $Family
    with
        $ClassFamilyOverride<
          PronunciationController,
          AsyncValue<PronunciationState>,
          PronunciationState,
          FutureOr<PronunciationState>,
          (String, String, String)
        > {
  const PronunciationControllerFamily._()
    : super(
        retry: null,
        name: r'pronunciationControllerProvider',
        dependencies: null,
        $allTransitiveDependencies: null,
        isAutoDispose: true,
      );

  /// This controller is an [AsyncNotifier] that holds and handles our authentication state

  PronunciationControllerProvider call(
    String level,
    String chapter,
    String path,
  ) => PronunciationControllerProvider._(
    argument: (level, chapter, path),
    from: this,
  );

  @override
  String toString() => r'pronunciationControllerProvider';
}

/// This controller is an [AsyncNotifier] that holds and handles our authentication state

abstract class _$PronunciationController
    extends $AsyncNotifier<PronunciationState> {
  late final _$args = ref.$arg as (String, String, String);
  String get level => _$args.$1;
  String get chapter => _$args.$2;
  String get path => _$args.$3;

  FutureOr<PronunciationState> build(String level, String chapter, String path);
  @$mustCallSuper
  @override
  void runBuild() {
    final created = build(_$args.$1, _$args.$2, _$args.$3);
    final ref =
        this.ref as $Ref<AsyncValue<PronunciationState>, PronunciationState>;
    final element =
        ref.element
            as $ClassProviderElement<
              AnyNotifier<AsyncValue<PronunciationState>, PronunciationState>,
              AsyncValue<PronunciationState>,
              Object?,
              Object?
            >;
    element.handleValue(ref, created);
  }
}
