// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'speaking_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, type=warning
/// This controller is an [AsyncNotifier] that holds and handles our speaking state

@ProviderFor(SpeakingController)
const speakingControllerProvider = SpeakingControllerFamily._();

/// This controller is an [AsyncNotifier] that holds and handles our speaking state
final class SpeakingControllerProvider
    extends $AsyncNotifierProvider<SpeakingController, SpeakingState> {
  /// This controller is an [AsyncNotifier] that holds and handles our speaking state
  const SpeakingControllerProvider._({
    required SpeakingControllerFamily super.from,
    required (String, String, String, SpeakingStage?) super.argument,
  }) : super(
         retry: null,
         name: r'speakingControllerProvider',
         isAutoDispose: true,
         dependencies: null,
         $allTransitiveDependencies: null,
       );

  @override
  String debugGetCreateSourceHash() => _$speakingControllerHash();

  @override
  String toString() {
    return r'speakingControllerProvider'
        ''
        '$argument';
  }

  @$internal
  @override
  SpeakingController create() => SpeakingController();

  @override
  bool operator ==(Object other) {
    return other is SpeakingControllerProvider && other.argument == argument;
  }

  @override
  int get hashCode {
    return argument.hashCode;
  }
}

String _$speakingControllerHash() =>
    r'4bf449784910b2debb796c19e5d3bb4f2bdd64c3';

/// This controller is an [AsyncNotifier] that holds and handles our speaking state

final class SpeakingControllerFamily extends $Family
    with
        $ClassFamilyOverride<
          SpeakingController,
          AsyncValue<SpeakingState>,
          SpeakingState,
          FutureOr<SpeakingState>,
          (String, String, String, SpeakingStage?)
        > {
  const SpeakingControllerFamily._()
    : super(
        retry: null,
        name: r'speakingControllerProvider',
        dependencies: null,
        $allTransitiveDependencies: null,
        isAutoDispose: true,
      );

  /// This controller is an [AsyncNotifier] that holds and handles our speaking state

  SpeakingControllerProvider call(
    String level,
    String chapter,
    String path,
    SpeakingStage? stage,
  ) => SpeakingControllerProvider._(
    argument: (level, chapter, path, stage),
    from: this,
  );

  @override
  String toString() => r'speakingControllerProvider';
}

/// This controller is an [AsyncNotifier] that holds and handles our speaking state

abstract class _$SpeakingController extends $AsyncNotifier<SpeakingState> {
  late final _$args = ref.$arg as (String, String, String, SpeakingStage?);
  String get level => _$args.$1;
  String get chapter => _$args.$2;
  String get path => _$args.$3;
  SpeakingStage? get stage => _$args.$4;

  FutureOr<SpeakingState> build(
    String level,
    String chapter,
    String path,
    SpeakingStage? stage,
  );
  @$mustCallSuper
  @override
  void runBuild() {
    final created = build(_$args.$1, _$args.$2, _$args.$3, _$args.$4);
    final ref = this.ref as $Ref<AsyncValue<SpeakingState>, SpeakingState>;
    final element =
        ref.element
            as $ClassProviderElement<
              AnyNotifier<AsyncValue<SpeakingState>, SpeakingState>,
              AsyncValue<SpeakingState>,
              Object?,
              Object?
            >;
    element.handleValue(ref, created);
  }
}
