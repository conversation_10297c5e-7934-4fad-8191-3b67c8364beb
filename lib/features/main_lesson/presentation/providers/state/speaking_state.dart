import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:selfeng/features/diagnostic_test/domain/models/question_model.dart';
import 'package:selfeng/features/main_lesson/domain/models/main_lesson.dart';
import 'package:selfeng/shared/domain/models/pronunciation/pronunciation_result_formatted.dart';
import 'package:selfeng/shared/domain/models/user-data/user_data.dart';

part 'speaking_state.freezed.dart';

@freezed
abstract class SpeakingState with _$SpeakingState {
  factory SpeakingState({
    @Default([]) List<SpeakingPart> speakings,
    QuestionResultModel? result,
    @Default(0) int currentPage,
    @Default(false) bool expandedResult,
    AudioPath? audioPath,
    PronunciationResultFormatted? response,
    @Default(SpeakingStage.stage1) SpeakingStage speakingStage,
    @Default(0) int selectedIndex,
    @Default(false) bool nextSection,
    @Default(false) bool isLoading,
    @Default(false) bool isListening,
    @Default(false) bool isTranslate,
    @Default(0) int resultPronunciation,
    SpeakingAgregateScore? resultStage2,
    SpeakingAgregateScore? resultStage3,
    @Default(true) bool showStageOnboarding,
    @Default(false) bool isIntro,
  }) = _SpeakingState;

  // Allow custom getters / setters
  const SpeakingState._();

  bool get isQuestionTalking => speakings[selectedIndex].question.isActive;
  int get questionLength => speakings.length;
  bool get isResponse => response != null;
  bool get isRecord => !isResponse && !nextSection && !isLoading;
  String get audioUrl {
    String audioUrl = '';
    final currentSpeaking = speakings[selectedIndex];
    if (isQuestionTalking) {
      audioUrl = currentSpeaking.question.audio;
    } else {
      audioUrl = currentSpeaking.answer.audio;
    }
    return audioUrl;
  }

  /// Check if both stage 2 and stage 3 results are available and valid
  bool get hasValidStageResults => resultStage2 != null && resultStage3 != null;

  /// Check if both stage results have valid data counts (greater than 0)
  bool get hasNonEmptyStageResults =>
      hasValidStageResults &&
      resultStage2!.dataCount > 0 &&
      resultStage3!.dataCount > 0;
}
