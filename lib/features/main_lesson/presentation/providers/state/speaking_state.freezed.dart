// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'speaking_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$SpeakingState {

 List<SpeakingPart> get speakings; QuestionResultModel? get result; int get currentPage; bool get expandedResult; AudioPath? get audioPath; PronunciationResultFormatted? get response; SpeakingStage get speakingStage; int get selectedIndex; bool get nextSection; bool get isLoading; bool get isListening; bool get isTranslate; int get resultPronunciation; SpeakingAgregateScore? get resultStage2; SpeakingAgregateScore? get resultStage3; bool get showStageOnboarding; bool get isIntro;
/// Create a copy of SpeakingState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$SpeakingStateCopyWith<SpeakingState> get copyWith => _$SpeakingStateCopyWithImpl<SpeakingState>(this as SpeakingState, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SpeakingState&&const DeepCollectionEquality().equals(other.speakings, speakings)&&(identical(other.result, result) || other.result == result)&&(identical(other.currentPage, currentPage) || other.currentPage == currentPage)&&(identical(other.expandedResult, expandedResult) || other.expandedResult == expandedResult)&&(identical(other.audioPath, audioPath) || other.audioPath == audioPath)&&(identical(other.response, response) || other.response == response)&&(identical(other.speakingStage, speakingStage) || other.speakingStage == speakingStage)&&(identical(other.selectedIndex, selectedIndex) || other.selectedIndex == selectedIndex)&&(identical(other.nextSection, nextSection) || other.nextSection == nextSection)&&(identical(other.isLoading, isLoading) || other.isLoading == isLoading)&&(identical(other.isListening, isListening) || other.isListening == isListening)&&(identical(other.isTranslate, isTranslate) || other.isTranslate == isTranslate)&&(identical(other.resultPronunciation, resultPronunciation) || other.resultPronunciation == resultPronunciation)&&(identical(other.resultStage2, resultStage2) || other.resultStage2 == resultStage2)&&(identical(other.resultStage3, resultStage3) || other.resultStage3 == resultStage3)&&(identical(other.showStageOnboarding, showStageOnboarding) || other.showStageOnboarding == showStageOnboarding)&&(identical(other.isIntro, isIntro) || other.isIntro == isIntro));
}


@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(speakings),result,currentPage,expandedResult,audioPath,response,speakingStage,selectedIndex,nextSection,isLoading,isListening,isTranslate,resultPronunciation,resultStage2,resultStage3,showStageOnboarding,isIntro);

@override
String toString() {
  return 'SpeakingState(speakings: $speakings, result: $result, currentPage: $currentPage, expandedResult: $expandedResult, audioPath: $audioPath, response: $response, speakingStage: $speakingStage, selectedIndex: $selectedIndex, nextSection: $nextSection, isLoading: $isLoading, isListening: $isListening, isTranslate: $isTranslate, resultPronunciation: $resultPronunciation, resultStage2: $resultStage2, resultStage3: $resultStage3, showStageOnboarding: $showStageOnboarding, isIntro: $isIntro)';
}


}

/// @nodoc
abstract mixin class $SpeakingStateCopyWith<$Res>  {
  factory $SpeakingStateCopyWith(SpeakingState value, $Res Function(SpeakingState) _then) = _$SpeakingStateCopyWithImpl;
@useResult
$Res call({
 List<SpeakingPart> speakings, QuestionResultModel? result, int currentPage, bool expandedResult, AudioPath? audioPath, PronunciationResultFormatted? response, SpeakingStage speakingStage, int selectedIndex, bool nextSection, bool isLoading, bool isListening, bool isTranslate, int resultPronunciation, SpeakingAgregateScore? resultStage2, SpeakingAgregateScore? resultStage3, bool showStageOnboarding, bool isIntro
});


$QuestionResultModelCopyWith<$Res>? get result;$AudioPathCopyWith<$Res>? get audioPath;$PronunciationResultFormattedCopyWith<$Res>? get response;$SpeakingAgregateScoreCopyWith<$Res>? get resultStage2;$SpeakingAgregateScoreCopyWith<$Res>? get resultStage3;

}
/// @nodoc
class _$SpeakingStateCopyWithImpl<$Res>
    implements $SpeakingStateCopyWith<$Res> {
  _$SpeakingStateCopyWithImpl(this._self, this._then);

  final SpeakingState _self;
  final $Res Function(SpeakingState) _then;

/// Create a copy of SpeakingState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? speakings = null,Object? result = freezed,Object? currentPage = null,Object? expandedResult = null,Object? audioPath = freezed,Object? response = freezed,Object? speakingStage = null,Object? selectedIndex = null,Object? nextSection = null,Object? isLoading = null,Object? isListening = null,Object? isTranslate = null,Object? resultPronunciation = null,Object? resultStage2 = freezed,Object? resultStage3 = freezed,Object? showStageOnboarding = null,Object? isIntro = null,}) {
  return _then(_self.copyWith(
speakings: null == speakings ? _self.speakings : speakings // ignore: cast_nullable_to_non_nullable
as List<SpeakingPart>,result: freezed == result ? _self.result : result // ignore: cast_nullable_to_non_nullable
as QuestionResultModel?,currentPage: null == currentPage ? _self.currentPage : currentPage // ignore: cast_nullable_to_non_nullable
as int,expandedResult: null == expandedResult ? _self.expandedResult : expandedResult // ignore: cast_nullable_to_non_nullable
as bool,audioPath: freezed == audioPath ? _self.audioPath : audioPath // ignore: cast_nullable_to_non_nullable
as AudioPath?,response: freezed == response ? _self.response : response // ignore: cast_nullable_to_non_nullable
as PronunciationResultFormatted?,speakingStage: null == speakingStage ? _self.speakingStage : speakingStage // ignore: cast_nullable_to_non_nullable
as SpeakingStage,selectedIndex: null == selectedIndex ? _self.selectedIndex : selectedIndex // ignore: cast_nullable_to_non_nullable
as int,nextSection: null == nextSection ? _self.nextSection : nextSection // ignore: cast_nullable_to_non_nullable
as bool,isLoading: null == isLoading ? _self.isLoading : isLoading // ignore: cast_nullable_to_non_nullable
as bool,isListening: null == isListening ? _self.isListening : isListening // ignore: cast_nullable_to_non_nullable
as bool,isTranslate: null == isTranslate ? _self.isTranslate : isTranslate // ignore: cast_nullable_to_non_nullable
as bool,resultPronunciation: null == resultPronunciation ? _self.resultPronunciation : resultPronunciation // ignore: cast_nullable_to_non_nullable
as int,resultStage2: freezed == resultStage2 ? _self.resultStage2 : resultStage2 // ignore: cast_nullable_to_non_nullable
as SpeakingAgregateScore?,resultStage3: freezed == resultStage3 ? _self.resultStage3 : resultStage3 // ignore: cast_nullable_to_non_nullable
as SpeakingAgregateScore?,showStageOnboarding: null == showStageOnboarding ? _self.showStageOnboarding : showStageOnboarding // ignore: cast_nullable_to_non_nullable
as bool,isIntro: null == isIntro ? _self.isIntro : isIntro // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}
/// Create a copy of SpeakingState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$QuestionResultModelCopyWith<$Res>? get result {
    if (_self.result == null) {
    return null;
  }

  return $QuestionResultModelCopyWith<$Res>(_self.result!, (value) {
    return _then(_self.copyWith(result: value));
  });
}/// Create a copy of SpeakingState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$AudioPathCopyWith<$Res>? get audioPath {
    if (_self.audioPath == null) {
    return null;
  }

  return $AudioPathCopyWith<$Res>(_self.audioPath!, (value) {
    return _then(_self.copyWith(audioPath: value));
  });
}/// Create a copy of SpeakingState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$PronunciationResultFormattedCopyWith<$Res>? get response {
    if (_self.response == null) {
    return null;
  }

  return $PronunciationResultFormattedCopyWith<$Res>(_self.response!, (value) {
    return _then(_self.copyWith(response: value));
  });
}/// Create a copy of SpeakingState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$SpeakingAgregateScoreCopyWith<$Res>? get resultStage2 {
    if (_self.resultStage2 == null) {
    return null;
  }

  return $SpeakingAgregateScoreCopyWith<$Res>(_self.resultStage2!, (value) {
    return _then(_self.copyWith(resultStage2: value));
  });
}/// Create a copy of SpeakingState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$SpeakingAgregateScoreCopyWith<$Res>? get resultStage3 {
    if (_self.resultStage3 == null) {
    return null;
  }

  return $SpeakingAgregateScoreCopyWith<$Res>(_self.resultStage3!, (value) {
    return _then(_self.copyWith(resultStage3: value));
  });
}
}


/// Adds pattern-matching-related methods to [SpeakingState].
extension SpeakingStatePatterns on SpeakingState {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _SpeakingState value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _SpeakingState() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _SpeakingState value)  $default,){
final _that = this;
switch (_that) {
case _SpeakingState():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _SpeakingState value)?  $default,){
final _that = this;
switch (_that) {
case _SpeakingState() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( List<SpeakingPart> speakings,  QuestionResultModel? result,  int currentPage,  bool expandedResult,  AudioPath? audioPath,  PronunciationResultFormatted? response,  SpeakingStage speakingStage,  int selectedIndex,  bool nextSection,  bool isLoading,  bool isListening,  bool isTranslate,  int resultPronunciation,  SpeakingAgregateScore? resultStage2,  SpeakingAgregateScore? resultStage3,  bool showStageOnboarding,  bool isIntro)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _SpeakingState() when $default != null:
return $default(_that.speakings,_that.result,_that.currentPage,_that.expandedResult,_that.audioPath,_that.response,_that.speakingStage,_that.selectedIndex,_that.nextSection,_that.isLoading,_that.isListening,_that.isTranslate,_that.resultPronunciation,_that.resultStage2,_that.resultStage3,_that.showStageOnboarding,_that.isIntro);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( List<SpeakingPart> speakings,  QuestionResultModel? result,  int currentPage,  bool expandedResult,  AudioPath? audioPath,  PronunciationResultFormatted? response,  SpeakingStage speakingStage,  int selectedIndex,  bool nextSection,  bool isLoading,  bool isListening,  bool isTranslate,  int resultPronunciation,  SpeakingAgregateScore? resultStage2,  SpeakingAgregateScore? resultStage3,  bool showStageOnboarding,  bool isIntro)  $default,) {final _that = this;
switch (_that) {
case _SpeakingState():
return $default(_that.speakings,_that.result,_that.currentPage,_that.expandedResult,_that.audioPath,_that.response,_that.speakingStage,_that.selectedIndex,_that.nextSection,_that.isLoading,_that.isListening,_that.isTranslate,_that.resultPronunciation,_that.resultStage2,_that.resultStage3,_that.showStageOnboarding,_that.isIntro);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( List<SpeakingPart> speakings,  QuestionResultModel? result,  int currentPage,  bool expandedResult,  AudioPath? audioPath,  PronunciationResultFormatted? response,  SpeakingStage speakingStage,  int selectedIndex,  bool nextSection,  bool isLoading,  bool isListening,  bool isTranslate,  int resultPronunciation,  SpeakingAgregateScore? resultStage2,  SpeakingAgregateScore? resultStage3,  bool showStageOnboarding,  bool isIntro)?  $default,) {final _that = this;
switch (_that) {
case _SpeakingState() when $default != null:
return $default(_that.speakings,_that.result,_that.currentPage,_that.expandedResult,_that.audioPath,_that.response,_that.speakingStage,_that.selectedIndex,_that.nextSection,_that.isLoading,_that.isListening,_that.isTranslate,_that.resultPronunciation,_that.resultStage2,_that.resultStage3,_that.showStageOnboarding,_that.isIntro);case _:
  return null;

}
}

}

/// @nodoc


class _SpeakingState extends SpeakingState {
   _SpeakingState({final  List<SpeakingPart> speakings = const [], this.result, this.currentPage = 0, this.expandedResult = false, this.audioPath, this.response, this.speakingStage = SpeakingStage.stage1, this.selectedIndex = 0, this.nextSection = false, this.isLoading = false, this.isListening = false, this.isTranslate = false, this.resultPronunciation = 0, this.resultStage2, this.resultStage3, this.showStageOnboarding = true, this.isIntro = false}): _speakings = speakings,super._();
  

 final  List<SpeakingPart> _speakings;
@override@JsonKey() List<SpeakingPart> get speakings {
  if (_speakings is EqualUnmodifiableListView) return _speakings;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_speakings);
}

@override final  QuestionResultModel? result;
@override@JsonKey() final  int currentPage;
@override@JsonKey() final  bool expandedResult;
@override final  AudioPath? audioPath;
@override final  PronunciationResultFormatted? response;
@override@JsonKey() final  SpeakingStage speakingStage;
@override@JsonKey() final  int selectedIndex;
@override@JsonKey() final  bool nextSection;
@override@JsonKey() final  bool isLoading;
@override@JsonKey() final  bool isListening;
@override@JsonKey() final  bool isTranslate;
@override@JsonKey() final  int resultPronunciation;
@override final  SpeakingAgregateScore? resultStage2;
@override final  SpeakingAgregateScore? resultStage3;
@override@JsonKey() final  bool showStageOnboarding;
@override@JsonKey() final  bool isIntro;

/// Create a copy of SpeakingState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SpeakingStateCopyWith<_SpeakingState> get copyWith => __$SpeakingStateCopyWithImpl<_SpeakingState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _SpeakingState&&const DeepCollectionEquality().equals(other._speakings, _speakings)&&(identical(other.result, result) || other.result == result)&&(identical(other.currentPage, currentPage) || other.currentPage == currentPage)&&(identical(other.expandedResult, expandedResult) || other.expandedResult == expandedResult)&&(identical(other.audioPath, audioPath) || other.audioPath == audioPath)&&(identical(other.response, response) || other.response == response)&&(identical(other.speakingStage, speakingStage) || other.speakingStage == speakingStage)&&(identical(other.selectedIndex, selectedIndex) || other.selectedIndex == selectedIndex)&&(identical(other.nextSection, nextSection) || other.nextSection == nextSection)&&(identical(other.isLoading, isLoading) || other.isLoading == isLoading)&&(identical(other.isListening, isListening) || other.isListening == isListening)&&(identical(other.isTranslate, isTranslate) || other.isTranslate == isTranslate)&&(identical(other.resultPronunciation, resultPronunciation) || other.resultPronunciation == resultPronunciation)&&(identical(other.resultStage2, resultStage2) || other.resultStage2 == resultStage2)&&(identical(other.resultStage3, resultStage3) || other.resultStage3 == resultStage3)&&(identical(other.showStageOnboarding, showStageOnboarding) || other.showStageOnboarding == showStageOnboarding)&&(identical(other.isIntro, isIntro) || other.isIntro == isIntro));
}


@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(_speakings),result,currentPage,expandedResult,audioPath,response,speakingStage,selectedIndex,nextSection,isLoading,isListening,isTranslate,resultPronunciation,resultStage2,resultStage3,showStageOnboarding,isIntro);

@override
String toString() {
  return 'SpeakingState(speakings: $speakings, result: $result, currentPage: $currentPage, expandedResult: $expandedResult, audioPath: $audioPath, response: $response, speakingStage: $speakingStage, selectedIndex: $selectedIndex, nextSection: $nextSection, isLoading: $isLoading, isListening: $isListening, isTranslate: $isTranslate, resultPronunciation: $resultPronunciation, resultStage2: $resultStage2, resultStage3: $resultStage3, showStageOnboarding: $showStageOnboarding, isIntro: $isIntro)';
}


}

/// @nodoc
abstract mixin class _$SpeakingStateCopyWith<$Res> implements $SpeakingStateCopyWith<$Res> {
  factory _$SpeakingStateCopyWith(_SpeakingState value, $Res Function(_SpeakingState) _then) = __$SpeakingStateCopyWithImpl;
@override @useResult
$Res call({
 List<SpeakingPart> speakings, QuestionResultModel? result, int currentPage, bool expandedResult, AudioPath? audioPath, PronunciationResultFormatted? response, SpeakingStage speakingStage, int selectedIndex, bool nextSection, bool isLoading, bool isListening, bool isTranslate, int resultPronunciation, SpeakingAgregateScore? resultStage2, SpeakingAgregateScore? resultStage3, bool showStageOnboarding, bool isIntro
});


@override $QuestionResultModelCopyWith<$Res>? get result;@override $AudioPathCopyWith<$Res>? get audioPath;@override $PronunciationResultFormattedCopyWith<$Res>? get response;@override $SpeakingAgregateScoreCopyWith<$Res>? get resultStage2;@override $SpeakingAgregateScoreCopyWith<$Res>? get resultStage3;

}
/// @nodoc
class __$SpeakingStateCopyWithImpl<$Res>
    implements _$SpeakingStateCopyWith<$Res> {
  __$SpeakingStateCopyWithImpl(this._self, this._then);

  final _SpeakingState _self;
  final $Res Function(_SpeakingState) _then;

/// Create a copy of SpeakingState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? speakings = null,Object? result = freezed,Object? currentPage = null,Object? expandedResult = null,Object? audioPath = freezed,Object? response = freezed,Object? speakingStage = null,Object? selectedIndex = null,Object? nextSection = null,Object? isLoading = null,Object? isListening = null,Object? isTranslate = null,Object? resultPronunciation = null,Object? resultStage2 = freezed,Object? resultStage3 = freezed,Object? showStageOnboarding = null,Object? isIntro = null,}) {
  return _then(_SpeakingState(
speakings: null == speakings ? _self._speakings : speakings // ignore: cast_nullable_to_non_nullable
as List<SpeakingPart>,result: freezed == result ? _self.result : result // ignore: cast_nullable_to_non_nullable
as QuestionResultModel?,currentPage: null == currentPage ? _self.currentPage : currentPage // ignore: cast_nullable_to_non_nullable
as int,expandedResult: null == expandedResult ? _self.expandedResult : expandedResult // ignore: cast_nullable_to_non_nullable
as bool,audioPath: freezed == audioPath ? _self.audioPath : audioPath // ignore: cast_nullable_to_non_nullable
as AudioPath?,response: freezed == response ? _self.response : response // ignore: cast_nullable_to_non_nullable
as PronunciationResultFormatted?,speakingStage: null == speakingStage ? _self.speakingStage : speakingStage // ignore: cast_nullable_to_non_nullable
as SpeakingStage,selectedIndex: null == selectedIndex ? _self.selectedIndex : selectedIndex // ignore: cast_nullable_to_non_nullable
as int,nextSection: null == nextSection ? _self.nextSection : nextSection // ignore: cast_nullable_to_non_nullable
as bool,isLoading: null == isLoading ? _self.isLoading : isLoading // ignore: cast_nullable_to_non_nullable
as bool,isListening: null == isListening ? _self.isListening : isListening // ignore: cast_nullable_to_non_nullable
as bool,isTranslate: null == isTranslate ? _self.isTranslate : isTranslate // ignore: cast_nullable_to_non_nullable
as bool,resultPronunciation: null == resultPronunciation ? _self.resultPronunciation : resultPronunciation // ignore: cast_nullable_to_non_nullable
as int,resultStage2: freezed == resultStage2 ? _self.resultStage2 : resultStage2 // ignore: cast_nullable_to_non_nullable
as SpeakingAgregateScore?,resultStage3: freezed == resultStage3 ? _self.resultStage3 : resultStage3 // ignore: cast_nullable_to_non_nullable
as SpeakingAgregateScore?,showStageOnboarding: null == showStageOnboarding ? _self.showStageOnboarding : showStageOnboarding // ignore: cast_nullable_to_non_nullable
as bool,isIntro: null == isIntro ? _self.isIntro : isIntro // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

/// Create a copy of SpeakingState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$QuestionResultModelCopyWith<$Res>? get result {
    if (_self.result == null) {
    return null;
  }

  return $QuestionResultModelCopyWith<$Res>(_self.result!, (value) {
    return _then(_self.copyWith(result: value));
  });
}/// Create a copy of SpeakingState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$AudioPathCopyWith<$Res>? get audioPath {
    if (_self.audioPath == null) {
    return null;
  }

  return $AudioPathCopyWith<$Res>(_self.audioPath!, (value) {
    return _then(_self.copyWith(audioPath: value));
  });
}/// Create a copy of SpeakingState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$PronunciationResultFormattedCopyWith<$Res>? get response {
    if (_self.response == null) {
    return null;
  }

  return $PronunciationResultFormattedCopyWith<$Res>(_self.response!, (value) {
    return _then(_self.copyWith(response: value));
  });
}/// Create a copy of SpeakingState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$SpeakingAgregateScoreCopyWith<$Res>? get resultStage2 {
    if (_self.resultStage2 == null) {
    return null;
  }

  return $SpeakingAgregateScoreCopyWith<$Res>(_self.resultStage2!, (value) {
    return _then(_self.copyWith(resultStage2: value));
  });
}/// Create a copy of SpeakingState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$SpeakingAgregateScoreCopyWith<$Res>? get resultStage3 {
    if (_self.resultStage3 == null) {
    return null;
  }

  return $SpeakingAgregateScoreCopyWith<$Res>(_self.resultStage3!, (value) {
    return _then(_self.copyWith(resultStage3: value));
  });
}
}

// dart format on
