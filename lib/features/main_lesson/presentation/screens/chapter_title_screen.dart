import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:selfeng/configs/routes/core_router_name.dart';
import 'package:selfeng/features/main_lesson/presentation/providers/chapter_title_controller.dart';
import 'package:selfeng/features/main_lesson/presentation/providers/state/chapter_title_state.dart';
import 'package:selfeng/shared/helpers/navigation_helper.dart';
import 'package:selfeng/shared/widgets/loading_circle.dart';

class ChapterTitleScreen extends ConsumerStatefulWidget {
  final String level;
  final String chapter;
  const ChapterTitleScreen({
    super.key,
    required this.level,
    required this.chapter,
  });
  @override
  ConsumerState<ChapterTitleScreen> createState() => _ChapterTitleScreenState();
}

class _ChapterTitleScreenState extends ConsumerState<ChapterTitleScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;

  late AsyncValue<ChapterTitleState> viewState;
  late ChapterTitleController viewModel;

  @override
  void initState() {
    super.initState();

    _animationController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    );
    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeIn,
    );

    _animation.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        _animationController.reverse();
      } else if (status == AnimationStatus.dismissed) {
        customNav(
          context,
          RouterName.pronunciationChallengeOnboarding,
          isReplace: true,
          params: {'level': widget.level, 'chapter': widget.chapter},
        );
      }
    });

    _animationController.forward();
  }

  @override
  dispose() {
    _animationController.dispose(); // Dispose tick to prevent memory leak!
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final prov = chapterTitleControllerProvider(widget.level, widget.chapter);
    viewState = ref.watch(prov);
    viewModel = ref.watch(prov.notifier);

    ref.listen(prov, ((previous, next) {
      //show Snackbar on failure
      next.maybeWhen(
        error: (error, track) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(SnackBar(content: Text(error.toString())));
        },
        orElse: () {},
      );
    }));

    return Scaffold(
      body: FadeTransition(
        opacity: _animation,
        child: Stack(
          children: [
            if (viewState.value!.chapter != null)
              CachedNetworkImage(
                placeholder: (context, url) => const LoadingCircle(),
                errorWidget: (context, url, error) => const Icon(Icons.error),
                imageUrl: viewState.value!.chapter?.imageUrl ?? '',
                imageBuilder: (context, imageProvider) => Container(
                  decoration: BoxDecoration(
                    image: DecorationImage(
                      image: imageProvider,
                      fit: BoxFit.cover,
                    ),
                  ),
                  child: Stack(
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Align(
                            alignment: Alignment.topCenter,
                            child: Padding(
                              padding: const EdgeInsets.only(top: 120),
                              child: Column(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Container(
                                    decoration: BoxDecoration(
                                      gradient: const LinearGradient(
                                        colors: [
                                          Color.fromARGB(255, 202, 30, 35),
                                          Color.fromARGB(255, 66, 21, 7),
                                        ],
                                        begin: Alignment.topCenter,
                                        end: Alignment.bottomCenter,
                                      ),
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 20,
                                      vertical: 3,
                                    ),
                                    child: Text(
                                      'CHAPTER ${widget.chapter}',
                                      style: const TextStyle(
                                        color: Colors.white,
                                        fontSize: 28,
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                  ),
                                  const SizedBox(height: 16),
                                  Container(
                                    decoration: BoxDecoration(
                                      gradient: const LinearGradient(
                                        colors: [
                                          Color.fromARGB(255, 254, 117, 76),
                                          Color.fromARGB(255, 226, 31, 41),
                                          Color.fromARGB(255, 195, 21, 31),
                                        ],
                                        begin: Alignment.bottomCenter,
                                        end: Alignment.topCenter,
                                      ),
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 20,
                                      vertical: 3,
                                    ),
                                    child: SizedBox(
                                      width:
                                          MediaQuery.of(context).size.width *
                                          0.8,
                                      child: Text(
                                        viewState.value!.chapter?.label ?? '',
                                        style: const TextStyle(
                                          color: Colors.white,
                                          fontSize: 28,
                                          fontWeight: FontWeight.w600,
                                        ),
                                        softWrap: true,
                                        textAlign: TextAlign.center,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
