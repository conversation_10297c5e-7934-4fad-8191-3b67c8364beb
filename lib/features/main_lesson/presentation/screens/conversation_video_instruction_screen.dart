import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:selfeng/configs/routes/core_router_name.dart';
import 'package:selfeng/features/main_lesson/presentation/widgets/desc_with_image.dart';
import 'package:selfeng/shared/domain/models/models.dart';
import 'package:selfeng/shared/globals.dart';
import 'package:selfeng/shared/helpers/navigation_helper.dart';
import 'package:selfeng/shared/localization/app_localizations_context.dart';
import 'package:selfeng/shared/widgets/v_button_gradient.dart';

class ConversationVideoInstructionScreen extends ConsumerStatefulWidget {
  final String level;
  final String chapter;
  const ConversationVideoInstructionScreen({
    super.key,
    required this.level,
    required this.chapter,
  });

  @override
  ConsumerState<ConversationVideoInstructionScreen> createState() =>
      _ConversationVideoInstructionScreenState();
}

class _ConversationVideoInstructionScreenState
    extends ConsumerState<ConversationVideoInstructionScreen>
    with TickerProviderStateMixin {
  late Size _size;
  late List<DefaultModel> _listItem;
  bool _isInitialized = false;

  @override
  void initState() {
    super.initState();
  }

  void init() {
    _isInitialized = true;
    _size = MediaQuery.of(context).size;
    _listItem = [
      DefaultModel(
        title: '1',
        image:
            '$assetImageMainLesson/conversation_video/Panduan${context.loc.localeName == 'en' ? '-Eng' : ''}.png',
        child: DescWithImage(prefix: context.loc.cv_instruction_decs1),
      ),
      DefaultModel(
        title: '2',
        image: '$assetImageMainLesson/conversation_video/Panduan-1.png',
        child: DescWithImage(prefix: context.loc.cv_instruction_decs2),
      ),
      DefaultModel(
        title: '3',
        image: '$assetImageMainLesson/conversation_video/Panduan-2.png',
        child: DescWithImage(prefix: context.loc.cv_instruction_decs3),
      ),
      DefaultModel(
        title: '4',
        image: '$assetImageMainLesson/conversation_video/Panduan-5.png',
        child: DescWithImage(prefix: context.loc.cv_instruction_decs4),
      ),
      DefaultModel(
        title: '5',
        image: '$assetImageMainLesson/conversation_video/Panduan-4.png',
        child: DescWithImage(prefix: context.loc.cv_instruction_decs5),
      ),
    ];
  }

  @override
  Widget build(BuildContext context) {
    if (!_isInitialized) init();

    return Scaffold(
      body: SafeArea(
        child: Stack(
          children: [
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: ListView(
                children: [
                  const SizedBox(height: 32),
                  LinearProgressIndicator(
                    value: 1 / 1,
                    backgroundColor: const Color(0xffFFDAD2),
                    valueColor: AlwaysStoppedAnimation(
                      Theme.of(context).primaryColor,
                    ),
                  ),
                  const SizedBox(height: 26),
                  Text(
                    context.loc.how_to_answer,
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const SizedBox(height: 20),
                  Column(
                    children: _listItem
                        .map<Widget>((item) => instructionItem(item: item))
                        .toList(),
                  ),
                  const SizedBox(height: 130),
                ],
              ),
            ),
            Align(
              alignment: Alignment.bottomCenter,
              child: Container(
                margin: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 24,
                ),
                child: VButtonGradient(
                  title: context.loc.next,
                  onTap: () {
                    customNav(
                      context,
                      RouterName.conversationVideo,
                      isReplace: true,
                      params: {
                        'level': widget.level,
                        'chapter': widget.chapter,
                        'path': 'blankpath',
                      },
                    );
                  },
                  isBorder: false,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Container instructionItem({required DefaultModel item}) => Container(
    padding: const EdgeInsets.symmetric(vertical: 20),
    child: Row(
      children: [
        Text('${item.title}.', style: Theme.of(context).textTheme.titleLarge),
        const SizedBox(width: 12),
        Container(
          height: 90,
          width: 90,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            gradient: const LinearGradient(
              colors: [Color(0xffFE754C), Color(0xffE21F29), Color(0xffC3151F)],
              begin: Alignment.bottomLeft,
              end: Alignment.topRight,
            ),
            image: DecorationImage(
              image: AssetImage(item.image),
              fit: BoxFit.scaleDown,
            ),
          ),
        ),
        const SizedBox(width: 12),
        item.child,
      ],
    ),
  );
}
