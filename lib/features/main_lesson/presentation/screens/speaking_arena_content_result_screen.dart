import 'package:audioplayers/audioplayers.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:selfeng/configs/routes/core_router_name.dart';
import 'package:selfeng/features/main_lesson/domain/models/main_lesson.dart';
import 'package:selfeng/features/main_lesson/presentation/providers/speaking_controller.dart';
import 'package:selfeng/features/main_lesson/presentation/providers/state/speaking_state.dart';
import 'package:selfeng/shared/globals.dart';
import 'package:selfeng/shared/helpers/navigation_helper.dart';
import 'package:selfeng/shared/localization/app_localizations_context.dart';
import 'package:selfeng/shared/widgets/widgets.dart';
import 'package:lottie/lottie.dart';
import 'package:selfeng/services/setting_cache_service/domain/providers/setting_cache_provider.dart'; // Added import

class SpeakingArenaContentResultScreen extends ConsumerStatefulWidget {
  final String level;
  final String chapter;
  final String path;
  final String stage;

  const SpeakingArenaContentResultScreen({
    super.key,
    required this.level,
    required this.chapter,
    required this.path,
    required this.stage,
  });

  @override
  ConsumerState<SpeakingArenaContentResultScreen> createState() =>
      _SpeakingArenaContentResultScreenState();
}

class _SpeakingArenaContentResultScreenState
    extends ConsumerState<SpeakingArenaContentResultScreen>
    with TickerProviderStateMixin {
  late AsyncValue<SpeakingState> viewState;
  late SpeakingController viewModel;
  final AudioPlayer _confettiPlayer = AudioPlayer();
  bool _mounted = true;
  bool _isNavigating = false;

  Future<void> _playCongrats() async {
    if (_mounted) {
      // Read the audio toggle state
      final isAudioEnabled = ref.read(audioToggleProvider);
      // Only play if audio is enabled
      if (isAudioEnabled) {
        await _confettiPlayer.play(AssetSource('sounds/success3.mp3'));
      }
    }
  }

  Future<void> _playFail() async {
    if (_mounted) {
      // Read the audio toggle state
      final isAudioEnabled = ref.read(audioToggleProvider);
      // Only play if audio is enabled
      if (isAudioEnabled) {
        await _confettiPlayer.play(AssetSource('sounds/wrong.mp3'));
      }
    }
  }

  @override
  void initState() {
    super.initState();
    _mounted = true;
  }

  @override
  void dispose() {
    _mounted = false;
    _confettiPlayer.dispose();
    super.dispose();
  }

  String _getPronScoreImage(double score) {
    if (score > 95) return 'Luar Biasa-Android';
    if (score > 85) return 'Kerja Bagus-Android';
    if (score > 75) return 'Kerja Bagus-Android';
    if (score > 60) return 'Cukup sesuai-Android';
    return 'Tingkatkan Kembali-Android';
  }

  String _getPronScoreText(BuildContext context, double score) {
    if (score > 95) return context.loc.excellent;
    if (score > 85) return context.loc.very_good;
    if (score > 75) return context.loc.be_better;
    return context.loc.fair;
  }

  @override
  Widget build(BuildContext context) {
    if (_isNavigating) {
      return const Scaffold();
    }
    final prov = speakingControllerProvider(
      widget.level,
      widget.chapter,
      widget.path,
      SpeakingStage.values.byName(widget.stage),
    );
    viewState = ref.watch(prov);
    viewModel = ref.watch(prov.notifier);

    return viewState.value!.isLoading ? const AppLoading() : _buildBody();
  }

  Widget _buildBody() {
    if (viewState.value?.response != null) {
      _playCongrats();
    } else {
      _playFail();
    }

    return Scaffold(
      body: Stack(
        children: [
          if (viewState.value?.response != null) ...[
            Lottie.asset(
              'assets/animations/congrats.json',
              animate: true,
              repeat: false,
              fit: BoxFit.fitWidth,
            ),
            _buildSuccessContent(),
          ] else
            _buildErrorMessage(
              context,
              'Please try again, we are unable to hear your voice.',
            ),
        ],
      ),
      bottomNavigationBar: _buildBottomNavigation(),
    );
  }

  Widget _buildSuccessContent() {
    return ListView(
      children: [
        const SizedBox(height: 120),
        viewState.when(
          data: (state) => Column(
            children: [
              Stack(
                children: [
                  Container(
                    height: 252,
                    width: 252,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      image: DecorationImage(
                        image: AssetImage(
                          '$assetImageMainLesson/pronunciation_challenge/${_getPronScoreImage(state.response!.pronScore)}.png',
                        ),
                        fit: BoxFit.scaleDown,
                      ),
                    ),
                  ),
                  Positioned.fill(
                    child: Center(
                      child: Text(
                        '${state.response!.pronScore.toInt()}',
                        style: TextStyle(
                          fontSize: 64,
                          fontWeight: FontWeight.bold,
                          color: Colors.white.withValues(alpha: .7),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Text(
                _getPronScoreText(context, state.response!.pronScore),
                textAlign: TextAlign.center,
                style: Theme.of(context).textTheme.titleLarge,
              ),
            ],
          ),
          error: (error, stackTrace) =>
              _buildErrorMessage(context, error.toString()),
          loading: () => const AppLoading(),
        ),
        const SizedBox(height: 124),
      ],
    );
  }

  Widget _buildBottomNavigation() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        const SizedBox(height: 16),
        RepeatNextButton(
          onTapRepeat: () {
            if (_mounted) {
              // Mark as navigating to suppress transient error UI during state reset
              setState(() {
                _isNavigating = true;
              });
              _mounted = false;
              viewModel.clearResponse();
              viewModel.setQAActive(
                session: SpeakingSessionType.question,
                isListening:
                    viewState.value!.speakingStage == SpeakingStage.stage2
                    ? false
                    : true,
              );
              customNav(
                context,
                RouterName.speakingArenaStage,
                isReplace: true,
                params: {
                  'level': widget.level,
                  'chapter': widget.chapter,
                  'path': widget.path,
                  'stage': viewState.value!.speakingStage.name,
                },
              );
            }
          },
          onTapNext: () {
            if (_mounted) {
              setState(() {
                _isNavigating = true;
              });
              _mounted = false;
              viewModel.nextQuestion(context);
            }
          },
        ),
      ],
    );
  }

  Widget _buildErrorMessage(BuildContext context, String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 48,
            color: Theme.of(context).colorScheme.error,
          ),
          const SizedBox(height: 16),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24),
            child: Text(
              message,
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Theme.of(context).colorScheme.error,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
