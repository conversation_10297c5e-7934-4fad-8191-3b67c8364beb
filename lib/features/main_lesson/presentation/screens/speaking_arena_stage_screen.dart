import 'dart:ui';

import 'package:audioplayers/audioplayers.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:selfeng/features/main_lesson/presentation/widgets/glass_effect_app_bar.dart';
import 'package:selfeng/features/main_lesson/presentation/widgets/recording_error_dialog.dart';
import 'package:selfeng/features/main_lesson/presentation/widgets/stage_transition_widget.dart';
import 'package:selfeng/configs/routes/core_router_name.dart';
import 'package:selfeng/features/main_lesson/domain/models/main_lesson.dart';
import 'package:selfeng/features/main_lesson/presentation/providers/speaking_controller.dart';
import 'package:selfeng/features/main_lesson/presentation/providers/state/speaking_state.dart';
import 'package:selfeng/features/main_lesson/presentation/widgets/audio_record.dart';
import 'package:selfeng/shared/globals.dart';
import 'package:selfeng/shared/helpers/navigation_helper.dart';
import 'package:selfeng/shared/localization/app_localizations_context.dart';
import 'package:selfeng/shared/widgets/loading_circle.dart';
import 'package:selfeng/shared/widgets/widgets.dart';
import 'package:selfeng/features/main_lesson/presentation/widgets/dash_progress_indicator.dart';

class SpeakingArenaStageScreen extends ConsumerStatefulWidget {
  final String level;
  final String chapter;
  final String path;
  final String stage;

  const SpeakingArenaStageScreen({
    super.key,
    required this.level,
    required this.chapter,
    required this.path,
    required this.stage,
  });

  @override
  ConsumerState<SpeakingArenaStageScreen> createState() =>
      _SpeakingArenaStageScreenState();
}

class _SpeakingArenaStageScreenState
    extends ConsumerState<SpeakingArenaStageScreen>
    with TickerProviderStateMixin {
  late AsyncValue<SpeakingState> viewState;
  late SpeakingController viewModel;
  late Size _size;
  final AudioPlayer _player = AudioPlayer();
  late final AnimationController _controller;
  late final Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _initializeAnimationController();
    _setupPlayerListener();
    _setupAnimationListener();
  }

  void _initializeAnimationController() {
    _controller = AnimationController(
      duration: const Duration(seconds: 1),
      vsync: this,
    );
    _animation = CurvedAnimation(parent: _controller, curve: Curves.easeIn);
  }

  void _setupPlayerListener() {
    _player.onPlayerComplete.listen((_) => _events());
  }

  void _setupAnimationListener() {
    _animation.addStatusListener(_handleAnimationStatus);
  }

  void _handleAnimationStatus(AnimationStatus status) {
    if (status == AnimationStatus.completed) {
      _controller.reverse();
    } else if (status == AnimationStatus.dismissed) {
      _handleStageTransition();
    }
  }

  void _handleStageTransition() {
    switch (viewState.value?.speakingStage) {
      case SpeakingStage.onboardingStage2:
        viewModel.changeStage(SpeakingStage.stage2);
        break;
      case SpeakingStage.onboardingStage3:
        viewModel.changeStage(SpeakingStage.stage3);
        break;
      default:
        break;
    }
  }

  @override
  void dispose() {
    _player.dispose();
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final prov = speakingControllerProvider(
      widget.level,
      widget.chapter,
      widget.path,
      SpeakingStage.values.byName(widget.stage),
    );
    viewState = ref.watch(prov);
    viewModel = ref.watch(prov.notifier);
    _size = MediaQuery.of(context).size;

    ref.listen(prov.select((value) => value), (previous, next) {
      next.maybeWhen(
        error: (error, _) {
          if (error.toString() ==
              "Please try again, we are unable to hear your voice.") {
            return showDialog(
              context: context,
              builder: (context) => RecordingErrorDialog(
                message: error.toString(),
                onClose: () => Navigator.of(context).pop(),
              ),
            );
          }
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(SnackBar(content: Text(error.toString())));
        },
        orElse: () {},
      );
    });

    return switch (viewState) {
      AsyncData() => Scaffold(
        body: Stack(
          children: [
            if (viewState.value?.speakings.isNotEmpty ?? false) stage(),
          ],
        ),
      ),
      AsyncError() => Scaffold(body: stage()),
      AsyncLoading() => const AppLoading(),
      _ => const Text('loading'),
    };
  }

  Widget stage() {
    if (viewState.value == null) return const SizedBox.shrink();

    if (viewState.value?.speakings == null ||
        viewState.value!.speakings.isEmpty) {
      return const SizedBox.shrink();
    }
    final clampedIndex = viewState.value!.selectedIndex.clamp(
      0,
      viewState.value!.speakings.length - 1,
    );
    final currentSpeaking = viewState.value!.speakings[clampedIndex];
    final String text;
    final String translateText;
    final String image;
    final String audio;

    if (currentSpeaking.question.isActive) {
      text = currentSpeaking.question.text;
      translateText = currentSpeaking.question.translation;
      image = currentSpeaking.image;
      audio = currentSpeaking.question.audio;
    } else {
      text = currentSpeaking.answer.text;
      translateText = currentSpeaking.answer.translation;
      image = currentSpeaking.imageB;
      audio = currentSpeaking.answer.audio;
    }

    switch (viewState.value!.speakingStage) {
      case SpeakingStage.onboardingStage2:
        return _buildStageTransition(
          image: '$assetImageMainLesson/speaking_arena/BG27.png',
          title: 'Stage II',
          subTitle: context.loc.stage2Speaking,
        );
      case SpeakingStage.onboardingStage3:
        return _buildStageTransition(
          image: '$assetImageMainLesson/speaking_arena/BG29.png',
          title: 'Stage III',
          subTitle: context.loc.stage3Speaking,
        );
      default:
        return _buildStageListen(
          text: text,
          translateText: translateText,
          image: image,
          audio: audio,
        );
    }
  }

  Widget _buildStageListen({
    required String text,
    required String translateText,
    required String image,
    required String audio,
  }) {
    return viewState.when(
      data: (state) => _buildStageContent(
        text: text,
        translateText: translateText,
        image: image,
        audio: audio,
        isListening: state.isListening,
        hasResponse: state.response != null,
        isError: false,
      ),
      error: (error, _) => _buildStageContent(
        text: text,
        translateText: translateText,
        image: image,
        audio: '',
        isListening: false,
        hasResponse: false,
        isError: true,
      ),
      loading: () => const LoadingCircle(),
    );
  }

  Widget _buildStageContent({
    required String text,
    required String translateText,
    required String image,
    required String audio,
    required bool isListening,
    required bool hasResponse,
    required bool isError,
  }) {
    if (isListening && audio.isNotEmpty) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        Future.delayed(const Duration(milliseconds: 500), () {
          _player.play(UrlSource(audio));
        });
      });
    }

    return CachedNetworkImage(
      placeholder: (_, __) => const LoadingCircle(),
      errorWidget: (_, __, ___) => const Icon(Icons.error),
      imageUrl: image.isNotEmpty ? image : 'placeholder.jpg',
      imageBuilder: (context, imageProvider) => Container(
        decoration: BoxDecoration(
          image: DecorationImage(image: imageProvider, fit: BoxFit.fill),
        ),
        child: Stack(
          alignment: Alignment.center,
          children: [
            SafeArea(
              child: Column(
                children: [
                  GlassEffectAppBar(
                    title: context.loc.speakingArena,
                    isBookmarked: viewState.value!.speakings.isNotEmpty
                        ? viewState
                              .value!
                              .speakings[viewState.value!.selectedIndex]
                              .isBookmarked
                        : false,
                    onBookmark: () {
                      viewModel.saveBookmark();
                    },
                    onHelp: () {
                      customNav(
                        context,
                        RouterName.speakingArenaInstruction,
                        isReplace: true,
                        params: {
                          'level': widget.level,
                          'chapter': widget.chapter,
                        },
                      );
                    },
                  ),
                  const SizedBox(height: 16),
                  if (viewState.value!.questionLength > 0)
                    DashProgressIndicator(
                      progress: viewState.value!.selectedIndex,
                      totalLength: viewState.value!.questionLength,
                    ),
                ],
              ),
            ),
            // if (viewState.value!.isTranslate)
            //   _buildTranslateBubble(translateText),
            _buildTextBubble(
              viewState.value!.isTranslate ? translateText : text,
            ),
            if (!isError) _buildBottomControls(),
          ],
        ),
      ),
    );
  }

  Widget _buildTextBubble(String text) {
    return Positioned(
      top: 150,
      left: 30,
      right: 30,
      child: ClipRect(
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 2, sigmaY: 10),
          child: Container(
            decoration: BoxDecoration(
              color: Color(0xffEBEBEB).withValues(alpha: 0.4),
              borderRadius: BorderRadius.all(Radius.circular(16)),
              border: Border.all(
                color: const Color(0xffEBEBEB).withValues(alpha: 0.2),
                width: 1,
              ),
            ),
            padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 14),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  text,
                  style: Theme.of(
                    context,
                  ).textTheme.titleMedium?.copyWith(color: Colors.white),
                ),
                IconButton(
                  icon: Icon(
                    Icons.g_translate,
                    size: 24,
                    color: Color(0xffD0C4C2),
                  ),
                  onPressed: () {
                    viewModel.translate();
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildBottomControls() {
    return Positioned(bottom: 32, child: _buildControlContent());
  }

  Widget _buildControlContent() {
    if (viewState.value?.isListening ?? false) {
      return _buildNextButton();
    }
    return _buildRecorderSection();
  }

  Widget _buildNextButton() {
    return InkWell(
      onTap: _events,
      child: Container(
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: const Color(0xffFFB3AC).withValues(alpha: .5),
        ),
        height: 40,
        width: 40,
        child: const Icon(
          Icons.chevron_right_rounded,
          size: 36,
          color: Colors.black,
        ),
      ),
    );
  }

  Widget _buildRecorderSection() {
    return SizedBox(
      width: _size.width,
      child: Column(
        children: [
          if (viewState.value?.isRecord ?? false)
            Recorder(
              onStart: () async {},
              isLoading: viewState.value?.isLoading ?? false,
              onStop: (path) {
                viewModel.clearResponse();
                viewModel.uploadAudio(path: path);
                _events();
              },
            ),
        ],
      ),
    );
  }

  Future<void> _events() async {
    await Future.delayed(const Duration(milliseconds: 10));
    await _player.stop();

    if (viewState.value == null) return;

    final isListening = viewState.value!.isListening;
    final speakingStage = viewState.value!.speakingStage;
    final clampedIndex = viewState.value!.selectedIndex.clamp(
      0,
      viewState.value!.speakings.length - 1,
    );
    final currentSpeaking = viewState.value!.speakings[clampedIndex];

    if (currentSpeaking.answer.isActive) {
      customNav(
        context,
        RouterName.speakingArenaContentResult,
        isReplace: true,
        params: {
          'level': widget.level,
          'chapter': widget.chapter,
          'path': widget.path,
          'stage': widget.stage,
        },
      );
    } else if (speakingStage == SpeakingStage.stage2 && !isListening) {
      viewModel.setQAActive(
        session: SpeakingSessionType.answer,
        isListening: true,
      );
    } else if (speakingStage == SpeakingStage.stage3 && isListening) {
      viewModel.setQAActive(
        session: SpeakingSessionType.answer,
        isListening: false,
      );
    }
  }

  Widget _buildStageTransition({
    required String image,
    required String title,
    required String subTitle,
  }) {
    _controller.forward();
    return StageTransitionWidget(
      image: image,
      title: title,
      subTitle: subTitle,
    );
  }
}
