import 'dart:async';

import 'package:audioplayers/audioplayers.dart';
import 'package:flutter/material.dart';

class AudioPlayerWidget extends StatefulWidget {
  final AudioPlayer player;
  final bool isAttached;
  final void Function()? onComplete;
  final void Function()? onInit; // Used to set the audio source

  const AudioPlayerWidget({
    required this.player,
    this.isAttached = false,
    this.onComplete,
    this.onInit,
    super.key,
  });

  @override
  State<StatefulWidget> createState() {
    return _AudioPlayerWidgetState();
  }
}

class _AudioPlayerWidgetState extends State<AudioPlayerWidget> {
  PlayerState? _playerState;
  Duration? _duration;
  Duration? _position;

  StreamSubscription? _durationSubscription;
  StreamSubscription? _positionSubscription;
  StreamSubscription? _playerCompleteSubscription;
  StreamSubscription? _playerStateChangeSubscription;

  bool get _isPlaying => _playerState == PlayerState.playing;

  String _formatDuration(Duration? d) {
    if (d == null) return '0:00';
    final minutes = d.inMinutes;
    final seconds = d.inSeconds % 60;
    return '$minutes:${seconds.toString().padLeft(2, '0')}';
  }

  String get _durationText => _formatDuration(_duration);
  String get _positionText => _formatDuration(_position);

  AudioPlayer get player => widget.player;

  @override
  void initState() {
    super.initState();

    // It's crucial that onInit sets the player's source.
    // If the source is already set on the player instance before passing to this widget,
    // onInit might not be strictly necessary here, but it's a good pattern for re-initialization.
    widget.onInit?.call();

    // Initial state
    _playerState = player.state;

    // Try to get initial values. These will be updated by streams.
    player.getDuration().then((value) {
      if (mounted) setState(() => _duration = value);
    });
    player.getCurrentPosition().then((value) {
      if (mounted) setState(() => _position = value);
    });

    _initStreams();
  }

  @override
  void dispose() {
    _durationSubscription?.cancel();
    _positionSubscription?.cancel();
    _playerCompleteSubscription?.cancel();
    _playerStateChangeSubscription?.cancel();
    // It's generally not the responsibility of this widget to dispose the player
    // as it's passed in. However, stopping it is good practice.
    player.stop();
    super.dispose();
  }

  void _initStreams() {
    _durationSubscription = player.onDurationChanged.listen((duration) {
      if (mounted) setState(() => _duration = duration);
    });

    _positionSubscription = player.onPositionChanged.listen((p) {
      if (mounted) setState(() => _position = p);
    });

    _playerCompleteSubscription = player.onPlayerComplete.listen((event) async {
      // Note: `stop()` might release the audio source.
      // Re-initializing might be needed if playing again.
      await player.stop();
      if (mounted) {
        setState(() {
          _playerState = PlayerState.stopped;
          _position = Duration.zero; // Reset position on complete
        });
      }
      widget.onComplete?.call();
    });

    _playerStateChangeSubscription = player.onPlayerStateChanged.listen((
      state,
    ) {
      if (mounted) {
        setState(() {
          _playerState = state;
        });
      }
    });
  }

  Future<void> _play() async {
    if (_playerState == PlayerState.stopped ||
        _playerState == PlayerState.completed) {
      // If stopped or completed, the source might need to be set again.
      // onInit should handle setting the source (e.g., player.setSourceUrl(...))
      widget.onInit?.call(); // Re-initialize source if necessary
      // After onInit, the player should be in a state where resume works (e.g., paused after source set)
      // Or, if onInit prepares and starts, this resume might be redundant or handled by onInit.
      // For audioplayers, typically you setSource and then play/resume.
    }
    // `resume` will start playing if paused, or play from the beginning if stopped and source is set.
    await player.resume();
    // The state change listener will update _playerState, but we can set it preemptively for UI responsiveness
    if (mounted) setState(() => _playerState = PlayerState.playing);
  }

  Future<void> _pause() async {
    await player.pause();
    if (mounted) setState(() => _playerState = PlayerState.paused);
  }

  @override
  Widget build(BuildContext context) {
    // final colorScheme = Theme.of(context).colorScheme;
    final iconColor = const Color(0xffA90013);
    final textColor = const Color(0xffB4A9A7);

    return Container(
      width: double.infinity, // Takes full width of its parent
      height: 58,
      decoration: BoxDecoration(
        borderRadius: widget.isAttached
            ? BorderRadius.only(
                bottomLeft: Radius.circular(8),
                bottomRight: Radius.circular(8),
              )
            : BorderRadius.circular(8),
        color: Color(0xffFFEDEB),
        border: widget.isAttached
            ? null
            : Border.all(color: const Color(0xffE82329), width: 0.4),
      ),
      padding: const EdgeInsets.symmetric(
        horizontal: 16,
        vertical: 12,
      ), // Adjusted padding slightly
      child: Row(
        mainAxisAlignment:
            MainAxisAlignment.spaceBetween, // This is fine now with Expanded
        children: [
          InkWell(
            onTap: _isPlaying ? _pause : _play,
            customBorder: const CircleBorder(),
            child: Container(
              padding: const EdgeInsets.all(7),
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: iconColor,
              ),
              child: Icon(
                _isPlaying ? Icons.pause : Icons.play_arrow_rounded,
                size: 20,
                color: Colors.white,
              ),
            ),
          ),
          const SizedBox(width: 10),
          Expanded(
            // <<<< KEY CHANGE: Makes Slider flexible
            child: SliderTheme(
              data: SliderTheme.of(context).copyWith(
                trackHeight: 2.0,
                thumbShape: const RoundSliderThumbShape(
                  enabledThumbRadius: 7.0,
                ),
                overlayShape: const RoundSliderOverlayShape(
                  overlayRadius: 14.0,
                ),
                activeTrackColor: iconColor,
                inactiveTrackColor: iconColor.withValues(alpha: .3),
                thumbColor: iconColor,
                overlayColor: iconColor.withAlpha(0x29),
              ),
              child: Slider(
                min: 0.0,
                max: (_duration?.inMilliseconds ?? 0).toDouble(),
                value:
                    (_position != null &&
                        _duration != null &&
                        _position!.inMilliseconds > 0 &&
                        _position!.inMilliseconds <=
                            (_duration!.inMilliseconds)) // Use <= for value
                    ? _position!.inMilliseconds.toDouble()
                    : 0.0,
                onChanged: (value) {
                  if (_duration == null) return;
                  final newPosition = Duration(milliseconds: value.round());
                  player.seek(newPosition);
                  // Optionally update position immediately for smoother UI, though stream will also update
                  if (mounted) setState(() => _position = newPosition);
                },
              ),
            ),
          ),
          const SizedBox(width: 10),
          Container(
            color: Color(0xffFFB3AC),
            child: Text(
              // '$_positionText / $_durationText',
              _durationText,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Color(0xff7D000B),
              ), // Slightly smaller font for tight spaces
              overflow:
                  TextOverflow.ellipsis, // Prevent text itself from overflowing
            ),
          ),
        ],
      ),
    );
  }
}
