import 'package:flutter/material.dart';

class DashProgressIndicator extends StatelessWidget {
  final int progress;
  final int totalLength;
  const DashProgressIndicator({
    super.key,
    required this.progress,
    required this.totalLength,
  });

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final parentHorizontalMargin = 32; // 16 * 2
    final containerHorizontalMargin = 8; // 4 * 2 per container
    final totalMargins =
        parentHorizontalMargin + (containerHorizontalMargin * totalLength);
    final availableWidth = screenWidth - totalMargins;
    final containerWidth = availableWidth / totalLength;

    return Container(
      height: 8,
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          for (int i = 0; i < totalLength; i++) ...[
            Container(
              width: containerWidth,
              height: 4,
              margin: const EdgeInsets.symmetric(horizontal: 4),
              decoration: BoxDecoration(
                color: i <= progress ? Color(0xFF35BF32) : Color(0xFFEDE0DE),
                borderRadius: BorderRadius.circular(4),
              ),
            ),
          ],
        ],
      ),
    );
  }
}
