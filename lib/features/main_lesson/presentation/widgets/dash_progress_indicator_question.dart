import 'package:flutter/material.dart';
import 'package:selfeng/features/main_lesson/domain/models/main_lesson.dart';

class DashProgressIndicatorQuestion extends StatelessWidget {
  final List<Question> questions;
  const DashProgressIndicatorQuestion({super.key, required this.questions});

  Color _getColor(Question q) {
    if (q.answer == null) {
      return const Color(0xFFEDE0DE);
    } else if (q.isCorrect == true) {
      return const Color(0xFF35BF32);
    } else {
      return const Color(0xFFE82329);
    }
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final parentHorizontalMargin = 32; // 16 * 2
    final containerHorizontalMargin = 8; // 4 * 2 per container
    final totalMargins =
        parentHorizontalMargin + (containerHorizontalMargin * questions.length);
    final availableWidth = screenWidth - totalMargins;
    final containerWidth = availableWidth / questions.length;

    return Container(
      height: 8,
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          for (int i = 0; i < questions.length; i++) ...[
            Container(
              width: containerWidth,
              height: 4,
              margin: const EdgeInsets.symmetric(horizontal: 4),
              decoration: BoxDecoration(
                color: _getColor(questions[i]),
                borderRadius: BorderRadius.circular(4),
              ),
            ),
          ],
        ],
      ),
    );
  }
}
