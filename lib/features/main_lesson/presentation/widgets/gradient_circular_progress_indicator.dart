import 'package:flutter/material.dart';
import 'dart:math' as math;

class GradientCircularProgressIndicator extends StatefulWidget {
  final double size;
  final bool clockwise;

  const GradientCircularProgressIndicator({
    super.key,
    this.size = 40.0,
    this.clockwise = true,
  });

  @override
  GradientCircularProgressIndicatorState createState() =>
      GradientCircularProgressIndicatorState();
}

class GradientCircularProgressIndicatorState
    extends State<GradientCircularProgressIndicator>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (_, child) {
        return SizedBox(
          child: CustomPaint(
            size: Size(widget.size, widget.size),
            painter: _GradientCircularProgressPainter(
              progress: _controller.value,
              clockwise: widget.clockwise,
            ),
          ),
        );
      },
    );
  }
}

class _GradientCircularProgressPainter extends CustomPainter {
  _GradientCircularProgressPainter({
    required this.progress,
    required this.clockwise,
  });

  final double progress;
  final bool clockwise;

  @override
  void paint(Canvas canvas, Size size) {
    final rect = Rect.fromLTWH(0, 0, size.width, size.height);
    final gradient = SweepGradient(
      startAngle: 0,
      endAngle: math.pi * 2,
      colors: [
        Colors.red.shade300,
        Colors.red.shade500,
        Colors.grey.shade300,
        Colors.grey.shade300,
      ],
      stops: const [0.0, 0.5, 0.5, 1.0],
      transform: GradientRotation(progress * 2 * math.pi),
    );

    final paint = Paint()
      ..shader = gradient.createShader(rect)
      ..strokeCap = StrokeCap.round
      ..style = PaintingStyle.stroke
      ..strokeWidth = size.width / 10;

    final center = Offset(size.width / 2, size.height / 2);
    final radius = (size.width - paint.strokeWidth) / 2;

    canvas.drawArc(
      Rect.fromCircle(center: center, radius: radius),
      -math.pi / 2,
      clockwise ? 2 * math.pi : -2 * math.pi,
      false,
      paint,
    );
  }

  @override
  bool shouldRepaint(_GradientCircularProgressPainter oldDelegate) =>
      progress != oldDelegate.progress;
}

class LoadingIndicators extends StatelessWidget {
  const LoadingIndicators({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      color: const Color(0xFFFCE9E9), // Light pink background
      child: const Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Padding(
            padding: EdgeInsets.all(16.0),
            child: GradientCircularProgressIndicator(size: 40, clockwise: true),
          ),
          Padding(
            padding: EdgeInsets.all(16.0),
            child: GradientCircularProgressIndicator(
              size: 40,
              clockwise: false,
            ),
          ),
        ],
      ),
    );
  }
}
