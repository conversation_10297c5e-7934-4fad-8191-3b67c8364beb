import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:selfeng/shared/localization/app_localizations_context.dart'; // Corrected import
import 'package:selfeng/shared/widgets/widgets.dart'; // Corrected import for VButtonGradient

class IncompleteChallenge extends StatelessWidget {
  final String assetImagePath;

  const IncompleteChallenge({super.key, required this.assetImagePath});

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 24.0),
        child: Stack(
          children: [
            Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Image.asset(
                  assetImagePath, // Use the parameter here
                  height: 200,
                  fit: BoxFit.contain,
                ),
                const SizedBox(height: 48),
                Text(
                  context.loc.complete_all_challenges,
                  textAlign: TextAlign
                      .center, // Uses extension method from corrected import
                  style: TextStyle(
                    fontSize: 28,
                    fontWeight: FontWeight.w700,
                    color: Colors.grey[800],
                  ),
                ),
                const SizedBox(height: 16),
                Text(
                  context
                      .loc
                      .complete_all_challenges_desc, // Uses extension method
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.grey[600],
                    height: 1.5,
                  ),
                ),
                const SizedBox(height: 32),
              ],
            ),
            Align(
              alignment: Alignment.bottomCenter,
              child: Padding(
                padding: const EdgeInsets.only(bottom: 32.0),
                child: VButtonGradient(
                  // Now correctly referenced
                  title: context.loc.back, // Uses extension method
                  onTap: context.pop,
                  isBorder: false,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
