import 'package:flutter/material.dart';
import 'package:selfeng/shared/localization/app_localizations_context.dart';
import 'package:selfeng/shared/widgets/widgets.dart';

class NextButton extends StatelessWidget {
  const NextButton({
    super.key,
    this.onTap,
    this.isBackgroundColor = true,
    this.child,
  });

  final VoidCallback? onTap;
  final bool isBackgroundColor;
  final child;

  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: Alignment.bottomCenter,
      child: Container(
        height: 115,
        padding: const EdgeInsets.symmetric(vertical: 24, horizontal: 32),
        decoration: !isBackgroundColor
            ? null
            : const BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Color(0xffFFF2F2),
                    Color(0xffFDD8D8),
                    Color(0xffFFECEC),
                    Color(0xffFFFFFF),
                  ],
                  begin: Alignment.bottomLeft,
                  end: Alignment.topRight,
                ),
                boxShadow: [
                  BoxShadow(
                    color: Color(0x33000000),
                    blurRadius: 40,
                    offset: Offset(0, 4),
                  ),
                ],
              ),
        child:
            child ??
            VButtonGradient(
              title: context.loc.next,
              onTap: onTap!,
              isBorder: true,
            ),
      ),
    );
  }
}
