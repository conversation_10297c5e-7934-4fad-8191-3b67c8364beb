import 'package:flutter/material.dart';
import 'dart:math' as math;

class ScoreProgressIndicator extends StatefulWidget {
  final int score;
  final double strokeWidth;
  final double width;
  final double height;
  final bool isHalfCircle;

  const ScoreProgressIndicator({
    super.key,
    required this.score,
    this.strokeWidth = 10.0,
    this.width = 100.0,
    this.height = 100.0,
    this.isHalfCircle = false,
  });

  @override
  State<ScoreProgressIndicator> createState() => _ScoreProgressIndicatorState();
}

class _ScoreProgressIndicatorState extends State<ScoreProgressIndicator>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scoreAnimation;
  late Animation<double> _progressAnimation;

  @override
  void initState() {
    super.initState();

    // Calculate proportional duration based on score
    // Max duration is 1800ms for score 100, proportional for lower scores
    final proportionalDuration = ((widget.score / 100.0) * 1800).round();
    final duration = proportionalDuration.clamp(100, 1800); // Minimum 100ms

    _animationController = AnimationController(
      duration: Duration(milliseconds: duration),
      vsync: this,
    );

    _scoreAnimation = Tween<double>(begin: 0.0, end: widget.score.toDouble())
        .animate(
          CurvedAnimation(
            parent: _animationController,
            curve: Curves.easeOutCubic,
          ),
        );

    _progressAnimation = Tween<double>(begin: 0.0, end: widget.score / 100.0)
        .animate(
          CurvedAnimation(
            parent: _animationController,
            curve: Curves.easeOutCubic,
          ),
        );

    // Start the animation
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Color _getScoreColor(double score) {
    return score > 90
        ? const Color(0xff36AA34)
        : score > 70
        ? const Color(0xffF5BE48)
        : const Color(0xff93000F);
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: widget.width,
      height: widget.isHalfCircle ? widget.height : widget.width,
      child: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          final currentScore = _scoreAnimation.value;
          final currentProgress = _progressAnimation.value;

          return Stack(
            children: [
              if (!widget.isHalfCircle)
                Align(
                  alignment: Alignment.center,
                  child: Text(
                    '${currentScore.round()}',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      color: _getScoreColor(currentScore),
                    ),
                  ),
                ),
              SizedBox(
                width: widget.width,
                height: widget.isHalfCircle ? widget.height : widget.width,
                child: CustomPaint(
                  painter: _CurvedProgressPainter(
                    progress: currentProgress,
                    backgroundColor: const Color(0xffEDE0DE),
                    progressColor: _getScoreColor(currentScore),
                    strokeWidth: widget.strokeWidth,
                    isHalfCircle: widget.isHalfCircle,
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }
}

class _CurvedProgressPainter extends CustomPainter {
  final double progress;
  final Color backgroundColor;
  final Color progressColor;
  final double strokeWidth;
  final bool isHalfCircle;

  _CurvedProgressPainter({
    required this.progress,
    required this.backgroundColor,
    required this.progressColor,
    required this.strokeWidth,
    required this.isHalfCircle,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final Paint backgroundPaint = Paint()
      ..color = backgroundColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = strokeWidth
      ..strokeCap = StrokeCap.round;

    final Paint progressPaint = Paint()
      ..color = progressColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = strokeWidth
      ..strokeCap = StrokeCap.round;

    final Rect rect = isHalfCircle
        ? Rect.fromLTRB(
            strokeWidth / 2,
            strokeWidth / 2,
            size.width - strokeWidth / 2,
            size.height * 2 - strokeWidth / 2,
          )
        : Rect.fromLTRB(
            strokeWidth / 2,
            strokeWidth / 2,
            size.width - strokeWidth / 2,
            size.height - strokeWidth / 2,
          );

    if (isHalfCircle) {
      // Half circle mode (unchanged)
      canvas.drawArc(rect, math.pi, math.pi, false, backgroundPaint);

      canvas.drawArc(rect, math.pi, math.pi * progress, false, progressPaint);
    } else {
      // Full circle mode (clockwise from top)
      canvas.drawArc(
        rect,
        math.pi / 2, // Start from bottom
        math.pi * 2,
        false,
        backgroundPaint,
      );

      canvas.drawArc(
        rect,
        0, // Start from 3 o'clock (right side)
        math.pi * 2 * progress,
        false,
        progressPaint,
      );
    }
  }

  @override
  bool shouldRepaint(_CurvedProgressPainter oldDelegate) {
    return oldDelegate.progress != progress ||
        oldDelegate.backgroundColor != backgroundColor ||
        oldDelegate.progressColor != progressColor ||
        oldDelegate.strokeWidth != strokeWidth ||
        oldDelegate.isHalfCircle != isHalfCircle;
  }
}
