part of 'package:selfeng/configs/routes/core_routes.dart';

@TypedGoRoute<ChapterTitleRoute>(
  path: '/main-lesson/chapter-title/:level/:chapter',
  name: RouterName.chapterTitle,
)
class ChapterTitleRoute extends GoRouteData with $ChapterTitleRoute {
  const ChapterTitleRoute(this.level, this.chapter);
  final String level;
  final String chapter;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return DeferredRoute(
      chapter_title.loadLibrary,
      () => chapter_title.ChapterTitleScreen(
        level: state.pathParameters['level'] ?? 'A1',
        chapter: state.pathParameters['chapter'] ?? '1',
      ),
    );
  }
}

//////////////////////////Pronunciation Challenge Route

@TypedGoRoute<PronunciationChallengeRoute>(
  path: '/main-lesson/pronunciation/:level/:chapter/:path',
  name: RouterName.pronunciationChallenge,
)
class PronunciationChallengeRoute extends GoRouteData
    with $PronunciationChallengeRoute {
  const PronunciationChallengeRoute(this.level, this.chapter, this.path);
  final String level;
  final String chapter;
  final String path;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return DeferredRoute(
      pronunciation_challenge.loadLibrary,
      () => pronunciation_challenge.PronunciationChallengeScreen(
        level: state.pathParameters['level'] ?? 'A1',
        chapter: state.pathParameters['chapter'] ?? '1',
        path: state.pathParameters['path'] ?? 'blankpath',
      ),
    );
  }
}

@TypedGoRoute<PronunciationChallengeOnboardingRoute>(
  path: '/main-lesson/pronunciation-onboarding/:level/:chapter',
  name: RouterName.pronunciationChallengeOnboarding,
)
class PronunciationChallengeOnboardingRoute extends GoRouteData
    with $PronunciationChallengeOnboardingRoute {
  const PronunciationChallengeOnboardingRoute(this.level, this.chapter);
  final String level;
  final String chapter;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return DeferredRoute(
      pronunciation_challenge_onboarding.loadLibrary,
      () =>
          pronunciation_challenge_onboarding.PronunciationChallengeOnboardingScreen(
            level: state.pathParameters['level'] ?? 'A1',
            chapter: state.pathParameters['chapter'] ?? '1',
          ),
    );
  }
}

@TypedGoRoute<PronunciationChallengeInstructionRoute>(
  path: '/main-lesson/pronunciation-instruction/:level/:chapter',
  name: RouterName.pronunciationChallengeInstruction,
)
class PronunciationChallengeInstructionRoute extends GoRouteData
    with $PronunciationChallengeInstructionRoute {
  const PronunciationChallengeInstructionRoute(this.level, this.chapter);
  final String level;
  final String chapter;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return DeferredRoute(
      pronunciation_challenge_instruction.loadLibrary,
      () =>
          pronunciation_challenge_instruction.PronunciationChallengeInstructionScreen(
            level: state.pathParameters['level'] ?? 'A1',
            chapter: state.pathParameters['chapter'] ?? '1',
          ),
    );
  }
}

@TypedGoRoute<PronunciationChallengeContentResultRoute>(
  path: '/main-lesson/pronunciation-content-result/:level/:chapter/:path',
  name: RouterName.pronunciationChallengeContentResult,
)
class PronunciationChallengeContentResultRoute extends GoRouteData
    with $PronunciationChallengeContentResultRoute {
  const PronunciationChallengeContentResultRoute(
    this.level,
    this.chapter,
    this.path,
  );
  final String level;
  final String chapter;
  final String path;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return DeferredRoute(
      pronunciation_challenge_content_result.loadLibrary,
      () =>
          pronunciation_challenge_content_result.PronunciationChallengeContentResultScreen(
            level: state.pathParameters['level'] ?? 'A1',
            chapter: state.pathParameters['chapter'] ?? '1',
            path: state.pathParameters['path'] ?? 'blankpath',
          ),
    );
  }
}

@TypedGoRoute<PronunciationChallengeResultRoute>(
  path: '/main-lesson/pronunciation-result/:level/:chapter/:path',
  name: RouterName.pronunciationChallengeResult,
)
class PronunciationChallengeResultRoute extends GoRouteData
    with $PronunciationChallengeResultRoute {
  const PronunciationChallengeResultRoute(this.level, this.chapter, this.path);
  final String level;
  final String chapter;
  final String path;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return DeferredRoute(
      pronunciation_challenge_result.loadLibrary,
      () => pronunciation_challenge_result.PronunciationChallengeResultScreen(
        level: state.pathParameters['level'] ?? 'A1',
        chapter: state.pathParameters['chapter'] ?? '1',
        path: state.pathParameters['path'] ?? 'blankpath',
      ),
    );
  }
}

//////////////////////////Video Conversation Route
@TypedGoRoute<VideoConversationRoute>(
  path: '/main-lesson/conversation-video/:level/:chapter/:path',
  name: RouterName.conversationVideo,
)
class VideoConversationRoute extends GoRouteData with $VideoConversationRoute {
  const VideoConversationRoute(this.level, this.chapter, this.path);
  final String level;
  final String chapter;
  final String path;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return DeferredRoute(
      conversation_video.loadLibrary,
      () => conversation_video.ConversationVideoScreen(
        level: state.pathParameters['level'] ?? 'A1',
        chapter: state.pathParameters['chapter'] ?? '1',
        path: state.pathParameters['path'] ?? 'blankpath',
      ),
    );
  }
}

@TypedGoRoute<VideoConversationOnboardingRoute>(
  path: '/main-lesson/conversation-video-onboarding/:level/:chapter',
  name: RouterName.conversationVideoOnboarding,
)
class VideoConversationOnboardingRoute extends GoRouteData
    with $VideoConversationOnboardingRoute {
  const VideoConversationOnboardingRoute(this.level, this.chapter);
  final String level;
  final String chapter;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return DeferredRoute(
      conversation_video_onboarding.loadLibrary,
      () => conversation_video_onboarding.ConversationVideoOnboardingScreen(
        level: state.pathParameters['level'] ?? 'A1',
        chapter: state.pathParameters['chapter'] ?? '1',
      ),
    );
  }
}

@TypedGoRoute<VideoConversationInstructionRoute>(
  path: '/main-lesson/conversation-video-instruction/:level/:chapter',
  name: RouterName.conversationVideoInstruction,
)
class VideoConversationInstructionRoute extends GoRouteData
    with $VideoConversationInstructionRoute {
  const VideoConversationInstructionRoute(this.level, this.chapter);
  final String level;
  final String chapter;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return DeferredRoute(
      conversation_video_instruction.loadLibrary,
      () => conversation_video_instruction.ConversationVideoInstructionScreen(
        level: state.pathParameters['level'] ?? 'A1',
        chapter: state.pathParameters['chapter'] ?? '1',
      ),
    );
  }
}

@TypedGoRoute<VideoConversationResultRoute>(
  path: '/main-lesson/conversation-video-result/:level/:chapter',
  name: RouterName.conversationVideoResult,
)
class VideoConversationResultRoute extends GoRouteData
    with $VideoConversationResultRoute {
  const VideoConversationResultRoute(this.level, this.chapter);
  final String level;
  final String chapter;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return DeferredRoute(
      conversation_video_result.loadLibrary,
      () => conversation_video_result.ConversationVideoResultScreen(
        level: state.pathParameters['level'] ?? 'A1',
        chapter: state.pathParameters['chapter'] ?? '1',
      ),
    );
  }
}

//////////////////////////Pronunciation Challenge Route
@TypedGoRoute<ListeningMasteryRoute>(
  path: '/main-lesson/listening-mastery/:level/:chapter/:path',
  name: RouterName.listeningMastery,
)
class ListeningMasteryRoute extends GoRouteData with $ListeningMasteryRoute {
  const ListeningMasteryRoute(this.level, this.chapter, this.path);
  final String level;
  final String chapter;
  final String path;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return DeferredRoute(
      listening_mastery.loadLibrary,
      () => listening_mastery.ListeningMasteryScreen(
        level: state.pathParameters['level'] ?? 'A1',
        chapter: state.pathParameters['chapter'] ?? '1',
        path: state.pathParameters['path'] ?? 'blankpath',
      ),
    );
  }
}

@TypedGoRoute<ListeningMasteryOnboardingRoute>(
  path: '/main-lesson/listening-mastery-onboarding/:level/:chapter',
  name: RouterName.listeningMasteryOnboarding,
)
class ListeningMasteryOnboardingRoute extends GoRouteData
    with $ListeningMasteryOnboardingRoute {
  const ListeningMasteryOnboardingRoute(this.level, this.chapter);
  final String level;
  final String chapter;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return DeferredRoute(
      listening_mastery_onboarding.loadLibrary,
      () => listening_mastery_onboarding.ListeningMasteryOnboardingScreen(
        level: state.pathParameters['level'] ?? 'A1',
        chapter: state.pathParameters['chapter'] ?? '1',
      ),
    );
  }
}

@TypedGoRoute<ListeningMasteryInstructionRoute>(
  path: '/main-lesson/listening-mastery-instruction/:level/:chapter',
  name: RouterName.listeningMasteryInstruction,
)
class ListeningMasteryInstructionRoute extends GoRouteData
    with $ListeningMasteryInstructionRoute {
  const ListeningMasteryInstructionRoute(this.level, this.chapter);
  final String level;
  final String chapter;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return DeferredRoute(
      listening_mastery_instruction.loadLibrary,
      () => listening_mastery_instruction.ListeningMasteryInstructionScreen(
        level: state.pathParameters['level'] ?? 'A1',
        chapter: state.pathParameters['chapter'] ?? '1',
      ),
    );
  }
}

// @TypedGoRoute<ListeningMasteryContentResultRoute>(
//   path: '/main-lesson/listening-content-result/:level/:chapter/:path',
//   name: RouterName.listeningMasteryContentResult,
// )
// class ListeningMasteryContentResultRoute extends GoRouteData {
//   const ListeningMasteryContentResultRoute(this.level, this.chapter, this.path);
//   final String level;
//   final String chapter;
//   final String path;

//   @override
//   Widget build(BuildContext context, GoRouterState state) {
//     return DeferredRoute(
//       listening_mastery_content_result_screen.loadLibrary,
//       () =>
//           listening_mastery_content_result_screen.ListeningMasteryContentResultScreen(
//             level: state.pathParameters['level'] ?? 'A1',
//             chapter: state.pathParameters['chapter'] ?? '1',
//             path: state.pathParameters['path'] ?? 'blankpath',
//           ),
//     );
//   }
// }

@TypedGoRoute<ListeningMasteryResultRoute>(
  path: '/main-lesson/listening-result/:level/:chapter/:path',
  name: RouterName.listeningMasteryResult,
)
class ListeningMasteryResultRoute extends GoRouteData
    with $ListeningMasteryResultRoute {
  const ListeningMasteryResultRoute(this.level, this.chapter, this.path);
  final String level;
  final String chapter;
  final String path;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return DeferredRoute(
      listening_mastery_result_screen.loadLibrary,
      () => listening_mastery_result_screen.ListeningMasteryResultScreen(
        level: state.pathParameters['level'] ?? 'A1',
        chapter: state.pathParameters['chapter'] ?? '1',
        path: state.pathParameters['path'] ?? 'blankpath',
      ),
    );
  }
}

//////////////////////////Speaking Arena Route
@TypedGoRoute<SpeakingArenaRoute>(
  path: '/main-lesson/speaking-arena/:level/:chapter/:path',
  name: RouterName.speakingArena,
)
class SpeakingArenaRoute extends GoRouteData with $SpeakingArenaRoute {
  const SpeakingArenaRoute(this.level, this.chapter, this.path);
  final String level;
  final String chapter;
  final String path;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return DeferredRoute(
      speaking_arena.loadLibrary,
      () => speaking_arena.SpeakingArenaScreen(
        level: state.pathParameters['level'] ?? 'A1',
        chapter: state.pathParameters['chapter'] ?? '1',
        path: state.pathParameters['path'] ?? 'blankpath',
      ),
    );
  }
}

@TypedGoRoute<SpeakingArenaStageRoute>(
  path: '/main-lesson/speaking-arena-stage/:level/:chapter/:path/:stage',
  name: RouterName.speakingArenaStage,
)
class SpeakingArenaStageRoute extends GoRouteData
    with $SpeakingArenaStageRoute {
  const SpeakingArenaStageRoute(
    this.level,
    this.chapter,
    this.path,
    this.stage,
  );
  final String level;
  final String chapter;
  final String path;
  final String stage;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return DeferredRoute(
      speaking_arena_stage.loadLibrary,
      () => speaking_arena_stage.SpeakingArenaStageScreen(
        level: state.pathParameters['level'] ?? 'A1',
        chapter: state.pathParameters['chapter'] ?? '1',
        path: state.pathParameters['path'] ?? 'blankpath',
        stage: state.pathParameters['stage'] ?? 'stage2',
      ),
    );
  }
}

@TypedGoRoute<SpeakingArenaOnboardingRoute>(
  path: '/main-lesson/speaking-arena-onboarding/:level/:chapter',
  name: RouterName.speakingArenaOnboarding,
)
class SpeakingArenaOnboardingRoute extends GoRouteData
    with $SpeakingArenaOnboardingRoute {
  const SpeakingArenaOnboardingRoute(this.level, this.chapter);
  final String level;
  final String chapter;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return DeferredRoute(
      speaking_arena_onboarding.loadLibrary,
      () => speaking_arena_onboarding.SpeakingArenaOnboardingScreen(
        level: state.pathParameters['level'] ?? 'A1',
        chapter: state.pathParameters['chapter'] ?? '1',
      ),
    );
  }
}

@TypedGoRoute<SpeakingArenaInstructionRoute>(
  path: '/main-lesson/speaking-arena-instruction/:level/:chapter',
  name: RouterName.speakingArenaInstruction,
)
class SpeakingArenaInstructionRoute extends GoRouteData
    with $SpeakingArenaInstructionRoute {
  const SpeakingArenaInstructionRoute(this.level, this.chapter);
  final String level;
  final String chapter;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return DeferredRoute(
      speaking_arena_instruction.loadLibrary,
      () => speaking_arena_instruction.SpeakingArenaInstructionScreen(
        level: state.pathParameters['level'] ?? 'A1',
        chapter: state.pathParameters['chapter'] ?? '1',
      ),
    );
  }
}

@TypedGoRoute<SpeakingArenaContentResultRoute>(
  path:
      '/main-lesson/speaking-arena-content-result/:level/:chapter/:path/:stage',
  name: RouterName.speakingArenaContentResult,
)
class SpeakingArenaContentResultRoute extends GoRouteData
    with $SpeakingArenaContentResultRoute {
  const SpeakingArenaContentResultRoute(
    this.level,
    this.chapter,
    this.path,
    this.stage,
  );
  final String level;
  final String chapter;
  final String path;
  final String stage;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return DeferredRoute(
      speaking_arena_content_result.loadLibrary,
      () => speaking_arena_content_result.SpeakingArenaContentResultScreen(
        level: state.pathParameters['level'] ?? 'A1',
        chapter: state.pathParameters['chapter'] ?? '1',
        path: state.pathParameters['path'] ?? 'blankpath',
        stage: state.pathParameters['stage'] ?? 'stage2',
      ),
    );
  }
}

@TypedGoRoute<SpeakingArenaResultRoute>(
  path: '/main-lesson/speaking-arena-result/:level/:chapter/:path/:stage',
  name: RouterName.speakingArenaResult,
)
class SpeakingArenaResultRoute extends GoRouteData
    with $SpeakingArenaResultRoute {
  const SpeakingArenaResultRoute(
    this.level,
    this.chapter,
    this.path,
    this.stage,
  );
  final String level;
  final String chapter;
  final String path;
  final String stage;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return DeferredRoute(
      speaking_arena_result.loadLibrary,
      () => speaking_arena_result.SpeakingArenaResultScreen(
        level: state.pathParameters['level'] ?? 'A1',
        chapter: state.pathParameters['chapter'] ?? '1',
        path: state.pathParameters['path'] ?? 'blankpath',
        stage: state.pathParameters['stage'] ?? 'stage3',
      ),
    );
  }
}

@TypedGoRoute<CertificateNotificationRoute>(
  path: '/main-lesson/certificate-notification/:level',
  name: RouterName.certificateNotification,
)
class CertificateNotificationRoute extends GoRouteData
    with $CertificateNotificationRoute {
  const CertificateNotificationRoute(this.level);
  final String level;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return DeferredRoute(
      certificate_notification.loadLibrary,
      () => certificate_notification.CertificateNotificationScreen(
        level: state.pathParameters['level'] ?? 'A1',
      ),
    );
  }
}
