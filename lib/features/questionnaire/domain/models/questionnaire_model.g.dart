// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'questionnaire_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_QuestionnaireModel _$QuestionnaireModelFromJson(Map<String, dynamic> json) =>
    _QuestionnaireModel(
      questionId: json['questionId'] as String?,
      status: json['status'] as bool? ?? false,
      choices: (json['choices'] as List<dynamic>?)
          ?.map((e) => Choices.fromJson(e as Map<String, dynamic>))
          .toList(),
      order: (json['order'] as num?)?.toInt(),
      question: json['question'] as Map<String, dynamic>?,
      type: (json['type'] as num?)?.toInt(),
      multipleAnswer: json['multiple_answer'] as bool? ?? false,
      answer: json['answer'],
    );

Map<String, dynamic> _$QuestionnaireModelToJson(_QuestionnaireModel instance) =>
    <String, dynamic>{
      'questionId': instance.questionId,
      'answer': instance.answer,
    };

_Choices _$ChoicesFromJson(Map<String, dynamic> json) => _Choices(
  image: json['image'] as String?,
  imageUrl: json['image_url'] as String?,
  value: json['value'] as String?,
  text: json['text'] as Map<String, dynamic>?,
);

Map<String, dynamic> _$ChoicesToJson(_Choices instance) => <String, dynamic>{
  'image': instance.image,
  'image_url': instance.imageUrl,
  'value': instance.value,
  'text': instance.text,
};
