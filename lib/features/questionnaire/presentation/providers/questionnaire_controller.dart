import 'package:flutter/material.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:selfeng/features/questionnaire/domain/models/questionnaire_model.dart';
import 'package:selfeng/features/questionnaire/domain/providers/questionnaire_provider.dart';
import 'package:selfeng/features/questionnaire/domain/repositories/questionnaire_repository.dart';
import 'package:selfeng/services/crashlytics_service/domain/providers/crashlytics_service_provider.dart';

import 'state/questionnaire_state.dart';

part 'questionnaire_controller.g.dart';

/// This controller is an [AsyncNotifier] that holds and handles our authentication state
@riverpod
class QuestionnaireController extends _$QuestionnaireController {
  late QuestionnaireRepository questionnaireRepository;

  @override
  FutureOr<QuestionnaireState> build() async {
    questionnaireRepository = ref.watch(questionnaireRepositoryProvider);

    // Set Crashlytics context
    final crashlytics = ref.read(crashlyticsServiceProvider);
    await crashlytics.setCustomKey('feature', 'questionnaire');
    await crashlytics.setCustomKey('controller', 'questionnaire_controller');

    return init();
  }

  FutureOr<QuestionnaireState> init() async {
    List<QuestionnaireModel> respond = [];
    state = const AsyncLoading();

    try {
      final result = await questionnaireRepository.getListQuestionnaire();
      result.fold(
        (failure) {
          state = AsyncError(failure.message, StackTrace.current);
        },
        (data) {
          respond = data;
        },
      );
    } catch (error, stackTrace) {
      final crashlytics = ref.read(crashlyticsServiceProvider);
      await crashlytics.recordError(
        error,
        stackTrace,
        reason: 'Failed to load questionnaire data',
        context: {
          'category': 'business_logic',
          'feature': 'questionnaire',
          'operation': 'get_list_questionnaire',
        },
        fatal: false,
      );
      state = AsyncError('Failed to load questionnaire data', stackTrace);
      respond = []; // Return empty list on error
    }

    return QuestionnaireState(
      questions: respond,
      controllerScroll: ScrollController(),
    );
  }

  Future<void> saveAnswer() async {
    try {
      final result = await questionnaireRepository.saveAnswer(
        state.value!.questions.map((item) => item.toJson()).toList(),
      );
      result.fold((failure) {
        state = AsyncError(failure.message, StackTrace.current);
      }, (data) {});
    } catch (error, stackTrace) {
      final crashlytics = ref.read(crashlyticsServiceProvider);
      await crashlytics.recordError(
        error,
        stackTrace,
        reason: 'Failed to save questionnaire answers',
        context: {
          'category': 'business_logic',
          'feature': 'questionnaire',
          'operation': 'save_answer',
          'question_count': state.value?.questions.length ?? 0,
        },
        fatal: false,
      );
      state = AsyncError('Failed to save answers', stackTrace);
    }
  }

  void answer(int index, dynamic value) {
    try {
      List<QuestionnaireModel> temp = List.from(state.value!.questions);
      if (temp[index].multipleAnswer) {
        if (temp[index].answer == null) {
          temp[index] = temp[index].copyWith(answer: []);
        }
        List<String> tempAnswer = List.from(temp[index].answer);
        if (tempAnswer.contains(value)) {
          tempAnswer.remove(value);
        } else {
          tempAnswer.add(value);
        }
        temp[index] = temp[index].copyWith(answer: tempAnswer);
      } else {
        temp[index] = temp[index].copyWith(answer: value);
      }
      state = AsyncData(state.value!.copyWith(questions: temp));
    } catch (error, stackTrace) {
      final crashlytics = ref.read(crashlyticsServiceProvider);
      crashlytics
          .recordError(
            error,
            stackTrace,
            reason: 'Failed to update questionnaire answer',
            context: {
              'category': 'ui',
              'feature': 'questionnaire',
              'operation': 'answer_question',
              'question_index': index,
              'answer_value': value.toString(),
              'question_count': state.value?.questions.length ?? 0,
            },
            fatal: false,
          )
          .catchError((e) => debugPrint('Failed to report UI error: $e'));
    }
  }

  Future nextPage(BuildContext context, double width) async {
    try {
      if (state.value!.currentQuestion < state.value!.questionLength) {
        state = AsyncData(
          state.value!.copyWith(
            currentQuestion: state.value!.currentQuestion + 1,
          ),
        );
        await state.value?.controllerScroll?.animateTo(
          (state.value?.currentQuestion ?? 0) * (width - 32),
          duration: const Duration(seconds: 1),
          curve: Curves.ease,
        );
      }
    } catch (error, stackTrace) {
      final crashlytics = ref.read(crashlyticsServiceProvider);
      await crashlytics.recordError(
        error,
        stackTrace,
        reason: 'Failed to navigate to next question',
        context: {
          'category': 'ui',
          'feature': 'questionnaire',
          'operation': 'next_page',
          'current_question': state.value?.currentQuestion ?? 0,
          'total_questions': state.value?.questionLength ?? 0,
        },
        fatal: false,
      );
    }
  }

  Future backPage(double width) async {
    try {
      if (state.value!.currentQuestion != 0) {
        state = AsyncData(
          state.value!.copyWith(
            currentQuestion: state.value!.currentQuestion - 1,
          ),
        );
      }
      await state.value?.controllerScroll?.animateTo(
        (state.value?.currentQuestion ?? 0) * (width - 32),
        duration: const Duration(seconds: 1),
        curve: Curves.ease,
      );
    } catch (error, stackTrace) {
      final crashlytics = ref.read(crashlyticsServiceProvider);
      await crashlytics.recordError(
        error,
        stackTrace,
        reason: 'Failed to navigate to previous question',
        context: {
          'category': 'ui',
          'feature': 'questionnaire',
          'operation': 'back_page',
          'current_question': state.value?.currentQuestion ?? 0,
          'total_questions': state.value?.questionLength ?? 0,
        },
        fatal: false,
      );
    }
  }
}
