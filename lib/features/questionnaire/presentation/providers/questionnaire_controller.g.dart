// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'questionnaire_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, type=warning
/// This controller is an [AsyncNotifier] that holds and handles our authentication state

@ProviderFor(QuestionnaireController)
const questionnaireControllerProvider = QuestionnaireControllerProvider._();

/// This controller is an [AsyncNotifier] that holds and handles our authentication state
final class QuestionnaireControllerProvider
    extends
        $AsyncNotifierProvider<QuestionnaireController, QuestionnaireState> {
  /// This controller is an [AsyncNotifier] that holds and handles our authentication state
  const QuestionnaireControllerProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'questionnaireControllerProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$questionnaireControllerHash();

  @$internal
  @override
  QuestionnaireController create() => QuestionnaireController();
}

String _$questionnaireControllerHash() =>
    r'556f8b90a8e3a0ff8eccf167d087885232613763';

/// This controller is an [AsyncNotifier] that holds and handles our authentication state

abstract class _$QuestionnaireController
    extends $AsyncNotifier<QuestionnaireState> {
  FutureOr<QuestionnaireState> build();
  @$mustCallSuper
  @override
  void runBuild() {
    final created = build();
    final ref =
        this.ref as $Ref<AsyncValue<QuestionnaireState>, QuestionnaireState>;
    final element =
        ref.element
            as $ClassProviderElement<
              AnyNotifier<AsyncValue<QuestionnaireState>, QuestionnaireState>,
              AsyncValue<QuestionnaireState>,
              Object?,
              Object?
            >;
    element.handleValue(ref, created);
  }
}
