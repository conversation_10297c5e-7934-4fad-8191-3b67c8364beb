part of 'package:selfeng/configs/routes/core_routes.dart';

@TypedGoRoute<QuestionnaireRoute>(
  path: '/questionnaire',
  name: RouterName.questionnaireScreen,
)
class QuestionnaireRoute extends GoRouteData with $QuestionnaireRoute {
  const QuestionnaireRoute();
  @override
  Widget build(BuildContext context, GoRouterState state) {
    return DeferredRoute(
      questionnaire.loadLibrary,
      () => questionnaire.QuestionnaireScreen(),
    );
  }
}

@TypedGoRoute<QuestionnaireFinishRoute>(
  path: '/questionnaire-finish',
  name: RouterName.questionnaireFinishScreen,
)
class QuestionnaireFinishRoute extends GoRouteData
    with $QuestionnaireFinishRoute {
  const QuestionnaireFinishRoute();
  @override
  Widget build(BuildContext context, GoRouterState state) {
    return DeferredRoute(
      questionnaire_finish.loadLibrary,
      () => questionnaire_finish.QuestionnaireFinishScreen(),
    );
  }
}
