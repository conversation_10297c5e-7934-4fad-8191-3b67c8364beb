part of 'package:selfeng/configs/routes/core_routes.dart';

@TypedGoRoute<SearchRoute>(path: '/search', name: RouterName.search)
class SearchRoute extends GoRouteData with $SearchRoute {
  const SearchRoute();

  @override
  Page<void> buildPage(BuildContext context, GoRouterState state) {
    // Prefetch next possible routes
    library_chapter.loadLibrary();

    return CustomPageTransition(
      child: DeferredRoute(
        search_screen.loadLibrary,
        () => search_screen.SearchScreen(),
      ),
    );
  }
}
