import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:selfeng/services/notification_service/domain/repositories/notification_service_repository.dart';
import 'package:selfeng/services/notification_service/domain/providers/notification_service_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

part 'notification_settings_provider.g.dart';

/// Notification settings notifier using the new Riverpod 3.0 API
@riverpod
class NotificationSettings extends _$NotificationSettings {
  @override
  Map<String, bool> build() {
    // Initialize with default settings
    final defaultSettings = {
      'general_notification': true,
      'promotion': true,
      'announcement': true,
      'study_reminder': true,
    };

    // Load settings asynchronously
    _loadSettings();

    return defaultSettings;
  }

  NotificationServiceRepository get _notificationService =>
      ref.read(notificationServiceProvider);

  /// Load notification settings from local storage
  Future<void> _loadSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final Map<String, bool> loadedSettings = {};

      for (final topic in state.keys) {
        loadedSettings[topic] = prefs.getBool('notification_$topic') ?? true;
      }

      state = loadedSettings;
    } catch (e) {
      // Keep default state if loading fails
    }
  }

  /// Toggle notification for a specific topic
  Future<void> toggleNotification(String topic) async {
    if (!state.containsKey(topic)) return;

    final newValue = !state[topic]!;

    // Update local state immediately for better UX
    state = {...state, topic: newValue};

    try {
      // Save to local storage
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('notification_$topic', newValue);

      // Subscribe/unsubscribe from FCM topic
      if (newValue) {
        await _notificationService.subscribeToTopic(topic);
      } else {
        await _notificationService.unsubscribeFromTopic(topic);
      }
    } catch (e) {
      // Revert state if operation failed
      state = {...state, topic: !newValue};
    }
  }

  /// Enable all notifications
  Future<void> enableAllNotifications() async {
    final Map<String, bool> allEnabled = {};

    for (final topic in state.keys) {
      allEnabled[topic] = true;
    }

    state = allEnabled;

    try {
      final prefs = await SharedPreferences.getInstance();

      for (final topic in state.keys) {
        await prefs.setBool('notification_$topic', true);
        await _notificationService.subscribeToTopic(topic);
      }
    } catch (e) {
      _loadSettings(); // Reload from storage on error
    }
  }

  /// Disable all notifications
  Future<void> disableAllNotifications() async {
    final Map<String, bool> allDisabled = {};

    for (final topic in state.keys) {
      allDisabled[topic] = false;
    }

    state = allDisabled;

    try {
      final prefs = await SharedPreferences.getInstance();

      for (final topic in state.keys) {
        await prefs.setBool('notification_$topic', false);
        await _notificationService.unsubscribeFromTopic(topic);
      }
    } catch (e) {
      _loadSettings(); // Reload from storage on error
    }
  }

  /// Get notification status for a specific topic
  bool isNotificationEnabled(String topic) {
    return state[topic] ?? false;
  }

  /// Initialize notification subscriptions based on current settings
  /// Call this after user authentication or app initialization
  Future<void> initializeSubscriptions() async {
    try {
      for (final entry in state.entries) {
        if (entry.value) {
          await _notificationService.subscribeToTopic(entry.key);
        } else {
          await _notificationService.unsubscribeFromTopic(entry.key);
        }
      }
    } catch (e) {
      // Handle exception silently
    }
  }
}

/// Provider to check if a specific notification is enabled
final notificationEnabledProvider = Provider.family<bool, String>((ref, topic) {
  final settings = ref.watch(notificationSettingsProvider);
  return settings[topic] ?? false;
});

/// Provider to check if all notifications are enabled
final allNotificationsEnabledProvider = Provider<bool>((ref) {
  final settings = ref.watch(notificationSettingsProvider);
  return settings.values.every((enabled) => enabled);
});

/// Provider to check if any notification is enabled
final anyNotificationEnabledProvider = Provider<bool>((ref) {
  final settings = ref.watch(notificationSettingsProvider);
  return settings.values.any((enabled) => enabled);
});
