import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:selfeng/features/setting/domain/providers/setting_provider.dart';
import 'package:selfeng/features/setting/domain/repositories/setting_repository.dart';
import 'package:selfeng/services/setting_cache_service/domain/providers/setting_cache_provider.dart';
import 'package:selfeng/services/setting_cache_service/domain/repositories/setting_cache_repository.dart';
import 'package:selfeng/services/user_data_service/domain/providers/user_data_service_provider.dart';
import 'package:selfeng/services/user_data_service/domain/repositories/user_data_service_repository.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:selfeng/shared/exceptions/unauthorized_exception.dart';
import 'package:selfeng/shared/helpers/navigation_helper.dart';
import 'package:selfeng/configs/routes/core_router_name.dart';
import 'package:selfeng/services/crashlytics_service/utils/error_handler.dart';
import 'package:selfeng/services/crashlytics_service/domain/providers/crashlytics_service_provider.dart';

import 'state/setting_state.dart';

part 'setting_controller.g.dart';

/// This controller is an [AsyncNotifier] that holds and handles our authentication state
@riverpod
class SettingController extends _$SettingController {
  late SettingCacheRepository settingCacheRepository;
  late SettingRepository settingRepository;
  late UserDataServiceRepository userDataServiceRepository;
  ErrorHandler? errorHandler;
  bool _listenerInitialized = false;

  @override
  Future<SettingState> build() async {
    settingCacheRepository = ref.watch(settingLocalRepositoryProvider);
    settingRepository = ref.watch(settingRepositoryProvider);
    userDataServiceRepository = ref.watch(userDataServiceProvider);

    // Initialize crashlytics and error handler after build phase
    _initializeCrashlyticsAsync();

    // Only set up the listener once
    if (!_listenerInitialized) {
      _persistenceRefreshLogicSetting();
      _listenerInitialized = true;
    }

    if (errorHandler != null) {
      return await errorHandler!.handleAsync(
            () => _settingRecoveryAttempt(),
            context: 'Setting Recovery',
            metadata: {'operation': 'initialize_settings'},
            fallbackValue: const SettingState(),
            fatal: false,
          ) ??
          const SettingState();
    } else {
      try {
        return await _settingRecoveryAttempt();
      } catch (e) {
        debugPrint('Setting recovery failed: $e');
        return const SettingState();
      }
    }
  }

  /// Tries to recover the saved locale from persistent storage.
  /// If no locale is found, returns a default SettingState with default locale.
  /// If an error occurs during retrieval, deletes any corrupted data and returns default state.
  Future<SettingState> _settingRecoveryAttempt() async {
    try {
      final savedLocale = await settingCacheRepository.fetchLocale();
      return savedLocale.fold(
        (l) {
          // No locale found - return default state (this is normal for first launch)
          return const SettingState();
        },
        (r) {
          // Locale found - return state with saved locale
          return SettingState(locale: r);
        },
      );
    } catch (_) {
      // Error occurred during retrieval - clean up and return default state
      settingCacheRepository.deleteLocale().ignore();
      return const SettingState();
    }
  }

  /// Mock of a successful login attempt, which results come from the network.
  Future<void> saveLocale(int index) async {
    try {
      ref
          .read(crashlyticsServiceProvider)
          .setCustomKey('operation', 'save_locale');
      await settingCacheRepository.saveLocale(
        locale: state.value!.languageList[index].localValue!,
      );
      state = AsyncData(
        state.value!.copyWith(
          locale: state.value!.languageList[index].localValue!,
          selectedIndex: index,
        ),
      );
    } catch (error, stackTrace) {
      await errorHandler?.reportBusinessLogicError(
        error,
        stackTrace,
        feature: 'settings',
        operation: 'save_locale',
        businessContext: {
          'selected_index': index,
          'locale_value': state.value!.languageList[index].localValue,
        },
      );
      rethrow;
    }
  }

  Future<void> isNewUser(dynamic context) async {
    try {
      ref
          .read(crashlyticsServiceProvider)
          .setCustomKey('operation', 'check_new_user');
      final result = await settingRepository.isNewUser();

      result.fold(
        (l) => throw const UnauthorizedException('No auth token found'),
        (r) => state = AsyncData(state.value!.copyWith(isNewUser: r)),
      );

      if (!state.value!.isNewUser.contains('diagnosticTest') &&
          !state.value!.isNewUser.contains('selfAssesment')) {
        customNav(context, RouterName.questionnaireScreen);
      } else {
        customNav(context, RouterName.dashboardScreen);
      }
    } catch (error, stackTrace) {
      await errorHandler?.reportBusinessLogicError(
        error,
        stackTrace,
        feature: 'settings',
        operation: 'check_new_user',
        businessContext: {
          'current_is_new_user': state.value?.isNewUser,
          'navigation_context': context.toString(),
        },
      );
      rethrow;
    }
  }

  Future<void> nextPage(dynamic context) async {
    try {
      ref
          .read(crashlyticsServiceProvider)
          .setCustomKey('operation', 'navigate_next_page');
      final result = await userDataServiceRepository.getUserData();

      result.fold(
        (l) => throw const UnauthorizedException('No user data found'),
        (r) => {
          if (r.afterTest)
            {customNav(context, RouterName.dashboardScreen)}
          else
            {customNav(context, RouterName.questionnaireScreen)},
        },
      );
    } catch (error, stackTrace) {
      await errorHandler?.reportBusinessLogicError(
        error,
        stackTrace,
        feature: 'settings',
        operation: 'navigate_next_page',
        businessContext: {
          'navigation_context': context.toString(),
          'current_state': state.value?.toString(),
        },
      );
      rethrow;
    }
  }

  Future<void> selectTab(int index) async {
    state = AsyncData(state.value!.copyWith(tabIndex: index));
  }

  /// Initialize crashlytics service asynchronously to avoid setState during build
  void _initializeCrashlyticsAsync() {
    // Use microtask to defer initialization until after build phase
    Future.microtask(() async {
      try {
        final crashlytics = ref.read(crashlyticsServiceProvider);
        errorHandler = ErrorHandler(crashlytics);

        // Set custom keys for setting context
        await crashlytics.setCustomKey('feature', 'settings');
        await crashlytics.setCustomKey('controller', 'SettingController');
      } catch (e) {
        // Don't let crashlytics initialization errors break the app
        debugPrint('Failed to initialize crashlytics in SettingController: $e');
      }
    });
  }

  /// Internal method used to listen authentication state changes.
  /// When the auth object is in a loading state, nothing happens.
  /// When the auth object is in an error state, we choose to remove the token
  /// Otherwise, we expect the current auth value to be reflected in our persitence API
  void _persistenceRefreshLogicSetting() {
    listenSelf((_, next) {
      if (next.isLoading) return;
      if (next.hasError) {
        settingCacheRepository.deleteLocale();
        return;
      }

      // next.requireValue.locale<void>(
      //   signedIn: (signedIn) async => await SettingCacheRepository.saveLocale(user: signedIn.user),
      //   signedOut: (signedOut) async {
      //     await SettingCacheRepository.deleteUser();
      //   },
      //   initial: (Initial value) {  }, loading: (Loading value) {  }, failure: (Failure value) {  }, success: (Success value) {  },
      // );
    });
  }
}
