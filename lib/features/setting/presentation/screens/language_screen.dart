import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:selfeng/configs/routes/core_router_name.dart';
import 'package:selfeng/features/setting/presentation/providers/setting_controller.dart';
import 'package:selfeng/features/setting/presentation/providers/state/setting_state.dart';
import 'package:selfeng/features/setting/presentation/widgets/select_language.dart';
import 'package:selfeng/shared/helpers/navigation_helper.dart';
import 'package:selfeng/shared/localization/app_localizations_context.dart';
import 'package:selfeng/shared/widgets/widgets.dart';
import 'package:go_router/go_router.dart';

class LanguageScreen extends ConsumerStatefulWidget {
  final String origin;
  const LanguageScreen({super.key, required this.origin});

  @override
  ConsumerState<LanguageScreen> createState() => _LanguageScreenState();
}

class _LanguageScreenState extends ConsumerState<LanguageScreen> {
  @override
  Widget build(BuildContext context) {
    final viewState = ref.watch(settingControllerProvider);
    final viewModel = ref.watch(settingControllerProvider.notifier);

    return Scaffold(
      body: viewState.when(
        data: (settingState) => _buildContent(context, settingState, viewModel),
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stack) => Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.error, color: Colors.red, size: 48),
              const SizedBox(height: 16),
              Text('Error: $error'),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () => ref.refresh(settingControllerProvider),
                child: const Text('Retry'),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildContent(
    BuildContext context,
    SettingState settingState,
    SettingController viewModel,
  ) {
    // Add safety checks
    if (settingState.languageList.isEmpty) {
      return const Center(child: Text('No languages available'));
    }

    return Stack(
      children: [
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 82),
              Text(
                context.loc.selectLanguage,
                style: Theme.of(context).textTheme.titleLarge,
              ),
              const SizedBox(height: 4),
              Text(
                context.loc.selectLanguageDes,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: const Color(0xff998E8D),
                ),
              ),
              const SizedBox(height: 36),
              // Build language options dynamically
              ...settingState.languageList.asMap().entries.map((entry) {
                final index = entry.key;
                final language = entry.value;
                return Column(
                  children: [
                    SelectLanguage(
                      title: language.title,
                      icon: language.icon,
                      groupValue: settingState.selectedIndex,
                      value: index,
                      onTap: (val) {
                        viewModel.saveLocale(val);
                      },
                    ),
                    if (index < settingState.languageList.length - 1)
                      const SizedBox(height: 24),
                  ],
                );
              }),
            ],
          ),
        ),
        // Background image with safety check
        if (settingState.selectedIndex < settingState.languageList.length)
          Align(
            alignment: FractionalOffset.bottomCenter,
            child: Image.asset(
              settingState
                  .languageList[settingState.selectedIndex]
                  .imageBackground,
              fit: BoxFit.fitWidth,
              width: double.infinity,
              errorBuilder: (context, error, stackTrace) {
                return const SizedBox.shrink(); // Hide if image fails to load
              },
            ),
          ),
        Align(
          alignment: FractionalOffset.bottomCenter,
          child: Container(
            margin: const EdgeInsets.symmetric(vertical: 32, horizontal: 16),
            child: VButtonGradient(
              title: context.loc.choose,
              onTap: () async {
                if (widget.origin == 'profile') {
                  context.pop();
                } else {
                  customNav(
                    context,
                    RouterName.selectedLanguageScreen,
                    isReplace: true,
                  );
                }
              },
            ),
          ),
        ),
      ],
    );
  }
}
