part of 'package:selfeng/configs/routes/core_routes.dart';

@TypedGoRoute<LanguageRoute>(
  path: '/language/:origin',
  name: RouterName.languageScreen,
)
class LanguageRoute extends GoRouteData with $LanguageRoute {
  const LanguageRoute({required this.origin});
  final String origin;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return DeferredRoute(
      language.loadLibrary,
      () => language.LanguageScreen(origin: origin),
    );
  }
}

@TypedGoRoute<SelectedLanguageRoute>(
  path: "/language-selected",
  name: RouterName.selectedLanguageScreen,
)
class SelectedLanguageRoute extends GoRouteData with $SelectedLanguageRoute {
  const SelectedLanguageRoute();
  @override
  Widget build(BuildContext context, GoRouterState state) {
    return DeferredRoute(
      selected_language.loadLibrary,
      () => selected_language.SelectedLanguageScreen(),
    );
  }
}

@TypedGoRoute<ProfileSettingRoute>(
  path: '/profile-settings',
  name: RouterName.profilesettingScreen,
)
class ProfileSettingRoute extends GoRouteData with $ProfileSettingRoute {
  const ProfileSettingRoute();
  @override
  Widget build(BuildContext context, GoRouterState state) {
    return DeferredRoute(
      profile_setting.loadLibrary,
      () => profile_setting.ProfileSettingsScreen(),
    );
  }
}

@TypedGoRoute<CertificateListRoute>(
  path: '/certificate-list',
  name: RouterName.certificateListScreen,
)
class CertificateListRoute extends GoRouteData with $CertificateListRoute {
  const CertificateListRoute();
  @override
  Widget build(BuildContext context, GoRouterState state) {
    return DeferredRoute(
      certificate_list.loadLibrary,
      () => certificate_list.CertificateListScreen(),
    );
  }
}

@TypedGoRoute<CertificateRoute>(
  path: '/certificate/:level',
  name: RouterName.certificateScreen,
)
class CertificateRoute extends GoRouteData with $CertificateRoute {
  const CertificateRoute(this.level);
  final String level;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return DeferredRoute(
      certificate.loadLibrary,
      () => certificate.CertificateScreen(
        level: state.pathParameters['level'] ?? 'A1',
      ),
    );
  }
}

@TypedGoRoute<CertificateDetailRoute>(
  path: '/certificate-detail/:level',
  name: RouterName.certificateDetailScreen,
)
class CertificateDetailRoute extends GoRouteData with $CertificateDetailRoute {
  const CertificateDetailRoute(this.level);
  final String level;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return DeferredRoute(
      certificate_detail.loadLibrary,
      () => certificate_detail.CertificateDetailScreen(
        level: state.pathParameters['level'] ?? 'A1',
      ),
    );
  }
}

@TypedGoRoute<NotificationSettingRoute>(
  path: '/notification-settings',
  name: RouterName.notificationSettingScreen,
)
class NotificationSettingRoute extends GoRouteData
    with $NotificationSettingRoute {
  const NotificationSettingRoute();
  @override
  Widget build(BuildContext context, GoRouterState state) {
    return DeferredRoute(
      notification_setting.loadLibrary,
      () => notification_setting.NotificationSettingScreen(),
    );
  }
}
