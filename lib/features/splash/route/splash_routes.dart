part of 'package:selfeng/configs/routes/core_routes.dart';

@TypedGoRoute<SplashRoute>(path: '/splash', name: RouterName.splashScreen)
class SplashRoute extends GoRouteData with $SplashRoute {
  const SplashRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const SplashScreen();
  }
}

// @TypedGoRoute<PlaygroundRoute>(
//   path: '/playground',
//   name: RouterName.playgroundScreen,
// )
// class PlaygroundRoute extends GoRouteData {
//   const PlaygroundRoute();

//   @override
//   Widget build(BuildContext context, GoRouterState state) {
//     return const PlaygroundScreen();
//   }
// }

@TypedGoRoute<OnboardingRoute>(
  path: '/onboarding',
  name: RouterName.onboardingScreen,
)
class OnboardingRoute extends GoRouteData with $OnboardingRoute {
  const OnboardingRoute();

  // Without this static key, the dialog will not cover the navigation rail.
  // static final GlobalKey<NavigatorState> $parentNavigatorKey = shellNavigatorKey;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return DeferredRoute(
      onboarding.loadLibrary,
      () => onboarding.OnboardingScreen(),
    );
  }
}
