// File generated by FlutterFire CLI.
// ignore_for_file: lines_longer_than_80_chars, avoid_classes_with_only_static_members
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, TargetPlatform;
import 'package:selfeng/main/app_env.dart';

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static FirebaseOptions android = FirebaseOptions(
    apiKey: EnvInfo.firebaseApiKeyAndroid,
    appId: EnvInfo.firebaseAppIdAndroid,
    messagingSenderId: EnvInfo.firebaseMessagingSenderIdAndroid,
    projectId: EnvInfo.firebaseProjectId,
    storageBucket: EnvInfo.firebaseStorageBucket,
  );

  static FirebaseOptions ios = FirebaseOptions(
    apiKey: EnvInfo.firebaseApiKeyIos,
    appId: EnvInfo.firebaseAppIdIos,
    messagingSenderId: EnvInfo.firebaseMessagingSenderIdIos,
    projectId: EnvInfo.firebaseProjectId,
    storageBucket: EnvInfo.firebaseStorageBucket,
    androidClientId: EnvInfo.firebaseAndroidClientId,
    iosClientId: EnvInfo.firebaseIosClientId,
    iosBundleId: EnvInfo.firebaseIosBundleId,
  );
}
