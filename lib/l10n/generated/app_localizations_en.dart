// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get appTitle => 'Self Eng';

  @override
  String get home => 'Home';

  @override
  String get library => 'Library';

  @override
  String get search => 'Search';

  @override
  String get games => 'Games';

  @override
  String get profile => 'Profile';

  @override
  String get counterPage => 'Counter page';

  @override
  String get buttonPushedTimes => 'You have pushed the button this many times:';

  @override
  String get increment => 'Increment';

  @override
  String get somethingWentWrong => 'Something went wrong';

  @override
  String get imageNotAvailable => 'Image not available';

  @override
  String get onboarding1 => 'Start Your English\nAdventure! 🚀';

  @override
  String get onboarding2 => 'Learn English,\nLimitless!🤳';

  @override
  String get onboarding3 => 'Get Started Now! ⏩';

  @override
  String get onboardingSingup => 'Register with';

  @override
  String get signingIn => 'Signing in...';

  @override
  String get pleaseWait => 'Please wait while we authenticate you';

  @override
  String get selectLanguage => 'Select a language';

  @override
  String get selectLanguageDes => 'Please select the language you want to use.';

  @override
  String get languageIndo => 'Bahasa Indonesia';

  @override
  String get languageEngl => 'English';

  @override
  String get choose => 'Choose';

  @override
  String get other => 'Other';

  @override
  String get next => 'Next';

  @override
  String get previous => 'Previous';

  @override
  String get send => 'Send';

  @override
  String get later => 'Later';

  @override
  String get shortDescription => 'Describe briefly and clearly';

  @override
  String get questionnaireOtherQ => 'Mention your other main goals:';

  @override
  String get questionnaireOtherA => 'Type briefly';

  @override
  String get questionnaireFinish =>
      'Thank you for completing the self-assessment questionnaire!';

  @override
  String get questionnaireFinishDesc =>
      'Your responses will help tailor the learning plan to meet your specific needs, interests, and goals. Let\'s begin this English learning journey together! 🚀📚✨🧑‍🤝‍🧑🌏';

  @override
  String get questionnaireIWilling => 'Yes, let\'s begin';

  @override
  String get please_type => 'Please type here';

  @override
  String get please_type_number => 'Please type number';

  @override
  String get doIt => 'Do it';

  @override
  String get instruction => 'Instructions';

  @override
  String get testInstruction => 'Test Instructions';

  @override
  String get testInstructionDesc =>
      'Take a deep breath, focus, and read the instructions carefully before answering the questions';

  @override
  String get testInstructionRule =>
      '1. You have 15 minutes to complete the test.\n2. Read the questions and choose the correct answer.\n3. Choose the answer that best suits your understanding.\n4. Answer all questions, there is no penalty.\n5. The test automatically ends when time runs out.\n6. Do not use outside help.\n7. Get feedback after the test.';

  @override
  String get diagnosticTests => 'Diagnostic Tests';

  @override
  String get areYouReady => 'Are you ready?';

  @override
  String get yesIAmReady => 'Yes, I am ready.';

  @override
  String get sorry_not_ready_yet => 'Sorry, not ready yet.';

  @override
  String get areYouSure => 'Are you sure?';

  @override
  String get yesIAmSure => 'Yes, I am sure';

  @override
  String get notYetLater => 'Not yet, later';

  @override
  String get continue1 => 'Continue';

  @override
  String get timesUpStudyGroup1 =>
      'Oh no, the test time is up 😔. Click the button ';

  @override
  String get timesUpStudyGroup2 => '‘continue’';

  @override
  String get timesUpStudyGroup3 => ' to find out your study group';

  @override
  String get finallyResult => 'Finally, your test results are out 🎉';

  @override
  String get processingTime => 'Processing time';

  @override
  String get totalScore => 'Total Score';

  @override
  String get learningLevel => 'Learning Level';

  @override
  String get finallyResultDesc =>
      'Happy learning, take your understanding to the next level, and enjoy every moment of this journey!';

  @override
  String get questionnaireOnboarding1 =>
      'Welcome to the Diagnostic Test for the Integrated Self-Learning Speaking Program!';

  @override
  String get questionnaireOnboarding2 =>
      'This test aims to assess your English speaking proficiency at various stages';

  @override
  String get questionnaireOnboarding3 =>
      'Your performance in this test will help determine the level of the program that best suits your needs';

  @override
  String get start => 'Start';

  @override
  String get point => 'Point';

  @override
  String get level => 'Level';

  @override
  String get information => 'Information';

  @override
  String get questionnaireCongrat1 =>
      'Discover the world through the wonders of English!';

  @override
  String get questionnaireCongrat1Desc =>
      'Whether you\'re just starting your journey or aiming to reach a new level of fluency, now is the perfect time to begin.';

  @override
  String get questionnaireCongrat2 =>
      'Let\'s embark on this transformative adventure together,';

  @override
  String get questionnaireCongrat2Desc =>
      'An adventure where every word learned is one step closer to exciting opportunities and limitless experiences';

  @override
  String get questionnaireCongrat3 => 'Ignite your passion for English today.';

  @override
  String get questionnaireCongrat3Desc =>
      'Are you ready to take this journey? Let\'s dive in and make your English learning dreams come true! 📚🚀';

  @override
  String get pronunciationChallenge => 'Pronunciation Challenge';

  @override
  String get conversationVideo => 'Conversation Video';

  @override
  String get listeningMastery => 'Listening Mastery';

  @override
  String get speakingArena => 'Speaking Arena';

  @override
  String get record => 'Record';

  @override
  String get stage1Speaking => 'Listen and follow the audio and scripts';

  @override
  String get stage1SpeakingDesc =>
      'Listen to the audio and read the script carefully.';

  @override
  String get stage2Speaking => 'You act\nas the Questioner';

  @override
  String get stage3Speaking => 'You act\nas the Responder';

  @override
  String get nextSection => 'Ready for the next challenge?';

  @override
  String hiUser(String userName) {
    return 'Hi, $userName 👋';
  }

  @override
  String get welcome => 'Welcome!';

  @override
  String get welcome_back => 'Welcome back! What\'s up?🥳';

  @override
  String get back => 'Back';

  @override
  String get seeAll => 'See all';

  @override
  String get selectedLanguageDesc1 => 'You have chosen';

  @override
  String get express_yourself_in_english =>
      'Ready to chat in English? Let\'s go! 💬';

  @override
  String get join_the_community => 'Join community';

  @override
  String get instructions => 'Instructions';

  @override
  String get listen_and_imitate => 'Listen and Imitate';

  @override
  String get listen_and_imitate_desc =>
      'Listen to the pronunciation recording, focus on the sounds and stress, then imitate it out loud.';

  @override
  String get record_and_analyze => 'Record and Improve';

  @override
  String get record_and_analyze_desc =>
      'Record your pronunciation, get AI analysis, compare it with the model, and adjust until perfect.';

  @override
  String get compare_and_adjust => 'Compare and Adjust';

  @override
  String get compare_and_adjust_desc =>
      'Compare your pronunciation with the recorded model and make necessary adjustments based on the feedback.';

  @override
  String get practice_and_perfect => 'Practice and Perfect';

  @override
  String get practice_and_perfect_desc =>
      'Keep practicing and refining your pronunciation until you feel confident in accurately pronouncing each word and expression.';

  @override
  String get watch_and_analyze => 'Watch and Analyze';

  @override
  String get watch_and_analyze_desc1 =>
      'Watch the video with subtitles on, repeat important parts, and focus on vocabulary, grammar, and conversation themes.';

  @override
  String get watch_and_analyze_desc2 =>
      'Rewatch segments, pause to focus on specific parts, and break the conversation into smaller sections.';

  @override
  String get focus_on_vocabulary_and_grammar =>
      'Focus on vocabulary and grammar';

  @override
  String get focus_on_vocabulary_and_grammar_desc1 =>
      'Use captions to reinforce understanding, vocabulary, and grammatical structures.';

  @override
  String get focus_on_vocabulary_and_grammar_desc2 =>
      'Note unfamiliar words or phrases and their meanings.';

  @override
  String get pronunciation_practice => 'Pronunciation Practice';

  @override
  String get pronunciation_practice_desc1 =>
      'Listen and mimic the pronunciation and intonation of the speaker.';

  @override
  String get pronunciation_practice_desc2 =>
      'Pay attention to stress patterns, rhythm, and word connections, using the text as a visual aid.';

  @override
  String get reflect_and_review => 'Practice and Review';

  @override
  String get reflect_and_review_desc1 =>
      'Imitate the pronunciation, pay attention to the intonation patterns, and review the notes to improve areas that need enhancement.';

  @override
  String get reflect_and_review_desc2 =>
      'Review your notes and identify areas that need improvement, considering your language learning goals and interests.';

  @override
  String get listen_actively => 'Listen and Take Notes';

  @override
  String get listen_actively_desc =>
      'Use headphones/speakers, listen actively, and take notes to understand the recording.';

  @override
  String get repeat_and_review => 'Repeat and Review';

  @override
  String get repeat_and_review_desc =>
      'Listen to the recording several times, pause and replay as needed.';

  @override
  String get answer_the_questions => 'Answer the Questions';

  @override
  String get answer_the_questions_desc =>
      'Read the questions carefully and answer them based on the information provided in the conversation.';

  @override
  String get submit_and_review => 'Answer and Review';

  @override
  String get submit_and_review_desc =>
      'Choose the available answers, submit for evaluation, and review the feedback to improve your listening skills.';

  @override
  String get listen_and_follow => 'Listen, Follow, and Record';

  @override
  String get listen_and_follow_desc =>
      'Play the audio recording, follow the script to mimic the pronunciation and intonation, then record your voice.';

  @override
  String get repeat_the_practice => 'Compare and Improve';

  @override
  String get repeat_the_practice_desc =>
      'Compare your recording with the model audio, review the strengths and weaknesses, and keep practicing to improve your speaking skills.';

  @override
  String get record_and_compare => 'Record and Compare';

  @override
  String get record_and_compare_desc =>
      'Record yourself while practicing and compare your recording with the audio model to identify differences.';

  @override
  String get evaluate_yourself => 'Self Evaluation';

  @override
  String get evaluate_yourself_desc =>
      'Review your recording, reflect on your pronunciation, fluency, intonation, and pace to identify strengths and areas for improvement.';

  @override
  String get continuous_improvement => 'Continuous Improvement';

  @override
  String get continuous_improvement_desc =>
      'Practice and evaluate your performance consistently to improve your speaking skills.';

  @override
  String get profile_settings => 'Profile & Settings';

  @override
  String get edit_profile => 'Edit Profile';

  @override
  String get settings => 'Settings';

  @override
  String get language => 'Language';

  @override
  String get sound => 'Sound';

  @override
  String get dark_theme => 'Dark Theme';

  @override
  String get membership => 'Membership';

  @override
  String get transaction_history => 'Transaction History';

  @override
  String get logout => 'Logout';

  @override
  String get notification_settings => 'Notification Settings';

  @override
  String get notification_preferences => 'Notification Preferences';

  @override
  String get general_notification => 'General Notification';

  @override
  String get general_notification_desc =>
      'Receive essential updates and information regarding your account and our services.';

  @override
  String get promotion => 'Promotion';

  @override
  String get promotion_desc =>
      'Get notified about special offers, discounts, and exclusive deals to help you save.';

  @override
  String get announcement => 'Announcement';

  @override
  String get announcement_desc =>
      'Stay informed about new features, important updates, and news about our services.';

  @override
  String get study_reminder => 'Study Reminder';

  @override
  String get study_reminder_desc =>
      'Receive timely reminders for your scheduled study sessions to help you stay on track with your learning goals.';

  @override
  String get notification_info =>
      'You can change these settings anytime. Some notifications may still be delivered for important account or security updates.';

  @override
  String get do_you_understand => 'Do you understand?';

  @override
  String get learning_progress => 'Learning Progress';

  @override
  String get score_acquisition => 'Score Acquisition';

  @override
  String get excellent => 'Excellent!';

  @override
  String get very_good => 'Very good!👍';

  @override
  String get good => 'Good!';

  @override
  String get be_better => 'Can be better!💪';

  @override
  String get fair => 'Fair enough!🙂';

  @override
  String get yes => 'Yes';

  @override
  String get no => 'No';

  @override
  String get how_to_answer => 'Instruction';

  @override
  String get cv_instruction_decs1 => 'Select a video from the list.';

  @override
  String get cv_instruction_decs2 => 'The video will play automatically.';

  @override
  String get cv_instruction_decs3 => 'Turn on subtitles if needed.';

  @override
  String get cv_instruction_decs4 => 'You can pause and adjust playback time.';

  @override
  String get cv_instruction_decs5 => 'Enlarge the video to full screen.';

  @override
  String get cv_instruction_decs6 => 'to exit full-ratio video view.';

  @override
  String get cv_result =>
      'Yay! You finally completed this video conversation section! 🎉';

  @override
  String get click_the_button => 'Click the button';

  @override
  String get pc_instruction_decs1 => 'to listen to the recording.';

  @override
  String get pc_instruction_decs2 => 'to send recorded voice.';

  @override
  String get pc_instruction_decs3a => 'Button color changes';

  @override
  String get pc_instruction_decs3b =>
      'to record your voice, then click back to send.';

  @override
  String get pc_instruction_decs4a => 'Not holding the button';

  @override
  String get pc_instruction_decs4b =>
      'in order to process the analysis of your voice recording.';

  @override
  String get pc_instruction_decs5a => 'Don\'t forget to click the button';

  @override
  String get pc_instruction_decs5b =>
      'in order to complete a series of challenges.';

  @override
  String get pc_instruction_decs6a => 'You can also click the button';

  @override
  String get pc_instruction_decs6b =>
      'if you are still hesitant in answering the challenge.';

  @override
  String get lm_instruction_decs1 => 'to play audio.';

  @override
  String get lm_instruction_decs2 => 'to stop the audio.';

  @override
  String get lm_instruction_decs3 =>
      'You can also replay the part of the audio you want to repeat.';

  @override
  String get lm_instruction_decs4a => 'Don\'t forget to click the button';

  @override
  String get lm_instruction_decs4b =>
      'in order to complete a series of challenges.';

  @override
  String get lm_instruction_decs5a => 'Also monitor your matrix';

  @override
  String get lm_instruction_decs5b => 'while working on this challenge.';

  @override
  String get record_your_voice => 'Rekam suara anda';

  @override
  String get stage => 'Stage';

  @override
  String get is_logout_desc => 'Do you want to exit this application?';

  @override
  String get repeat => 'Repeat';

  @override
  String get evaluation_results => 'Evaluation Results';

  @override
  String get score_details => 'Score Details';

  @override
  String get impressive_work => 'Impressive work!🌟';

  @override
  String get bravo => 'Bravo!👏';

  @override
  String get getting_closer => 'Getting closer!🔜';

  @override
  String get tackling_a_tough_one => 'Tackling a tough one!💪';

  @override
  String get interesting_attempt => 'Interesting attempt!😄';

  @override
  String get not_quite_there_yet => 'Not quite there yet!🤔';

  @override
  String get keep_practicing => 'Keep practicing!🔄';

  @override
  String get great_job => 'Great job!';

  @override
  String get good_effort => 'Good effort!';

  @override
  String get needs_improvement => 'Needs improvement!';

  @override
  String get accuracy => 'Accuracy';

  @override
  String get your_score => 'Your score';

  @override
  String get vocabulary => 'Vocabulary';

  @override
  String get part => 'Part';

  @override
  String get exercise => 'Exercise';

  @override
  String get your_answer_is_correct => 'Your answer is correct! 🤩🤗';

  @override
  String get your_answer_is_wrong => 'Your answer is wrong! 😫😭';

  @override
  String get correct => 'Correct';

  @override
  String get wrong => 'Incorrect';

  @override
  String get continueYourLessons => 'Keep learning, crush your goals!📚';

  @override
  String get learning_material =>
      'Finish it, can\'t wait to see the result! 🤩';

  @override
  String get explore_your_potential => 'Explore your potential! 🌍📱📚';

  @override
  String get unlock_opportunities =>
      'Ready to unlock a world of opportunities?';

  @override
  String get start_journey => 'It\'s time to embark on a learning journey';

  @override
  String get get_started => 'Get Started';

  @override
  String get prosody => 'Intonation & Rythm';

  @override
  String get completeness => 'Completeness';

  @override
  String get unit => 'Unit';

  @override
  String get chapter_list => 'Chapter List';

  @override
  String get more => 'More';

  @override
  String get listening_exercise => 'Listening Exercise';

  @override
  String get skills_list => 'Skills List';

  @override
  String get explore_the_courses => 'Explore the courses';

  @override
  String get level_pitch_sentences =>
      'From basic communication to full mastery, these levels guide you on your way to English fluency.';

  @override
  String get remember_7_items => 'Remember 7 items';

  @override
  String get tap_items_you_saw => 'Tap the items you saw';

  @override
  String get loading => 'Loading';

  @override
  String get ready => 'Siap?';

  @override
  String get go => 'Mulai!';

  @override
  String get memory_flash_result_desc =>
      'You’ve done it! Let’s check your result 📋';

  @override
  String get replay => 'Replay';

  @override
  String get topic_complete => 'Topic Complete 📑';

  @override
  String get level_complete => 'Level Complete🏅 ';

  @override
  String get congratulations => 'Congratulations! 🎉';

  @override
  String get score => 'Score';

  @override
  String get select_category => 'Select Category';

  @override
  String get select_topic => 'Select Topic';

  @override
  String get please_wait => 'Please wait';

  @override
  String get complete_all_challenges => 'Complete All Challenges';

  @override
  String get complete_all_challenges_desc =>
      'Please complete all challenges to view your final results.';

  @override
  String get gameplay => 'Gameplay 🕹️';

  @override
  String get how_to_play_memory_flash =>
      'See 7 words for 3 seconds each, then pick them from a list to 10 ( 7 correct + 3 distractors).';

  @override
  String get cert_notif_a1 => '👏 You’ve Completed the A1 Level! ';

  @override
  String get cert_notif_a2 => '🌟 Your English Skills Are Growing Fast!';

  @override
  String get cert_notif_b1 =>
      '💪 Reaching B1 Means You Can Communicate with Ease!';

  @override
  String get cert_notif_b2 => '🚀 Completing B2 Shows Your Dedication!';

  @override
  String get cert_notif_c1 => '🔥 You’ve Reached Advanced Mastery!';

  @override
  String get cert_notif_c2 => '👑 You’ve Reached the Top!';

  @override
  String get level_not_completed => 'Level Not Completed';

  @override
  String get level_not_completed_desc =>
      'Please complete all chapters and sections in this level to unlock your certificate.';

  @override
  String get back_to_lessons => 'Back to Lessons';

  @override
  String get cert_download_a1 =>
      '🎓 Download your A1 Certificate and show off your achievement!';

  @override
  String get cert_download_a2 =>
      '🎓 Grab your A2 Certificate and share your progress with the world!';

  @override
  String get cert_download_b1 =>
      '🎓 Download your B1 Certificate and inspire others to keep going!';

  @override
  String get cert_download_b2 =>
      '🎓 Show off your success — your B2 Certificate is ready!';

  @override
  String get cert_download_c1 =>
      '🎓 Download your C1 Certificate and mark your major achievement!';

  @override
  String get cert_download_c2 =>
      '🎓 Claim your C2 Mastery Certificate — you\'ve earned it!';

  @override
  String get qualify_title_a1 => '🚀 Master the basics!';

  @override
  String get qualify_desc_a1 =>
      '50+ points in all skills unlocks your A1 certificate.';

  @override
  String get qualify_title_a2 => '🎯 Boost your vocabulary & expressions!';

  @override
  String get qualify_desc_a2 =>
      'Reach 50+ in listening and speaking for your A2 certificate.';

  @override
  String get qualify_title_b1 => '🔥 Speak with confidence!';

  @override
  String get qualify_desc_b1 =>
      'Get 50+ points in all skills to earn your B1 certificate.';

  @override
  String get qualify_title_b2 => '🏆 Own the conversation!';

  @override
  String get qualify_desc_b2 =>
      'Achieve 50+ points in all skills for your B2 certificate.';

  @override
  String get qualify_title_c1 => '⚡ Show your mastery!';

  @override
  String get qualify_desc_c1 =>
      'Score 50+ in all skills to unlock your C1 certificate.';

  @override
  String get qualify_title_c2 => '🌟 Near native fluency!';

  @override
  String get qualify_desc_c2 =>
      'Earn your C2 certificate with 50+ points in all skills.';

  @override
  String get process_loading =>
      'Please wait a moment,\nyour work is still\nbeing processed 😊';

  @override
  String get certificate_list => 'Certificate List';

  @override
  String get recording_error =>
      'Please try again, we are unable to hear your voice.';

  @override
  String get chapter => 'Chapter';

  @override
  String get chapters_range => 'Chapters';

  @override
  String get needs_practice => 'Needs Practice!';

  @override
  String get needs_practice_desc => 'Speech is unclear and uneven.';

  @override
  String get sound_match => '🔊 Sound Match';

  @override
  String get smooth_talk => '💬 Smooth Talk';

  @override
  String get natural_flow => '🎶 Natural Flow';

  @override
  String get excellent_desc => 'Smooth, accurate, and natural.';

  @override
  String get good_desc => 'Clear, but still room to improve.';

  @override
  String get share => 'Share';

  @override
  String get certificates => 'Certificates';

  @override
  String get no_certificates_found => 'No certificates found.';

  @override
  String get certificate => 'Certificate';

  @override
  String get certificate_detail => 'Certificate Detail';

  @override
  String get no_certificate_data_available => 'No certificate data available';

  @override
  String get no_certificate_selected => 'No certificate selected';

  @override
  String get certificate_pages => 'Certificate Pages';

  @override
  String get page_1 => 'Page 1';

  @override
  String get page_2 => 'Page 2';

  @override
  String get view_details => 'View Details';

  @override
  String get download => 'Download';

  @override
  String get share_all_pages => 'Share All Pages';

  @override
  String issued_on(String date) {
    return 'Issued on $date';
  }

  @override
  String level_certificate(String level) {
    return '$level Certificate';
  }

  @override
  String check_out_my_certificate(String level) {
    return 'Check out my $level certificate!';
  }

  @override
  String check_out_my_certificates(String level) {
    return 'Check out my $level certificates!';
  }

  @override
  String get my_certificate => 'My Certificate';

  @override
  String get my_certificates => 'My Certificates';

  @override
  String get certificate_shared_successfully =>
      'Certificate shared successfully';

  @override
  String get certificates_shared_successfully =>
      'Certificates shared successfully';

  @override
  String get sharing_cancelled_or_failed => 'Sharing was cancelled or failed';

  @override
  String share_failed(String error) {
    return 'Share failed: $error';
  }

  @override
  String download_failed(String error) {
    return 'Download failed: $error';
  }

  @override
  String get unable_to_access_downloads_folder =>
      'Unable to access Downloads folder';

  @override
  String get failed_to_convert_certificate_to_png =>
      'Failed to convert certificate to PNG';

  @override
  String get certificate_saved_to_downloads =>
      'Certificate saved to Downloads folder';

  @override
  String get certificate_saved_to_files_app => 'Certificate saved to Files app';

  @override
  String get failed_to_load_certificates =>
      'Failed to load certificates. Please try again.';

  @override
  String get urls_and_filenames_mismatch =>
      'URLs and file names count mismatch';

  @override
  String get check_out_my_certificate_generic => 'Check out my certificate!';

  @override
  String get check_out_my_certificates_generic => 'Check out my certificates!';

  @override
  String get bookmarks => 'Bookmarks';

  @override
  String get lm_100_desc => 'You\'re ready\nfor the next level! ⏫';

  @override
  String get lm_50_desc =>
      'Come on, step on the gas\nand it\'ll go smoother! 🚀';

  @override
  String get lm_0_desc => 'Keep practicing,\nyou\'ll get better! 💪';

  @override
  String get correct_emot => 'Correct ✅';

  @override
  String get retake_certificate => 'Retake Certificate';

  @override
  String get retake_certificate_warning_title => 'Retake Certificate Warning';

  @override
  String get retake_certificate_warning_message =>
      'Retaking this certificate will reset all your progress for the current level. Only the last 3 certificates will be kept in your history.';

  @override
  String get retake_certificate_success =>
      'Your progress has been reset successfully. You can now attempt to retake the certificate.';

  @override
  String get retake_certificate_error =>
      'Failed to reset your progress. Please try again.';

  @override
  String get cancel => 'Cancel';

  @override
  String get no_certificates_yet => 'You don\'t have any certificates yet';

  @override
  String get every_expert_was_beginner =>
      'Every expert was once a beginner. Start your English learning journey today and earn your first certificate!';

  @override
  String get complete_lessons_practice =>
      'Complete lessons, practice regularly, and watch your progress grow!';

  @override
  String get start_learning_now => 'Start Learning Now';

  @override
  String get incorrect_emot => 'Incorrect ❌';

  @override
  String get install => 'Install';

  @override
  String get installUpdateTitle => 'Install Update';

  @override
  String get installUpdateDescription =>
      'The app will restart to apply the update. Any unsaved changes will be lost.';

  @override
  String get updateReadyToInstall => 'Update Ready to Install';

  @override
  String get tapToRestartAndApplyUpdate =>
      'Tap to restart and apply the update';

  @override
  String get search_bookmarks => 'Search bookmarks...';

  @override
  String get no_bookmarks => 'No bookmarks yet';

  @override
  String get explore_content => 'Explore Content';

  @override
  String get filter_by_type => 'Filter by section';
}
