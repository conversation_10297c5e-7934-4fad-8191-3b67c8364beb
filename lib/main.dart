import 'dart:async';

import 'package:firebase_app_check/firebase_app_check.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:selfeng/firebase_options.dart';
import 'package:selfeng/main/app.dart';
import 'package:selfeng/main/app_env.dart';
import 'package:selfeng/services/notification_service/domain/providers/notification_service_provider.dart';
import 'package:selfeng/services/in_app_update_service/domain/providers/in_app_update_service_provider.dart';
import 'package:selfeng/services/crashlytics_service/domain/providers/crashlytics_service_provider.dart';
// import 'package:selfeng/main/observers.dart';

void main() => mainCommon(AppEnvironment.PROD);

/// Initialize minimal notification service during app startup
/// This only sets up essential, non-blocking components
void _initializeNotificationServiceAsync() {
  // Run minimal initialization in background without awaiting to avoid blocking
  Future.microtask(() async {
    final container = ProviderContainer();
    try {
      final notificationService = container.read(notificationServiceProvider);
      final result = await notificationService.initializeMinimal();

      result.fold(
        (error) {
          // Don't throw here to prevent app crash - notifications are not critical for app startup
        },
        (_) {
          // Schedule full initialization for later
          _scheduleFullNotificationInitialization();
        },
      );
    } catch (e) {
      // Handle exception silently
    } finally {
      container.dispose();
    }
  });
}

/// Schedule full notification initialization after a delay
/// This allows the app to fully load before doing blocking operations
void _scheduleFullNotificationInitialization() {
  Future.delayed(const Duration(seconds: 2), () async {
    final container = ProviderContainer();
    try {
      final notificationService = container.read(notificationServiceProvider);
      final result = await notificationService.completeInitialization();

      result.fold(
        (error) {
          // Handle error silently
        },
        (_) {
          // Initialization completed
        },
      );
    } catch (e) {
      // Handle exception silently
    } finally {
      container.dispose();
    }
  });
}

/// Initialize in-app update service during app startup
/// This checks for available updates in the background
void _initializeInAppUpdateServiceAsync() {
  // Run initialization in background without awaiting to avoid blocking
  Future.microtask(() async {
    final container = ProviderContainer();
    try {
      final updateService = container.read(inAppUpdateServiceProvider);
      final result = await updateService.initialize();

      result.fold(
        (error) {
          // Don't throw here to prevent app crash - updates are not critical for app startup
        },
        (_) {
          // Schedule update check for later
          _scheduleUpdateCheck();
        },
      );
    } catch (e) {
      // Handle exception silently
    } finally {
      container.dispose();
    }
  });
}

/// Schedule update check after a delay
/// This allows the app to fully load before checking for updates
void _scheduleUpdateCheck() {
  Future.delayed(const Duration(seconds: 5), () async {
    final container = ProviderContainer();
    try {
      final updateService = container.read(inAppUpdateServiceProvider);
      final result = await updateService.checkForUpdate();

      result.fold(
        (error) {
          // Handle error silently
        },
        (updateInfo) {
          // Update check completed
        },
      );
    } catch (e) {
      // Handle exception silently
    } finally {
      container.dispose();
    }
  });
}

Future<void> mainCommon(AppEnvironment environment) async {
  runZonedGuarded<Future<void>>(
    () async {
      WidgetsFlutterBinding.ensureInitialized();
      SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle.dark);
      EnvInfo.initialize(environment);
      await dotenv.load(fileName: EnvInfo.envName);
      await Firebase.initializeApp(
        options: DefaultFirebaseOptions.currentPlatform,
      );
      await FirebaseAppCheck.instance.activate(
        androidProvider: kDebugMode
            ? AndroidProvider.debug
            : AndroidProvider.playIntegrity,
        appleProvider: kDebugMode
            ? AppleProvider.debug
            : AppleProvider.appAttest,
      );

      // Initialize Crashlytics service after Firebase initialization
      final container = ProviderContainer();
      final crashlyticsService = container.read(crashlyticsServiceProvider);
      await crashlyticsService.initialize();

      // Set app version and build info
      await crashlyticsService.setCustomKeys({
        'app_version': '1.0.0+41',
        'environment': EnvInfo.envName,
        'build_mode': kDebugMode ? 'debug' : 'release',
      });

      runApp(
        const ProviderScope(
          // observers: [if (kDebugMode) Observers()],
          child: MyApp(),
        ),
      );

      // Initialize notification service asynchronously after app starts
      // This prevents blocking the splash screen
      _initializeNotificationServiceAsync();
      _initializeInAppUpdateServiceAsync();
    },
    (error, stack) {
      try {
        // Use a separate container for error reporting to avoid circular dependencies
        final errorContainer = ProviderContainer();
        final crashlyticsService = errorContainer.read(
          crashlyticsServiceProvider,
        );
        crashlyticsService
            .recordError(error, stack, fatal: true)
            .catchError((e) => debugPrint('Failed to report zone error: $e'));
        errorContainer.dispose();
      } catch (e) {
        // If error reporting fails, log to console to prevent infinite loops
        debugPrint('Error in error handler: $e');
        debugPrint('Original error: $error');
      }
    },
  );
}
