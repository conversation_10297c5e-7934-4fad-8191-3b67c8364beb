# Firebase Crashlytics Service

A comprehensive Firebase Crashlytics integration service for Flutter applications with enhanced error handling, automatic reporting, and contextual information.

## Features

- 🔧 **Centralized Error Handling**: Single service for all crash reporting needs
- 📊 **Rich Context**: Automatic inclusion of user info, custom keys, and metadata
- 🎯 **Typed Error Categories**: Specific error types (Network, Auth, UI, Business Logic)
- 🛡️ **Safe Error Reporting**: Never breaks your app even if reporting fails
- 🔄 **Automatic Recovery**: Graceful fallbacks when operations fail
- 🧪 **Easy Testing**: Mockable interfaces for unit testing
- 📝 **Structured Logging**: Consistent error formatting and context

## Quick Start

### 1. Basic Setup

The service is automatically initialized in `main.dart` and available through Riverpod providers:

```dart
// Access the service in any provider
final crashlyticsService = ref.read(crashlyticsServiceProvider);

// Set user context when user logs in
await crashlyticsService.setUserId(user.id);
await crashlyticsService.setUserEmail(user.email);
await crashlyticsService.setCustomKeys({
  'user_type': user.isPremium ? 'premium' : 'free',
  'app_version': '1.0.0',
});
```

### 2. Using the Error Handler

```dart
import 'package:selfeng/services/crashlytics_service/utils/error_handler.dart';

class MyController {
  final ErrorHandler _errorHandler;

  Future<void> fetchData() async {
    await _errorHandler.handleAsync(
      () => apiService.getData(),
      context: 'Data Fetch',
      metadata: {
        'endpoint': '/api/data',
        'user_id': currentUser.id,
      },
      fatal: false,
      rethrow: false,
    );
  }
}
```

### 3. Using the Mixin (Recommended)

```dart
import 'package:selfeng/services/crashlytics_service/mixins/error_handling_mixin.dart';

@riverpod
class MyController extends _$MyController with ErrorHandlingMixin {
  @override
  String build() => 'initial';

  Future<void> authenticateUser() async {
    try {
      await authService.login();
    } catch (error, stackTrace) {
      await reportAuthError(
        error,
        stackTrace,
        authMethod: 'google_signin',
        userId: user.email,
      );
      rethrow;
    }
  }

  Future<void> networkOperation() async {
    await handleAsyncError(
      () => networkService.request(),
      context: 'Network Request',
      metadata: {'endpoint': '/api/endpoint'},
      fatal: false,
    );
  }
}
```

## Error Categories

### Network Errors
```dart
await reportNetworkError(
  error,
  stackTrace,
  endpoint: '/api/users',
  method: 'POST',
  statusCode: 500,
  requestData: {'userId': '123'},
);
```

### Authentication Errors
```dart
await reportAuthError(
  error,
  stackTrace,
  authMethod: 'google_signin',
  userId: user.id,
);
```

### UI/Navigation Errors
```dart
await reportUIError(
  error,
  stackTrace,
  screenName: 'HomePage',
  widget: 'UserProfileWidget',
  uiState: {'isLoading': false, 'hasData': true},
);
```

### Business Logic Errors
```dart
await reportBusinessLogicError(
  error,
  stackTrace,
  feature: 'payment_processing',
  operation: 'charge_credit_card',
  businessContext: {
    'amount': 99.99,
    'currency': 'USD',
    'payment_method': 'visa',
  },
);
```

## Advanced Features

### User Context Management

```dart
// When user logs in
await crashlyticsService.setUserId(user.id);
await crashlyticsService.setUserEmail(user.email);
await crashlyticsService.setCustomKeys({
  'subscription_type': user.subscriptionType,
  'user_level': user.level,
  'last_purchase': user.lastPurchase?.toIso8601String(),
});

// When user logs out
await crashlyticsService.clearUserData();
```

### Custom Keys for Context

```dart
// Set global context
await crashlyticsService.setCustomKeys({
  'app_version': packageInfo.version,
  'build_number': packageInfo.buildNumber,
  'environment': kDebugMode ? 'debug' : 'production',
  'platform': Platform.operatingSystem,
});

// Set feature-specific context
await crashlyticsService.setCustomKey('current_feature', 'main_lesson');
await crashlyticsService.setCustomKey('lesson_id', lessonId);
```

### Privacy Controls

```dart
// Check current collection status
final isEnabled = await crashlyticsService.isCrashlyticsCollectionEnabled();

// Enable/disable based on user preference
await crashlyticsService.setCrashlyticsCollectionEnabled(userConsent);

// Send unsent reports (when user opts in)
if (await crashlyticsService.checkForUnsentReports()) {
  await crashlyticsService.sendUnsentReports();
}
```

### Manual Logging

```dart
// Log breadcrumbs
crashlyticsService.log('User started lesson: $lessonId');
crashlyticsService.log('Payment process initiated');
crashlyticsService.log('User completed onboarding');

// These logs will be included in crash reports for context
```

## Testing

### Mocking the Service

```dart
// Create a mock implementation for testing
class MockCrashlyticsRepository implements CrashlyticsRepository {
  final List<String> loggedMessages = [];
  final List<Map<String, dynamic>> reportedErrors = [];

  @override
  void log(String message) {
    loggedMessages.add(message);
  }

  @override
  Future<void> recordError(
    dynamic exception,
    StackTrace? stackTrace, {
    String? reason,
    Map<String, dynamic>? context,
    bool fatal = false,
  }) async {
    reportedErrors.add({
      'exception': exception,
      'reason': reason,
      'context': context,
      'fatal': fatal,
    });
  }

  // ... implement other methods
}

// Use in tests
test('should report authentication errors', () async {
  final mockCrashlytics = MockCrashlyticsRepository();
  final errorHandler = ErrorHandler(mockCrashlytics);

  try {
    throw Exception('Auth failed');
  } catch (error, stackTrace) {
    await errorHandler.reportAuthError(error, stackTrace);
  }

  expect(mockCrashlytics.reportedErrors, hasLength(1));
  expect(mockCrashlytics.reportedErrors.first['context']['category'], 'authentication');
});
```

## Best Practices

### 1. Set Context Early
```dart
// In main.dart after Firebase initialization
await crashlyticsService.setCustomKeys({
  'app_version': packageInfo.version,
  'environment': EnvInfo.envName,
  'build_mode': kDebugMode ? 'debug' : 'release',
});
```

### 2. Use Appropriate Fatal Flags
```dart
// Fatal for critical errors that break core functionality
await reportError(error, stackTrace, fatal: true);

// Non-fatal for recoverable errors
await reportError(error, stackTrace, fatal: false);
```

### 3. Provide Rich Context
```dart
await reportNetworkError(
  error,
  stackTrace,
  endpoint: request.url,
  method: request.method,
  statusCode: response?.statusCode,
  requestData: {
    'user_id': currentUser?.id,
    'request_id': request.id,
    'retry_count': retryAttempt,
  },
);
```

### 4. Handle Sensitive Data
```dart
// Don't include sensitive information in crash reports
await reportError(
  error,
  stackTrace,
  context: {
    'user_id': user.id, // OK - identifier
    'email_domain': user.email.split('@').last, // OK - anonymized
    // 'password': user.password, // NEVER include
    // 'credit_card': user.ccNumber, // NEVER include
  },
);
```

### 5. Use Categories for Filtering
```dart
// This helps with Firebase console filtering
await crashlyticsService.setCustomKey('error_category', 'network');
await crashlyticsService.setCustomKey('feature', 'authentication');
await crashlyticsService.setCustomKey('severity', 'high');
```

## Integration Examples

See the [examples directory](./examples/) for complete implementation examples including:
- Controller integration with error handling mixin
- Widget-level error boundary
- Network service integration  
- Authentication flow error handling
- Business logic error reporting

## Architecture

```
crashlytics_service/
├── domain/
│   ├── providers/          # Riverpod providers
│   └── repositories/       # Abstract interfaces
├── data/
│   └── repositories/       # Firebase implementation
├── utils/
│   └── error_handler.dart  # Core error handling logic
├── mixins/
│   └── error_handling_mixin.dart  # Convenient mixin for providers
└── examples/               # Usage examples and documentation
```

The service follows clean architecture principles with clear separation between domain, data, and utility layers. All implementations are easily testable and mockable.
