import 'dart:async';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter/foundation.dart';

import '../../domain/repositories/crashlytics_repository.dart';

/// Firebase implementation of CrashlyticsRepository
class FirebaseCrashlyticsRepository implements CrashlyticsRepository {
  final FirebaseCrashlytics _crashlytics;

  FirebaseCrashlyticsRepository({FirebaseCrashlytics? crashlytics})
    : _crashlytics = crashlytics ?? FirebaseCrashlytics.instance;

  @override
  Future<void> initialize() async {
    try {
      // In debug mode, you might want to disable crashlytics or enable it based on your needs
      if (kDebugMode) {
        // Optionally disable crashlytics in debug mode
        // await _crashlytics.setCrashlyticsCollectionEnabled(false);
      }

      // Set up automatic error reporting
      FlutterError.onError = (errorDetails) {
        recordFlutterError(errorDetails);
      };

      // Catch errors outside of Flutter context
      PlatformDispatcher.instance.onError = (error, stack) {
        recordError(error, stack, fatal: true);
        return true;
      };

      log('Crashlytics service initialized');
    } catch (e) {
      // Don't let crashlytics initialization errors break the app
      debugPrint('Failed to initialize Crashlytics: $e');
    }
  }

  @override
  Future<void> setUserId(String userId) async {
    try {
      await _crashlytics.setUserIdentifier(userId);
      log('User ID set: $userId');
    } catch (e) {
      debugPrint('Failed to set user ID: $e');
    }
  }

  @override
  Future<void> setUserEmail(String email) async {
    try {
      await setCustomKey('user_email', email);
    } catch (e) {
      debugPrint('Failed to set user email: $e');
    }
  }

  @override
  Future<void> setCustomKey(String key, dynamic value) async {
    try {
      await _crashlytics.setCustomKey(key, value);
    } catch (e) {
      debugPrint('Failed to set custom key $key: $e');
    }
  }

  @override
  Future<void> setCustomKeys(Map<String, dynamic> keys) async {
    try {
      for (final entry in keys.entries) {
        await _crashlytics.setCustomKey(entry.key, entry.value);
      }
    } catch (e) {
      debugPrint('Failed to set custom keys: $e');
    }
  }

  @override
  void log(String message) {
    try {
      _crashlytics.log(message);
    } catch (e) {
      debugPrint('Failed to log message: $e');
    }
  }

  @override
  Future<void> recordError(
    dynamic exception,
    StackTrace? stackTrace, {
    String? reason,
    Map<String, dynamic>? context,
    bool fatal = false,
  }) async {
    try {
      // Add logging for diagnostics
      debugPrint('Crashlytics recordError called:');
      debugPrint('  exception: $exception');
      debugPrint('  stackTrace: ${stackTrace?.toString() ?? "NULL"}');
      debugPrint('  reason: $reason');
      debugPrint('  fatal: $fatal');
      debugPrint('  context: $context');

      // Add context as custom keys if provided
      if (context != null) {
        await setCustomKeys(context);
      }

      // Handle null stackTrace to prevent SDK crash
      final effectiveStackTrace = stackTrace ?? StackTrace.current;
      if (stackTrace == null) {
        debugPrint(
          'Null stackTrace detected - using StackTrace.current as fallback',
        );
      }

      // Log the reason if provided
      if (reason != null) {
        log('Error reason: $reason');
      }

      await _crashlytics.recordError(
        exception,
        effectiveStackTrace,
        fatal: fatal,
        information: reason != null ? [reason] : [],
      );
    } catch (e) {
      debugPrint('Failed to record error: $e');
    }
  }

  @override
  Future<void> recordFlutterError(FlutterErrorDetails errorDetails) async {
    try {
      await _crashlytics.recordFlutterFatalError(errorDetails);
    } catch (e) {
      debugPrint('Failed to record Flutter error: $e');
    }
  }

  @override
  Future<void> setCrashlyticsCollectionEnabled(bool enabled) async {
    try {
      await _crashlytics.setCrashlyticsCollectionEnabled(enabled);
      log('Crashlytics collection ${enabled ? 'enabled' : 'disabled'}');
    } catch (e) {
      debugPrint('Failed to set crashlytics collection enabled: $e');
    }
  }

  @override
  Future<bool> isCrashlyticsCollectionEnabled() async {
    try {
      return _crashlytics.isCrashlyticsCollectionEnabled;
    } catch (e) {
      debugPrint('Failed to check crashlytics collection status: $e');
      return false;
    }
  }

  @override
  Future<void> clearUserData() async {
    try {
      await _crashlytics.setUserIdentifier('');
      // Clear common user-related custom keys
      await setCustomKeys({
        'user_email': '',
        'user_role': '',
        'user_subscription': '',
      });
      log('User data cleared from crashlytics');
    } catch (e) {
      debugPrint('Failed to clear user data: $e');
    }
  }

  @override
  Future<void> sendUnsentReports() async {
    try {
      await _crashlytics.sendUnsentReports();
      log('Unsent reports sent');
    } catch (e) {
      debugPrint('Failed to send unsent reports: $e');
    }
  }

  @override
  Future<bool> checkForUnsentReports() async {
    try {
      return await _crashlytics.checkForUnsentReports();
    } catch (e) {
      debugPrint('Failed to check for unsent reports: $e');
      return false;
    }
  }
}
