import 'dart:async';
import 'package:flutter/foundation.dart';

/// Repository interface for crash reporting and analytics
abstract class CrashlyticsRepository {
  /// Initialize the crashlytics service
  Future<void> initialize();

  /// Set user identifier for crash reports
  Future<void> setUserId(String userId);

  /// Set user email for crash reports
  Future<void> setUserEmail(String email);

  /// Set custom key-value pairs for crash context
  Future<void> setCustomKey(String key, dynamic value);

  /// Set multiple custom keys at once
  Future<void> setCustomKeys(Map<String, dynamic> keys);

  /// Log a message that will be included in crash reports
  void log(String message);

  /// Record a non-fatal error with optional context
  Future<void> recordError(
    dynamic exception,
    StackTrace? stackTrace, {
    String? reason,
    Map<String, dynamic>? context,
    bool fatal = false,
  });

  /// Record a Flutter error (typically from FlutterError.onError)
  Future<void> recordFlutterError(FlutterErrorDetails errorDetails);

  /// Enable/disable crash collection (useful for privacy settings)
  Future<void> setCrashlyticsCollectionEnabled(bool enabled);

  /// Check if crashlytics collection is enabled
  Future<bool> isCrashlyticsCollectionEnabled();

  /// Clear all user data and custom keys
  Future<void> clearUserData();

  /// Send any pending crash reports
  Future<void> sendUnsentReports();

  /// Check for unsent reports
  Future<bool> checkForUnsentReports();
}
