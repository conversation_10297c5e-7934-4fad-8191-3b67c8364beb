import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../domain/providers/crashlytics_service_provider.dart';

part 'crashlytics_usage_example.g.dart';

/// Example of using the Crashlytics service in a typical controller
@riverpod
class ExampleController extends _$ExampleController {
  @override
  String build() => 'initial_state';

  /// Example: Network request with automatic error reporting
  Future<void> fetchData() async {
    try {
      await _makeNetworkRequest();
      state = 'data_fetched';
    } catch (error, stackTrace) {
      final crashlytics = ref.read(crashlyticsServiceProvider);
      await crashlytics.recordError(
        error,
        stackTrace,
        reason: 'Data Fetch Error',
        context: {
          'feature': 'example_controller',
          'operation': 'fetch_data',
          'timestamp': DateTime.now().toIso8601String(),
        },
        fatal: false,
      );
    }
  }

  /// Example: Authentication operation with specific error context
  Future<void> authenticateUser(String email) async {
    try {
      // Simulate auth operation
      await Future.delayed(const Duration(seconds: 1));

      // Set user context for future crashes
      final crashlytics = ref.read(crashlyticsServiceProvider);
      await crashlytics.setUserId(email.hashCode.toString());
      await crashlytics.setUserEmail(email);
      await crashlytics.setCustomKeys({
        'user_type': 'premium',
        'last_login': DateTime.now().toIso8601String(),
        'device_info': 'example_device',
      });

      state = 'authenticated';
    } catch (error, stackTrace) {
      final crashlytics = ref.read(crashlyticsServiceProvider);
      await crashlytics.recordError(
        error,
        stackTrace,
        reason: 'Authentication Error',
        context: {
          'category': 'authentication',
          'auth_method': 'email_password',
          'user_id': email,
        },
        fatal: false,
      );
      rethrow;
    }
  }

  /// Example: Business logic operation with custom error context
  Future<void> processPayment(double amount) async {
    try {
      await _processPaymentInternal(amount);
      state = 'payment_processed';
    } catch (error, stackTrace) {
      final crashlytics = ref.read(crashlyticsServiceProvider);
      await crashlytics.recordError(
        error,
        stackTrace,
        reason: 'Payment Processing Error',
        context: {
          'category': 'business_logic',
          'feature': 'payment_processing',
          'operation': 'charge_credit_card',
          'amount': amount,
          'currency': 'USD',
          'payment_method': 'credit_card',
        },
        fatal: false,
      );
      rethrow;
    }
  }

  /// Example: UI operation with error reporting
  void updateUI(Map<String, dynamic> uiData) {
    try {
      _updateUIInternal(uiData);
    } catch (error, stackTrace) {
      final crashlytics = ref.read(crashlyticsServiceProvider);
      crashlytics
          .recordError(
            error,
            stackTrace,
            reason: 'UI Update Error',
            context: {
              'category': 'ui',
              'screen_name': 'example_screen',
              'ui_data': uiData,
            },
            fatal: false,
          )
          .catchError((e) => debugPrint('Failed to report UI error: $e'));
    }
  }

  // Private methods simulating actual operations
  Future<void> _makeNetworkRequest() async {
    // Simulate network request
    await Future.delayed(const Duration(seconds: 2));
    // throw Exception('Network timeout'); // Uncomment to test error handling
  }

  Future<void> _processPaymentInternal(double amount) async {
    // Simulate payment processing
    await Future.delayed(const Duration(seconds: 1));
    if (amount <= 0) {
      throw ArgumentError('Invalid payment amount: $amount');
    }
  }

  void _updateUIInternal(Map<String, dynamic> uiData) {
    // Simulate UI update
    if (uiData.isEmpty) {
      throw StateError('UI data cannot be empty');
    }
    state = 'ui_updated';
  }
}

/// Example Widget showing how to use Crashlytics in UI components
class ExampleWidget extends ConsumerWidget {
  const ExampleWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final controller = ref.watch(exampleControllerProvider.notifier);
    final state = ref.watch(exampleControllerProvider);

    return Scaffold(
      appBar: AppBar(title: const Text('Crashlytics Example')),
      body: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text('State: $state'),
          const SizedBox(height: 20),

          // Example: Manual error reporting
          ElevatedButton(
            onPressed: () => _reportCustomError(ref),
            child: const Text('Report Custom Error'),
          ),

          // Example: Network operation
          ElevatedButton(
            onPressed: () => controller.fetchData(),
            child: const Text('Fetch Data'),
          ),

          // Example: Authentication
          ElevatedButton(
            onPressed: () => controller.authenticateUser('<EMAIL>'),
            child: const Text('Authenticate User'),
          ),

          // Example: Payment processing
          ElevatedButton(
            onPressed: () => controller.processPayment(99.99),
            child: const Text('Process Payment'),
          ),

          // Example: UI update
          ElevatedButton(
            onPressed: () => controller.updateUI({'key': 'value'}),
            child: const Text('Update UI'),
          ),
        ],
      ),
    );
  }

  /// Example of manual error reporting from UI
  Future<void> _reportCustomError(WidgetRef ref) async {
    try {
      // Simulate an error condition
      throw Exception('This is a test error from UI');
    } catch (error, stackTrace) {
      final crashlytics = ref.read(crashlyticsServiceProvider);
      await crashlytics.recordError(
        error,
        stackTrace,
        reason: 'User triggered test error',
        context: {
          'source': 'example_widget',
          'button': 'report_custom_error',
          'timestamp': DateTime.now().toIso8601String(),
        },
        fatal: false,
      );
    }
  }
}

/// Example of a global error boundary for the app
class AppErrorBoundary extends ConsumerWidget {
  final Widget child;

  const AppErrorBoundary({super.key, required this.child});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    ErrorWidget.builder = (FlutterErrorDetails details) {
      // Report widget errors to Crashlytics
      final crashlytics = ref.read(crashlyticsServiceProvider);
      crashlytics.recordFlutterError(details).catchError((e) {
        debugPrint('Failed to report widget error: $e');
      });

      // Return a custom error widget
      return Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.error_outline, color: Colors.red, size: 64),
              const SizedBox(height: 16),
              const Text(
                'Something went wrong',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              const Text(
                'The error has been reported and will be fixed soon.',
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () {
                  // You might want to restart the app or navigate to a safe screen
                  Navigator.of(context).pushReplacementNamed('/');
                },
                child: const Text('Go to Home'),
              ),
            ],
          ),
        ),
      );
    };

    return child;
  }
}
