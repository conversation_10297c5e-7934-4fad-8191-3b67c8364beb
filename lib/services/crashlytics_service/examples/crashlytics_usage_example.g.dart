// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'crashlytics_usage_example.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, type=warning
/// Example of using the Crashlytics service in a typical controller

@ProviderFor(ExampleController)
const exampleControllerProvider = ExampleControllerProvider._();

/// Example of using the Crashlytics service in a typical controller
final class ExampleControllerProvider
    extends $NotifierProvider<ExampleController, String> {
  /// Example of using the Crashlytics service in a typical controller
  const ExampleControllerProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'exampleControllerProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$exampleControllerHash();

  @$internal
  @override
  ExampleController create() => ExampleController();

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(String value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<String>(value),
    );
  }
}

String _$exampleControllerHash() => r'a9b609f33379f852b64ef1217c858b79ea674f76';

/// Example of using the Crashlytics service in a typical controller

abstract class _$ExampleController extends $Notifier<String> {
  String build();
  @$mustCallSuper
  @override
  void runBuild() {
    final created = build();
    final ref = this.ref as $Ref<String, String>;
    final element =
        ref.element
            as $ClassProviderElement<
              AnyNotifier<String, String>,
              String,
              Object?,
              Object?
            >;
    element.handleValue(ref, created);
  }
}
