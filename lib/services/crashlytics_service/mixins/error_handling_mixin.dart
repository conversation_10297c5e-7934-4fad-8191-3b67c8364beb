import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../domain/providers/crashlytics_service_provider.dart';
import '../utils/error_handler.dart';

/// Mixin that provides error handling capabilities to Riverpod providers
mixin ErrorHandlingMixin {
  /// Reference to the Riverpod ref (must be provided by the implementing class)
  Ref get ref;

  /// Get the error handler instance
  ErrorHandler get errorHandler =>
      ErrorHandler(ref.read(crashlyticsServiceProvider));

  /// Execute a future with automatic error handling and reporting
  Future<T?> handleAsyncError<T>(
    Future<T> Function() operation, {
    String? context,
    Map<String, dynamic>? metadata,
    T? fallbackValue,
    bool fatal = false,
    bool shouldRethrow = true,
  }) async {
    return await errorHandler.handleAsync(
      operation,
      context: context,
      metadata: metadata,
      fallbackValue: fallbackValue,
      fatal: fatal,
      shouldRethrow: shouldRethrow,
    );
  }

  /// Execute a synchronous operation with automatic error handling
  T? handleSyncError<T>(
    T Function() operation, {
    String? context,
    Map<String, dynamic>? metadata,
    T? fallbackValue,
    bool fatal = false,
    bool shouldRethrow = true,
  }) {
    return errorHandler.handleSync(
      operation,
      context: context,
      metadata: metadata,
      fallbackValue: fallbackValue,
      fatal: fatal,
      shouldRethrow: shouldRethrow,
    );
  }

  /// Report an error manually
  Future<void> reportError(
    dynamic error,
    StackTrace? stackTrace, {
    String? context,
    Map<String, dynamic>? metadata,
    bool fatal = false,
  }) async {
    await errorHandler.reportError(
      error,
      stackTrace,
      context: context,
      metadata: metadata,
      fatal: fatal,
    );
  }

  /// Report a network error with specific context
  Future<void> reportNetworkError(
    dynamic error,
    StackTrace? stackTrace, {
    String? endpoint,
    String? method,
    int? statusCode,
    Map<String, dynamic>? requestData,
  }) async {
    await errorHandler.reportNetworkError(
      error,
      stackTrace,
      endpoint: endpoint,
      method: method,
      statusCode: statusCode,
      requestData: requestData,
    );
  }

  /// Report an authentication error
  Future<void> reportAuthError(
    dynamic error,
    StackTrace? stackTrace, {
    String? authMethod,
    String? userId,
  }) async {
    await errorHandler.reportAuthError(
      error,
      stackTrace,
      authMethod: authMethod,
      userId: userId,
    );
  }

  /// Report a UI/Navigation error
  Future<void> reportUIError(
    dynamic error,
    StackTrace? stackTrace, {
    String? screenName,
    String? widget,
    Map<String, dynamic>? uiState,
  }) async {
    await errorHandler.reportUIError(
      error,
      stackTrace,
      screenName: screenName,
      widget: widget,
      uiState: uiState,
    );
  }

  /// Report a business logic error
  Future<void> reportBusinessLogicError(
    dynamic error,
    StackTrace? stackTrace, {
    String? feature,
    String? operation,
    Map<String, dynamic>? businessContext,
  }) async {
    await errorHandler.reportBusinessLogicError(
      error,
      stackTrace,
      feature: feature,
      operation: operation,
      businessContext: businessContext,
    );
  }
}
