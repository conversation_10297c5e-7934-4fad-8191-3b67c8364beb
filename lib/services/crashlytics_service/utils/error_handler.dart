import 'dart:async';
import 'package:flutter/foundation.dart';
import '../domain/repositories/crashlytics_repository.dart';

/// Utility class for handling errors with automatic Crashlytics reporting
class ErrorHandler {
  final CrashlyticsRepository _crashlytics;

  const Error<PERSON><PERSON><PERSON>(this._crashlytics);

  /// Execute a function and automatically handle any errors
  Future<T?> handleAsync<T>(
    Future<T> Function() operation, {
    String? context,
    Map<String, dynamic>? metadata,
    T? fallbackValue,
    bool fatal = false,
    bool shouldRethrow = true,
  }) async {
    try {
      return await operation();
    } catch (error, stackTrace) {
      await _reportError(
        error,
        stackTrace,
        context: context,
        metadata: metadata,
        fatal: fatal,
      );

      if (shouldRethrow) {
        rethrow;
      }

      return fallbackValue;
    }
  }

  /// Execute a synchronous function and automatically handle any errors
  T? handleSync<T>(
    T Function() operation, {
    String? context,
    Map<String, dynamic>? metadata,
    T? fallbackValue,
    bool fatal = false,
    bool shouldRethrow = true,
  }) {
    try {
      return operation();
    } catch (error, stackTrace) {
      // Report error asynchronously to not block sync operations
      _reportError(
        error,
        stackTrace,
        context: context,
        metadata: metadata,
        fatal: fatal,
      ).catchError((e) => debugPrint('Failed to report error: $e'));

      if (shouldRethrow) {
        rethrow;
      }

      return fallbackValue;
    }
  }

  /// Report an error with additional context
  Future<void> reportError(
    dynamic error,
    StackTrace? stackTrace, {
    String? context,
    Map<String, dynamic>? metadata,
    bool fatal = false,
  }) async {
    await _reportError(
      error,
      stackTrace,
      context: context,
      metadata: metadata,
      fatal: fatal,
    );
  }

  /// Internal method to report errors with consistent formatting
  Future<void> _reportError(
    dynamic error,
    StackTrace? stackTrace, {
    String? context,
    Map<String, dynamic>? metadata,
    bool fatal = false,
  }) async {
    try {
      // Enhanced error context
      final errorContext = <String, dynamic>{
        'timestamp': DateTime.now().toIso8601String(),
        'error_type': error.runtimeType.toString(),
        'is_debug_mode': kDebugMode,
        if (context != null) 'context': context,
        if (metadata != null) ...metadata,
      };

      await _crashlytics.recordError(
        error,
        stackTrace ?? StackTrace.current,
        reason: context,
        context: errorContext,
        fatal: fatal,
      );
    } catch (e) {
      // Don't let error reporting errors break the app
      debugPrint('Failed to report error to Crashlytics: $e');
    }
  }

  /// Report a network error with specific context
  Future<void> reportNetworkError(
    dynamic error,
    StackTrace? stackTrace, {
    String? endpoint,
    String? method,
    int? statusCode,
    Map<String, dynamic>? requestData,
  }) async {
    await _reportError(
      error,
      stackTrace,
      context: 'Network Error',
      metadata: {
        'category': 'network',
        if (endpoint != null) 'endpoint': endpoint,
        if (method != null) 'method': method,
        if (statusCode != null) 'status_code': statusCode,
        if (requestData != null) 'request_data': requestData,
      },
      fatal: false,
    );
  }

  /// Report an authentication error
  Future<void> reportAuthError(
    dynamic error,
    StackTrace? stackTrace, {
    String? authMethod,
    String? userId,
  }) async {
    await _reportError(
      error,
      stackTrace,
      context: 'Authentication Error',
      metadata: {
        'category': 'authentication',
        if (authMethod != null) 'auth_method': authMethod,
        if (userId != null) 'user_id': userId,
      },
      fatal: false,
    );
  }

  /// Report a UI/Navigation error
  Future<void> reportUIError(
    dynamic error,
    StackTrace? stackTrace, {
    String? screenName,
    String? widget,
    Map<String, dynamic>? uiState,
  }) async {
    await _reportError(
      error,
      stackTrace,
      context: 'UI Error',
      metadata: {
        'category': 'ui',
        if (screenName != null) 'screen_name': screenName,
        if (widget != null) 'widget': widget,
        if (uiState != null) 'ui_state': uiState,
      },
      fatal: false,
    );
  }

  /// Report a business logic error
  Future<void> reportBusinessLogicError(
    dynamic error,
    StackTrace? stackTrace, {
    String? feature,
    String? operation,
    Map<String, dynamic>? businessContext,
  }) async {
    await _reportError(
      error,
      stackTrace,
      context: 'Business Logic Error',
      metadata: {
        'category': 'business_logic',
        if (feature != null) 'feature': feature,
        if (operation != null) 'operation': operation,
        if (businessContext != null) 'business_context': businessContext,
      },
      fatal: false,
    );
  }
}
