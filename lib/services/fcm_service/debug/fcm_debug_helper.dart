import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:selfeng/services/fcm_service/domain/providers/fcm_service_provider.dart';

/// Debug helper for testing FCM token functionality
class FCMDebugHelper {
  /// Test FCM token initialization after authentication
  /// Call this after user login to test if FCM tokens can be saved
  static Future<void> testFCMTokenAfterAuth(WidgetRef ref) async {
    try {
      final fcmService = ref.read(fcmServiceProvider);

      // Test FCM token initialization after auth
      final result = await fcmService.initializeFCMTokenAfterAuth();

      result.fold((error) {}, (_) {});
    } catch (e) {}
  }

  /// Test getting FCM token without saving
  static Future<void> testGetFCMToken(WidgetRef ref) async {
    try {
      final fcmService = ref.read(fcmServiceProvider);

      final tokenResult = await fcmService.getFCMToken();

      tokenResult.fold((error) {}, (token) {});
    } catch (e) {}
  }

  /// Test device ID retrieval
  static Future<void> testGetDeviceId(WidgetRef ref) async {
    try {
      final fcmService = ref.read(fcmServiceProvider);

      final deviceIdResult = await fcmService.getDeviceId();

      deviceIdResult.fold((error) {}, (deviceId) {});
    } catch (e) {}
  }

  /// Test topic subscription
  static Future<void> testTopicSubscription(WidgetRef ref, String topic) async {
    try {
      final fcmService = ref.read(fcmServiceProvider);

      final subscribeResult = await fcmService.subscribeToTopic(topic);

      subscribeResult.fold((error) {}, (_) {});
    } catch (e) {}
  }

  /// Test general topic subscription (default topic)
  static Future<void> testGeneralTopicSubscription(WidgetRef ref) async {
    await testTopicSubscription(ref, 'general_notification');
  }
}
