import 'package:device_info_plus/device_info_plus.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:selfeng/shared/domain/i_platform_service.dart';
import 'package:selfeng/services/user_data_service/domain/repositories/user_data_service_repository.dart';
import 'package:selfeng/shared/domain/models/either.dart';
import 'package:selfeng/shared/exceptions/http_exception.dart';

/// Repository for managing FCM tokens and device information
class FCMServiceRepository {
  final UserDataServiceRepository _userDataService;
  final FirebaseMessaging _firebaseMessaging;
  final DeviceInfoPlugin _deviceInfo;
  final IPlatformService _platformService;

  FCMServiceRepository(
    this._userDataService,
    this._firebaseMessaging,
    this._deviceInfo,
    this._platformService,
  );

  /// Initialize FCM token management
  /// This should be called during app initialization
  Future<Either<AppException, void>> initializeFCMToken() async {
    try {
      // Check if user is authenticated first
      final currentUser = _userDataService.dataSource.firebaseAuth.currentUser;
      if (currentUser == null) {
        // Still setup token refresh listener for when user logs in
        _setupTokenRefreshListener();
        return const Right(null);
      }

      // Get current FCM token
      final tokenResult = await getFCMToken();
      return tokenResult.fold((error) => Left(error), (token) async {
        if (token.isEmpty) {
          return Left(
            AppException(
              identifier: 'FCM token initialization failed',
              statusCode: 0,
              message: 'Unable to retrieve FCM token',
            ),
          );
        }

        // Get device ID
        final deviceIdResult = await getDeviceId();
        return deviceIdResult.fold((error) => Left(error), (deviceId) async {
          if (deviceId.isEmpty) {
            return Left(
              AppException(
                identifier: 'Device ID retrieval failed',
                statusCode: 0,
                message: 'Unable to retrieve device ID',
              ),
            );
          }

          // Save token to user data
          final saveResult = await _userDataService.saveFCMToken(
            token: token,
            deviceId: deviceId,
          );

          return saveResult.fold((error) => Left(error), (_) {
            // Listen for token refresh
            _setupTokenRefreshListener();
            return const Right(null);
          });
        });
      });
    } catch (e) {
      return Left(
        AppException(
          identifier: 'FCM initialization error',
          statusCode: 0,
          message: e.toString(),
        ),
      );
    }
  }

  /// Get the current FCM token
  Future<Either<AppException, String>> getFCMToken() async {
    try {
      final token = await _firebaseMessaging.getToken();
      return Right(token ?? '');
    } catch (e) {
      return Left(
        AppException(
          identifier: 'FCM token retrieval failed',
          statusCode: 0,
          message: e.toString(),
        ),
      );
    }
  }

  /// Get unique device identifier
  Future<Either<AppException, String>> getDeviceId() async {
    try {
      if (_platformService.isAndroid) {
        final androidInfo = await _deviceInfo.androidInfo;
        return Right(androidInfo.id);
      } else if (_platformService.isIOS) {
        final iosInfo = await _deviceInfo.iosInfo;
        return Right(iosInfo.identifierForVendor ?? '');
      } else {
        return Left(
          AppException(
            identifier: 'Unsupported platform',
            statusCode: 0,
            message: 'Device ID not available for this platform',
          ),
        );
      }
    } catch (e) {
      return Left(
        AppException(
          identifier: 'Device ID retrieval failed',
          statusCode: 0,
          message: e.toString(),
        ),
      );
    }
  }

  /// Refresh and save the current FCM token
  Future<Either<AppException, void>> refreshFCMToken() async {
    try {
      final tokenResult = await getFCMToken();
      return tokenResult.fold((error) => Left(error), (token) async {
        final deviceIdResult = await getDeviceId();
        return deviceIdResult.fold(
          (error) => Left(error),
          (deviceId) =>
              _userDataService.saveFCMToken(token: token, deviceId: deviceId),
        );
      });
    } catch (e) {
      return Left(
        AppException(
          identifier: 'FCM token refresh failed',
          statusCode: 0,
          message: e.toString(),
        ),
      );
    }
  }

  /// Remove FCM token for current device
  Future<Either<AppException, void>> removeFCMTokenForCurrentDevice() async {
    try {
      final deviceIdResult = await getDeviceId();
      return deviceIdResult.fold(
        (error) => Left(error),
        (deviceId) => _userDataService.removeFCMToken(deviceId: deviceId),
      );
    } catch (e) {
      return Left(
        AppException(
          identifier: 'FCM token removal failed',
          statusCode: 0,
          message: e.toString(),
        ),
      );
    }
  }

  /// Get all FCM tokens for the current user
  Future<Either<AppException, Map<String, dynamic>>> getAllFCMTokens() async {
    return await _userDataService.getFCMTokens();
  }

  /// Setup listener for FCM token refresh
  void _setupTokenRefreshListener() {
    _firebaseMessaging.onTokenRefresh.listen((newToken) async {
      final deviceIdResult = await getDeviceId();
      deviceIdResult.fold((error) => null, (deviceId) async {
        await _userDataService.saveFCMToken(
          token: newToken,
          deviceId: deviceId,
        );
      });
    });
  }

  /// Request notification permissions
  Future<Either<AppException, NotificationSettings>>
  requestPermissions() async {
    try {
      final settings = await _firebaseMessaging.requestPermission(
        alert: true,
        announcement: true,
        badge: true,
        carPlay: true,
        criticalAlert: true,
        provisional: false,
        sound: true,
      );

      return Right(settings);
    } catch (e) {
      return Left(
        AppException(
          identifier: 'Permission request failed',
          statusCode: 0,
          message: e.toString(),
        ),
      );
    }
  }

  /// Check if notifications are enabled
  Future<Either<AppException, bool>> areNotificationsEnabled() async {
    try {
      final settings = await _firebaseMessaging.getNotificationSettings();
      return Right(
        settings.authorizationStatus == AuthorizationStatus.authorized,
      );
    } catch (e) {
      return Left(
        AppException(
          identifier: 'Permission check failed',
          statusCode: 0,
          message: e.toString(),
        ),
      );
    }
  }

  /// Initialize FCM token after user authentication
  /// Call this method after user successfully logs in
  Future<Either<AppException, void>> initializeFCMTokenAfterAuth() async {
    try {
      // Check if user is authenticated
      final currentUser = _userDataService.dataSource.firebaseAuth.currentUser;
      if (currentUser == null) {
        return Left(
          AppException(
            identifier: 'User not authenticated',
            statusCode: 401,
            message: 'User must be authenticated to save FCM token',
          ),
        );
      }

      // Get current FCM token
      final tokenResult = await getFCMToken();
      return tokenResult.fold((error) => Left(error), (token) async {
        if (token.isEmpty) {
          return Left(
            AppException(
              identifier: 'FCM token initialization failed',
              statusCode: 0,
              message: 'Unable to retrieve FCM token',
            ),
          );
        }

        // Get device ID
        final deviceIdResult = await getDeviceId();
        return deviceIdResult.fold((error) => Left(error), (deviceId) async {
          if (deviceId.isEmpty) {
            return Left(
              AppException(
                identifier: 'Device ID retrieval failed',
                statusCode: 0,
                message: 'Unable to retrieve device ID',
              ),
            );
          }

          // Save token to user data
          final saveResult = await _userDataService.saveFCMToken(
            token: token,
            deviceId: deviceId,
          );

          return saveResult.fold((error) => Left(error), (_) async {
            // Subscribe to general notification topic after successful token save
            await _subscribeToGeneralTopic();
            return const Right(null);
          });
        });
      });
    } catch (e) {
      return Left(
        AppException(
          identifier: 'FCM token initialization after auth failed',
          statusCode: 0,
          message: e.toString(),
        ),
      );
    }
  }

  /// Subscribe to general notification topic
  Future<void> _subscribeToGeneralTopic() async {
    try {
      await _firebaseMessaging.subscribeToTopic('general_notification');
    } catch (e) {
      // Don't throw error - topic subscription failure shouldn't break the flow
    }
  }

  /// Subscribe to a specific topic
  Future<Either<AppException, void>> subscribeToTopic(String topic) async {
    try {
      await _firebaseMessaging.subscribeToTopic(topic);
      return const Right(null);
    } catch (e) {
      return Left(
        AppException(
          identifier: 'Topic subscription failed',
          statusCode: 0,
          message: 'Failed to subscribe to topic $topic: ${e.toString()}',
        ),
      );
    }
  }

  /// Unsubscribe from a specific topic
  Future<Either<AppException, void>> unsubscribeFromTopic(String topic) async {
    try {
      await _firebaseMessaging.unsubscribeFromTopic(topic);
      return const Right(null);
    } catch (e) {
      return Left(
        AppException(
          identifier: 'Topic unsubscription failed',
          statusCode: 0,
          message: 'Failed to unsubscribe from topic $topic: ${e.toString()}',
        ),
      );
    }
  }
}
