# In-App Update Service

This service provides a comprehensive solution for handling in-app updates in the SELF Mobile application, with support for both flexible and forced updates.

## 🚀 Features

- **Flexible Updates**: Non-blocking updates that download in the background
- **Forced Updates**: Critical updates that require immediate installation
- **Modern UI**: Google Play-inspired dialogs with consistent design
- **Automatic Detection**: Smart detection of update priority and type
- **Error Handling**: Comprehensive error handling and user feedback
- **Update Banner**: Non-intrusive dashboard banner for flexible updates
- **Installation Prompts**: Automatic prompts when updates are ready to install

## ✅ Fixed Issues

This implementation fixes the critical issue where **flexible updates would download successfully but never install**. The **simplified solution** includes:

1. **Native Google Play Dialogs**: Uses ONLY native dialogs for update detection and download
2. **Custom Installation UI**: Simple UI components that appear when update is ready to install
3. **Improved Monitoring**: Better detection of download completion status
4. **Installation Trigger**: Clear UI for calling `completeFlexibleUpdate()` after download

## 🎯 Simplified Approach

**What Uses Native Google Play UI:**
- ✅ Update detection and availability dialog
- ✅ Download progress notification/snackbar
- ✅ Immediate updates (critical updates)

**What Uses Custom UI:**
- ✅ Installation prompt when flexible update is downloaded
- ✅ Dashboard banner for "ready to install" state

## Components

### Dialogs

#### FlexibleUpdateDialog
A modern dialog for non-critical updates featuring:
- **Official Google Play logo** from SVG asset
- Clean, user-friendly interface with subtle shadows
- Support for downloaded update installation
- Priority indicators for recommended updates
- "Later" option for user convenience

```dart
// Show flexible update dialog
await showFlexibleUpdateDialog(context);
```

#### ForcedUpdateDialog
A full-screen dialog for critical updates featuring:
- Full-screen immersive experience
- App icon with update indicator badge
- **Google Play branding** for trust and recognition
- Detailed feature list and benefits
- No dismissal option (forced update)
- Professional, trustworthy design

```dart
// Show forced update dialog
await showForcedUpdateDialog(context);
```

#### Auto-Selection (Recommended)
The service automatically chooses the appropriate dialog based on update priority:

```dart
// Automatically shows the right dialog type
await showUpdateDialog(context, isForced: shouldForce);
```

### Repository

The `InAppUpdateServiceRepository` handles all update operations:

- **Initialize**: Set up the update service
- **Check for Updates**: Query available updates
- **Start Updates**: Begin flexible or immediate updates
- **Monitor Progress**: Track download and installation status

### Controller

The `InAppUpdateController` manages state and provides reactive updates:

- **State Management**: Reactive state using Riverpod
- **Update Monitoring**: Automatic progress tracking
- **Error Handling**: Comprehensive error management
- **Priority Detection**: Smart update priority classification

## Usage

### Basic Setup

1. **Initialize the service** (usually in main.dart):
```dart
final updateController = ref.read(inAppUpdateControllerProvider.notifier);
await updateController.initialize();
```

2. **Check for updates** periodically:
```dart
await updateController.checkForUpdates();
```

3. **Show appropriate dialog** when updates are available:
```dart
final shouldForce = updateController.shouldForceImmediateUpdate;
await showUpdateDialog(context, isForced: shouldForce);
```

### Integration Examples

#### Dashboard Integration
```dart
class DashboardScreen extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      body: Column(
        children: [
          // Show update banner if available
          const UpdateBanner(),
          // ... rest of dashboard
        ],
      ),
    );
  }
}
```

#### App Startup Check
```dart
class MyApp extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Check for updates on app start
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      final updateController = ref.read(inAppUpdateControllerProvider.notifier);
      await updateController.initialize();
      
      final updateState = ref.read(inAppUpdateControllerProvider);
      if (updateState.isUpdateAvailable && context.mounted) {
        final shouldForce = updateController.shouldForceImmediateUpdate;
        await showUpdateDialog(context, isForced: shouldForce);
      }
    });
    
    return MaterialApp(/* ... */);
  }
}
```

## Update Types

### Flexible Updates
- **Use Case**: Regular updates, feature additions, minor bug fixes
- **Behavior**: Downloads in background, user can continue using app
- **UI**: Dialog with "Later" option
- **Installation**: User chooses when to install

### Forced Updates
- **Use Case**: Critical security updates, breaking changes
- **Behavior**: Blocks app usage until update is installed
- **UI**: Full-screen dialog, no dismissal option
- **Installation**: Immediate installation required

## Priority System

The service uses a smart priority system:

- **Critical Updates**: Version difference > 5 → Forced update
- **Normal Updates**: Version difference ≤ 5 → Flexible update
- **High Priority**: Recommended updates with priority indicators

## Error Handling

The service provides comprehensive error handling:

- **Network Errors**: Graceful handling of connectivity issues
- **Update Failures**: Clear error messages and retry options
- **Platform Support**: Automatic detection of platform capabilities
- **Fallback Behavior**: Graceful degradation on unsupported platforms

## Customization

### Styling
The dialogs use the app's theme system and can be customized by modifying:
- `AppColors` for color scheme
- Theme data for typography
- Individual dialog components for specific styling

### Behavior
Update behavior can be customized through:
- Priority thresholds in the repository
- Dialog presentation logic
- Error handling strategies

## Migration Complete ✅

The legacy `UpdateDialog` has been **completely removed** and replaced with the new modern dialogs. All existing code automatically uses the new implementation:

- `showUpdateDialog()` now automatically selects the appropriate dialog
- `UpdateBanner` uses the new dialogs
- All examples and documentation updated
- **Google Play SVG asset** integrated for authentic branding

### Key Improvements
- **Official Google Play logo** from `assets/images/icons/google_play.svg`
- Modern, professional UI design
- Better user experience with clear visual hierarchy
- Consistent branding across both dialog types

## Testing

Use the example screen for testing different dialog types:
```dart
Navigator.push(
  context,
  MaterialPageRoute(builder: (_) => const UpdateDialogsExampleScreen()),
);
```

## Platform Support

- **Android**: Full support via Google Play In-App Updates
- **iOS**: Full support via App Store updates
- **Web/Desktop**: Graceful fallback (no-op)

## Best Practices

1. **Initialize Early**: Initialize the service during app startup
2. **Check Periodically**: Check for updates on app resume/focus
3. **Respect User Choice**: Don't force non-critical updates
4. **Provide Feedback**: Show clear progress and error states
5. **Test Thoroughly**: Test both update types in different scenarios