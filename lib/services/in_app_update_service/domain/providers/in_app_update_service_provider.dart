import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:selfeng/services/in_app_update_service/domain/repositories/in_app_update_service_impl.dart';
import 'package:selfeng/services/in_app_update_service/domain/repositories/in_app_update_service_repository.dart';
import 'package:selfeng/services/in_app_update_service/domain/repositories/package_info_service_impl.dart';
import 'package:selfeng/shared/domain/providers/platform_service_provider.dart';

/// Provider for InAppUpdateServiceRepository with dependency injection
final inAppUpdateServiceProvider = Provider<InAppUpdateServiceRepository>((
  ref,
) {
  return InAppUpdateServiceRepository(
    inAppUpdateService: InAppUpdateServiceImpl(),
    packageInfoService: PackageInfoServiceImpl(),
    platformService: ref.read(platformServiceProvider),
  );
});
