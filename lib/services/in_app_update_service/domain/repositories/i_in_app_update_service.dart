import 'package:in_app_update/in_app_update.dart';

/// Interface for in-app update service operations
abstract class IInAppUpdateService {
  /// Check for available app updates
  Future<AppUpdateInfo> checkForUpdate();

  /// Perform an immediate update (blocks the app until complete)
  Future<void> performImmediateUpdate();

  /// Start a flexible update (downloads in background)
  Future<void> startFlexibleUpdate();

  /// Complete a flexible update (installs the downloaded update)
  Future<void> completeFlexibleUpdate();
}
