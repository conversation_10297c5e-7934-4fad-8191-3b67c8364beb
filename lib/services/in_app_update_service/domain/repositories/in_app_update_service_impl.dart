import 'package:in_app_update/in_app_update.dart';
import 'package:selfeng/services/in_app_update_service/domain/repositories/i_in_app_update_service.dart';

/// Concrete implementation of IInAppUpdateService using the in_app_update package
class InAppUpdateServiceImpl implements IInAppUpdateService {
  @override
  Future<AppUpdateInfo> checkForUpdate() async {
    return await InAppUpdate.checkForUpdate();
  }

  @override
  Future<void> performImmediateUpdate() async {
    await InAppUpdate.performImmediateUpdate();
  }

  @override
  Future<void> startFlexibleUpdate() async {
    await InAppUpdate.startFlexibleUpdate();
  }

  @override
  Future<void> completeFlexibleUpdate() async {
    await InAppUpdate.completeFlexibleUpdate();
  }
}
