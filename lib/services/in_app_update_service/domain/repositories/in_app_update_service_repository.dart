import 'package:in_app_update/in_app_update.dart';
import 'package:selfeng/services/in_app_update_service/domain/repositories/i_in_app_update_service.dart';
import 'package:selfeng/services/in_app_update_service/domain/repositories/i_package_info_service.dart';
import 'package:selfeng/shared/domain/i_platform_service.dart';
import 'package:selfeng/shared/domain/models/either.dart';
import 'package:selfeng/shared/exceptions/http_exception.dart';

/// Repository for managing in-app updates
class InAppUpdateServiceRepository {
  final IInAppUpdateService _inAppUpdateService;
  final IPackageInfoService _packageInfoService;
  final IPlatformService _platformService;

  /// Constructor with dependency injection
  InAppUpdateServiceRepository({
    required IInAppUpdateService inAppUpdateService,
    required IPackageInfoService packageInfoService,
    required IPlatformService platformService,
  }) : _inAppUpdateService = inAppUpdateService,
       _packageInfoService = packageInfoService,
       _platformService = platformService;

  /// Initialize the in-app update service
  Future<Either<AppException, void>> initialize() async {
    try {
      // In-app updates are only supported on Android and iOS
      if (!_platformService.isAndroid && !_platformService.isIOS) {
        return const Right(null);
      }

      // Verify package info is accessible
      await _packageInfoService.getBuildNumber();

      return const Right(null);
    } catch (e) {
      return Left(
        AppException(
          message: 'Failed to initialize in-app update service: $e',
          statusCode: 500,
          identifier: 'in_app_update_init_error',
        ),
      );
    }
  }

  /// Check for available updates
  Future<Either<AppException, AppUpdateInfo?>> checkForUpdate() async {
    try {
      // In-app updates are only supported on Android and iOS
      if (!_platformService.isAndroid && !_platformService.isIOS) {
        return const Right(null);
      }

      final updateInfo = await _inAppUpdateService.checkForUpdate();

      return Right(updateInfo);
    } catch (e) {
      return Left(
        AppException(
          message: 'Failed to check for updates: $e',
          statusCode: 500,
          identifier: 'in_app_update_check_error',
        ),
      );
    }
  }

  /// Start an immediate update (blocks the app until update is complete)
  /// Use this for critical updates that must be installed immediately
  Future<Either<AppException, void>> startImmediateUpdate(
    AppUpdateInfo updateInfo,
  ) async {
    try {
      if (updateInfo.updateAvailability != UpdateAvailability.updateAvailable) {
        return Left(
          AppException(
            message: 'No update available',
            statusCode: 404,
            identifier: 'no_update_available',
          ),
        );
      }

      if (!updateInfo.immediateUpdateAllowed) {
        return Left(
          AppException(
            message: 'Immediate update not allowed',
            statusCode: 403,
            identifier: 'immediate_update_not_allowed',
          ),
        );
      }

      await _inAppUpdateService.performImmediateUpdate();
      return const Right(null);
    } catch (e) {
      return Left(
        AppException(
          message: 'Failed to start immediate update: $e',
          statusCode: 500,
          identifier: 'immediate_update_failed',
        ),
      );
    }
  }

  /// Start a flexible update (downloads in background, user can continue using app)
  /// Use this for non-critical updates
  Future<Either<AppException, void>> startFlexibleUpdate(
    AppUpdateInfo updateInfo,
  ) async {
    try {
      if (updateInfo.updateAvailability != UpdateAvailability.updateAvailable) {
        return Left(
          AppException(
            message: 'No update available',
            statusCode: 404,
            identifier: 'no_update_available',
          ),
        );
      }

      if (!updateInfo.flexibleUpdateAllowed) {
        return Left(
          AppException(
            message: 'Flexible update not allowed',
            statusCode: 403,
            identifier: 'flexible_update_not_allowed',
          ),
        );
      }

      await _inAppUpdateService.startFlexibleUpdate();
      return const Right(null);
    } catch (e) {
      return Left(
        AppException(
          message: 'Failed to start flexible update: $e',
          statusCode: 500,
          identifier: 'flexible_update_failed',
        ),
      );
    }
  }

  /// Complete a flexible update (installs the downloaded update)
  /// Call this after a flexible update has been downloaded
  Future<Either<AppException, void>> completeFlexibleUpdate() async {
    try {
      await _inAppUpdateService.completeFlexibleUpdate();
      return const Right(null);
    } catch (e) {
      return Left(
        AppException(
          message: 'Failed to complete flexible update: $e',
          statusCode: 500,
          identifier: 'complete_flexible_update_failed',
        ),
      );
    }
  }

  /// Check if a flexible update is downloaded and ready to install
  Future<Either<AppException, bool>> isFlexibleUpdateDownloaded(
    AppUpdateInfo updateInfo,
  ) async {
    try {
      final isDownloaded = updateInfo.installStatus == InstallStatus.downloaded;
      return Right(isDownloaded);
    } catch (e) {
      return Left(
        AppException(
          message: 'Failed to check flexible update status: $e',
          statusCode: 500,
          identifier: 'check_flexible_update_status_failed',
        ),
      );
    }
  }

  /// Check if update is available
  bool isUpdateAvailable(AppUpdateInfo updateInfo) =>
      updateInfo.updateAvailability == UpdateAvailability.updateAvailable;

  /// Check if immediate update is allowed
  bool isImmediateUpdateAllowed(AppUpdateInfo updateInfo) =>
      updateInfo.immediateUpdateAllowed;

  /// Check if flexible update is allowed
  bool isFlexibleUpdateAllowed(AppUpdateInfo updateInfo) =>
      updateInfo.flexibleUpdateAllowed;

  /// Check if this is a critical update that requires immediate installation
  /// Critical updates are determined by version code difference > 5
  Future<Either<AppException, bool>> isCriticalUpdate(
    AppUpdateInfo updateInfo,
  ) async {
    try {
      final currentVersionCodeResult = await _getCurrentVersionCode();
      if (currentVersionCodeResult.isLeft()) {
        return currentVersionCodeResult.fold(
          (error) => Left(error),
          (_) => throw StateError('Should not reach here'),
        );
      }

      final currentVersionCode = currentVersionCodeResult.fold(
        (_) => throw StateError('Should not reach here'),
        (code) => code,
      );

      final availableVersionCode = updateInfo.availableVersionCode;

      if (availableVersionCode != null && currentVersionCode > 0) {
        final versionDifference = availableVersionCode - currentVersionCode;
        return Right(versionDifference > 5);
      }

      return const Right(false);
    } catch (e) {
      return Left(
        AppException(
          message: 'Failed to check if update is critical: $e',
          statusCode: 500,
          identifier: 'check_critical_update_failed',
        ),
      );
    }
  }

  /// Check if this is a normal update (version code difference <= 5)
  Future<Either<AppException, bool>> isNormalUpdate(
    AppUpdateInfo updateInfo,
  ) async {
    final criticalResult = await isCriticalUpdate(updateInfo);
    return criticalResult.fold(
      (error) => Left(error),
      (isCritical) => Right(!isCritical),
    );
  }

  /// Legacy compatibility - maps to critical/normal system
  /// Returns 5 for critical updates, 2 for normal updates
  Future<Either<AppException, int>> getUpdatePriority(
    AppUpdateInfo updateInfo,
  ) async {
    final criticalResult = await isCriticalUpdate(updateInfo);
    return criticalResult.fold(
      (error) => Left(error),
      (isCritical) => Right(isCritical ? 5 : 2),
    );
  }

  /// Legacy compatibility - critical updates are considered high priority
  Future<Either<AppException, bool>> isHighPriorityUpdate(
    AppUpdateInfo updateInfo,
  ) async {
    return isCriticalUpdate(updateInfo);
  }

  /// Get current app version code
  Future<Either<AppException, int>> _getCurrentVersionCode() async {
    try {
      final buildNumber = await _packageInfoService.getBuildNumber();
      final versionCode = int.tryParse(buildNumber) ?? 1;
      return Right(versionCode);
    } catch (e) {
      return Left(
        AppException(
          message: 'Failed to get version code: $e',
          statusCode: 500,
          identifier: 'get_version_code_failed',
        ),
      );
    }
  }
}
