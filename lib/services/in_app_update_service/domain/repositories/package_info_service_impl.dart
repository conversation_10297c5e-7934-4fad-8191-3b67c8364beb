import 'package:package_info_plus/package_info_plus.dart';
import 'package:selfeng/services/in_app_update_service/domain/repositories/i_package_info_service.dart';

/// Concrete implementation of IPackageInfoService using package_info_plus
class PackageInfoServiceImpl implements IPackageInfoService {
  @override
  Future<String> getBuildNumber() async {
    final packageInfo = await PackageInfo.fromPlatform();
    return packageInfo.buildNumber;
  }
}
