// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'in_app_update_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, type=warning
/// Controller for managing in-app updates using new Riverpod 3.0 API

@ProviderFor(InAppUpdateController)
const inAppUpdateControllerProvider = InAppUpdateControllerProvider._();

/// Controller for managing in-app updates using new Riverpod 3.0 API
final class InAppUpdateControllerProvider
    extends $NotifierProvider<InAppUpdateController, InAppUpdateState> {
  /// Controller for managing in-app updates using new Riverpod 3.0 API
  const InAppUpdateControllerProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'inAppUpdateControllerProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$inAppUpdateControllerHash();

  @$internal
  @override
  InAppUpdateController create() => InAppUpdateController();

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(InAppUpdateState value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<InAppUpdateState>(value),
    );
  }
}

String _$inAppUpdateControllerHash() =>
    r'de38f17c3d2080269fde4b0c6e9479b8c8931930';

/// Controller for managing in-app updates using new Riverpod 3.0 API

abstract class _$InAppUpdateController extends $Notifier<InAppUpdateState> {
  InAppUpdateState build();
  @$mustCallSuper
  @override
  void runBuild() {
    final created = build();
    final ref = this.ref as $Ref<InAppUpdateState, InAppUpdateState>;
    final element =
        ref.element
            as $ClassProviderElement<
              AnyNotifier<InAppUpdateState, InAppUpdateState>,
              InAppUpdateState,
              Object?,
              Object?
            >;
    element.handleValue(ref, created);
  }
}
