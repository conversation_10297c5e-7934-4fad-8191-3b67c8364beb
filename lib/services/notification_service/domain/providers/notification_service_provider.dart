import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:selfeng/services/crashlytics_service/domain/providers/crashlytics_service_provider.dart';
import 'package:selfeng/services/fcm_service/domain/providers/fcm_service_provider.dart';
import 'package:selfeng/services/notification_service/domain/repositories/notification_service_repository.dart';

/// Provider for NotificationServiceRepository
final notificationServiceProvider = Provider<NotificationServiceRepository>((
  ref,
) {
  final fcmService = ref.watch(fcmServiceProvider);
  final firebaseMessaging = ref.watch(firebaseMessagingProvider);
  final crashlyticsService = ref.watch(crashlyticsServiceProvider);

  return NotificationServiceRepository(
    fcmService,
    firebaseMessaging,
    crashlyticsService,
  );
});
