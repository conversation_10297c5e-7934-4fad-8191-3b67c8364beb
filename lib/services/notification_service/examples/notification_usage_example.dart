import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:selfeng/services/fcm_service/domain/providers/fcm_service_provider.dart';
import 'package:selfeng/services/notification_service/domain/providers/notification_service_provider.dart';

/// Example widget showing how to properly use the new notification services
class NotificationServiceExample extends ConsumerStatefulWidget {
  const NotificationServiceExample({super.key});

  @override
  ConsumerState<NotificationServiceExample> createState() =>
      _NotificationServiceExampleState();
}

class _NotificationServiceExampleState
    extends ConsumerState<NotificationServiceExample> {
  String _status = 'Not initialized';
  String _fcmToken = '';
  int _notificationCount = 0;
  bool _notificationsEnabled = false;

  @override
  void initState() {
    super.initState();
    _initializeNotifications();
  }

  /// Initialize notification services
  Future<void> _initializeNotifications() async {
    final notificationService = ref.read(notificationServiceProvider);

    setState(() {
      _status = 'Initializing...';
    });

    final result = await notificationService.initialize();
    result.fold(
      (error) {
        setState(() {
          _status = 'Error: ${error.message}';
        });
      },
      (_) {
        setState(() {
          _status = 'Initialized successfully';
        });
        _loadNotificationData();
      },
    );
  }

  /// Load notification-related data
  Future<void> _loadNotificationData() async {
    final notificationService = ref.read(notificationServiceProvider);
    final fcmService = ref.read(fcmServiceProvider);

    // Get FCM token
    final tokenResult = await fcmService.getFCMToken();
    tokenResult.fold((error) => null, (token) {
      setState(() {
        _fcmToken = token;
      });
    });

    // Get notification count
    final countResult = await notificationService.getNotificationCount();
    countResult.fold((error) => null, (count) {
      setState(() {
        _notificationCount = count;
      });
    });

    // Check if notifications are enabled
    final enabledResult = await notificationService.areNotificationsEnabled();
    enabledResult.fold((error) => null, (enabled) {
      setState(() {
        _notificationsEnabled = enabled;
      });
    });
  }

  /// Refresh FCM token
  Future<void> _refreshFCMToken() async {
    final notificationService = ref.read(notificationServiceProvider);

    final result = await notificationService.refreshFCMToken();
    result.fold(
      (error) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to refresh token: ${error.message}')),
        );
      },
      (_) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('FCM token refreshed successfully')),
        );
        _loadNotificationData();
      },
    );
  }

  /// Clear notification count
  Future<void> _clearNotificationCount() async {
    final notificationService = ref.read(notificationServiceProvider);

    final result = await notificationService.clearNotificationCount();
    result.fold(
      (error) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to clear count: ${error.message}')),
        );
      },
      (_) {
        setState(() {
          _notificationCount = 0;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Notification count cleared')),
        );
      },
    );
  }

  /// Remove FCM token (useful for logout)
  Future<void> _removeFCMToken() async {
    final notificationService = ref.read(notificationServiceProvider);

    final result = await notificationService.removeFCMToken();
    result.fold(
      (error) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to remove token: ${error.message}')),
        );
      },
      (_) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('FCM token removed successfully')),
        );
        _loadNotificationData();
      },
    );
  }

  /// Subscribe to a custom topic
  Future<void> _subscribeToTopic(String topic) async {
    final notificationService = ref.read(notificationServiceProvider);

    final result = await notificationService.subscribeToTopic(topic);
    result.fold(
      (error) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to subscribe to $topic: ${error.message}'),
          ),
        );
      },
      (_) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Subscribed to $topic successfully')),
        );
      },
    );
  }

  /// Unsubscribe from general topic (useful for logout)
  Future<void> _unsubscribeFromGeneralTopic() async {
    final notificationService = ref.read(notificationServiceProvider);

    final result = await notificationService.unsubscribeFromGeneralTopic();
    result.fold(
      (error) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to unsubscribe: ${error.message}')),
        );
      },
      (_) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Unsubscribed from general notifications'),
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Notification Service Example')),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Service Status',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    Text('Status: $_status'),
                    const SizedBox(height: 8),
                    Text('Notifications Enabled: $_notificationsEnabled'),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'FCM Token',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      _fcmToken.isEmpty ? 'No token available' : _fcmToken,
                      style: const TextStyle(fontSize: 12),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        ElevatedButton(
                          onPressed: _refreshFCMToken,
                          child: const Text('Refresh Token'),
                        ),
                        const SizedBox(width: 8),
                        ElevatedButton(
                          onPressed: _removeFCMToken,
                          child: const Text('Remove Token'),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Notification Count',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    Text('Count: $_notificationCount'),
                    const SizedBox(height: 8),
                    ElevatedButton(
                      onPressed: _clearNotificationCount,
                      child: const Text('Clear Count'),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Topic Management',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    const Text('Default topic: general_notification'),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        ElevatedButton(
                          onPressed: () => _subscribeToTopic('custom_topic'),
                          child: const Text('Subscribe Custom'),
                        ),
                        const SizedBox(width: 8),
                        ElevatedButton(
                          onPressed: _unsubscribeFromGeneralTopic,
                          child: const Text('Unsubscribe General'),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
