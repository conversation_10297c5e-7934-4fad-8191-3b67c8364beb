import 'dart:convert';
import 'dart:ui';

import 'package:selfeng/shared/data/local/storage_service.dart';
import 'package:selfeng/shared/domain/models/either.dart';
import 'package:selfeng/shared/exceptions/http_exception.dart';
import 'package:selfeng/shared/globals.dart';

// Define the key for storing the audio enabled state
const String AUDIO_ENABLED_LOCAL_STORAGE_KEY = 'audio_enabled';

abstract class SettingDataSource {
  String get storageKeyLocale;
  String get storageKeyAudioEnabled; // Add key getter for audio

  Future<Either<AppException, Locale>> fetchLocale();
  Future<bool> saveLocale({required Locale locale});
  Future<bool> deleteLocale();
  Future<bool> hasLocale();

  // Add abstract methods for audio state
  Future<Either<AppException, bool>> getAudioEnabled();
  Future<bool> saveAudioEnabled({required bool enabled});
}

class SettingLocalDatasource extends SettingDataSource {
  SettingLocalDatasource(this.storageService);

  final StorageService storageService;

  @override
  String get storageKeyLocale => Locale_LOCAL_STORAGE_KEY;

  @override
  String get storageKeyAudioEnabled => AUDIO_ENABLED_LOCAL_STORAGE_KEY; // Implement key getter

  @override
  Future<Either<AppException, Locale>> fetchLocale() async {
    try {
      final data = await storageService.get(storageKeyLocale);
      if (data == null) {
        return Left(
          AppException(
            identifier: 'SettingLocalDatasource',
            statusCode: 404,
            message: 'Locale not found',
          ),
        );
      }

      final localeJson = jsonDecode(data.toString());
      if (localeJson is! Map<String, dynamic>) {
        return Left(
          AppException(
            identifier: 'SettingLocalDatasource',
            statusCode: 400,
            message: 'Invalid locale data format',
          ),
        );
      }

      final languageCode = localeJson['languageCode'];
      if (languageCode == null || languageCode is! String) {
        return Left(
          AppException(
            identifier: 'SettingLocalDatasource',
            statusCode: 400,
            message: 'Invalid or missing language code',
          ),
        );
      }

      return Right(
        Locale.fromSubtags(
          languageCode: languageCode,
          countryCode: localeJson['countryCode'],
        ),
      );
    } catch (e) {
      return Left(
        AppException(
          identifier: 'SettingLocalDatasource',
          statusCode: 500,
          message: 'Failed to fetch locale: ${e.toString()}',
        ),
      );
    }
  }

  @override
  Future<bool> saveLocale({required Locale locale}) async {
    try {
      return await storageService.set(
        storageKeyLocale,
        jsonEncode({
          'languageCode': locale.languageCode,
          'countryCode': locale.countryCode,
        }),
      );
    } catch (e) {
      return false;
    }
  }

  @override
  Future<bool> deleteLocale() async {
    try {
      return await storageService.remove(storageKeyLocale);
    } catch (e) {
      return false;
    }
  }

  @override
  Future<bool> hasLocale() async {
    try {
      return await storageService.has(storageKeyLocale);
    } catch (e) {
      return false;
    }
  }

  @override
  Future<Either<AppException, bool>> getAudioEnabled() async {
    try {
      final data = await storageService.get(storageKeyAudioEnabled);
      // Default to true if not found or null
      if (data == null) {
        return const Right(true);
      }
      // Assuming storageService stores bools directly or as 'true'/'false' strings
      if (data is bool) {
        return Right(data);
      } else if (data is String) {
        return Right(data.toLowerCase() == 'true');
      }
      // Default to true if type is unexpected
      return const Right(true);
    } catch (e) {
      // Default to true in case of any error during fetch
      return const Right(true);
      // Optionally, return Left with an exception if specific error handling is needed
      // return Left(AppException(
      //   identifier: 'SettingLocalDatasource.getAudioEnabled',
      //   message: 'Failed to get audio state: ${e.toString()}',
      //   statusCode: 500, // Or appropriate code
      // ));
    }
  }

  @override
  Future<bool> saveAudioEnabled({required bool enabled}) async {
    try {
      // Convert boolean to string before saving
      return await storageService.set(
        storageKeyAudioEnabled,
        enabled.toString(),
      );
    } catch (e) {
      return false;
    }
  }
}
