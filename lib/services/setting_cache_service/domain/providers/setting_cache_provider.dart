import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:selfeng/services/setting_cache_service/data/datasource/setting_local_datasource.dart';
import 'package:selfeng/services/setting_cache_service/data/repositories/setting_repository_impl.dart';
import 'package:selfeng/services/setting_cache_service/domain/repositories/setting_cache_repository.dart';
import 'package:selfeng/shared/data/local/storage_service.dart';
import 'package:selfeng/shared/domain/providers/shared_preferences_storage_service_provider.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

part 'setting_cache_provider.g.dart';

final settingDatasourceProvider =
    Provider.family<SettingDataSource, StorageService>(
      (_, storageService) =>
          SettingLocalDatasource(storageService), // Corrected variable name
    );

final settingLocalRepositoryProvider = Provider<SettingCacheRepository>((ref) {
  final storageService = ref.watch(storageServiceProvider);

  // Use .call() for family providers or pass the argument directly
  final datasource = ref.watch(settingDatasourceProvider(storageService));

  final repository = SettingCacheRepositoryImpl(datasource);

  return repository;
});

// Define the Notifier for audio toggle using new Riverpod 3.0 API
@riverpod
class AudioToggleNotifier extends _$AudioToggleNotifier {
  @override
  bool build() {
    // Default state to true
    _loadInitialState();
    return true;
  }

  SettingCacheRepository get _repository =>
      ref.watch(settingLocalRepositoryProvider);

  Future<void> _loadInitialState() async {
    final result = await _repository.getAudioEnabled();
    // Update state based on fetched value, defaulting to true on error/Left
    state = result.fold(
      (exception) => true, // Default to true on error
      (isEnabled) => isEnabled,
    );
  }

  Future<void> setAudioEnabled(bool enabled) async {
    await _repository.saveAudioEnabled(enabled: enabled);
    state = enabled;
  }
}
