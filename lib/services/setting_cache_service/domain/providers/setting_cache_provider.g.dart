// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'setting_cache_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, type=warning

@ProviderFor(AudioToggleNotifier)
const audioToggleProvider = AudioToggleNotifierProvider._();

final class AudioToggleNotifierProvider
    extends $NotifierProvider<AudioToggleNotifier, bool> {
  const AudioToggleNotifierProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'audioToggleProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$audioToggleNotifierHash();

  @$internal
  @override
  AudioToggleNotifier create() => AudioToggleNotifier();

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$audioToggleNotifierHash() =>
    r'341b13df8854f6da4b07f12d7406bdaa75fa5c2a';

abstract class _$AudioToggleNotifier extends $Notifier<bool> {
  bool build();
  @$mustCallSuper
  @override
  void runBuild() {
    final created = build();
    final ref = this.ref as $Ref<bool, bool>;
    final element =
        ref.element
            as $ClassProviderElement<
              AnyNotifier<bool, bool>,
              bool,
              Object?,
              Object?
            >;
    element.handleValue(ref, created);
  }
}
