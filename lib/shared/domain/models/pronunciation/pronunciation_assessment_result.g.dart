// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'pronunciation_assessment_result.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_PronunciationAssessmentResult _$PronunciationAssessmentResultFromJson(
  Map<String, dynamic> json,
) => _PronunciationAssessmentResult(
  result: PronunciationAssessmentResultData.fromJson(
    json['result'] as Map<String, dynamic>,
  ),
);

Map<String, dynamic> _$PronunciationAssessmentResultToJson(
  _PronunciationAssessmentResult instance,
) => <String, dynamic>{'result': instance.result};

_PronunciationAssessmentResultData _$PronunciationAssessmentResultDataFromJson(
  Map<String, dynamic> json,
) => _PronunciationAssessmentResultData(
  privPronJson: PronunciationAssessmentPriv.fromJson(
    json['privPronJson'] as Map<String, dynamic>,
  ),
);

Map<String, dynamic> _$PronunciationAssessmentResultDataToJson(
  _PronunciationAssessmentResultData instance,
) => <String, dynamic>{'privPronJson': instance.privPronJson};

_PronunciationAssessmentPriv _$PronunciationAssessmentPrivFromJson(
  Map<String, dynamic> json,
) => _PronunciationAssessmentPriv(
  confidence: (json['Confidence'] as num).toDouble(),
  lexical: json['Lexical'] as String,
  itn: json['ITN'] as String,
  maskedItn: json['MaskedITN'] as String,
  display: json['Display'] as String,
  pronunciationAssessment: PronunciationAssessmentValue.fromJson(
    json['PronunciationAssessment'] as Map<String, dynamic>,
  ),
  words: (json['Words'] as List<dynamic>)
      .map(
        (e) => PronunciationAssessmentWord.fromJson(e as Map<String, dynamic>),
      )
      .toList(),
);

Map<String, dynamic> _$PronunciationAssessmentPrivToJson(
  _PronunciationAssessmentPriv instance,
) => <String, dynamic>{
  'Confidence': instance.confidence,
  'Lexical': instance.lexical,
  'ITN': instance.itn,
  'MaskedITN': instance.maskedItn,
  'Display': instance.display,
  'PronunciationAssessment': instance.pronunciationAssessment.toJson(),
  'Words': instance.words.map((e) => e.toJson()).toList(),
};

_PronunciationAssessmentValue _$PronunciationAssessmentValueFromJson(
  Map<String, dynamic> json,
) => _PronunciationAssessmentValue(
  accuracyScore: (json['AccuracyScore'] as num).toDouble(),
  fluencyScore: (json['FluencyScore'] as num).toDouble(),
  prosodyScore: (json['ProsodyScore'] as num).toDouble(),
  completenessScore: (json['CompletenessScore'] as num).toDouble(),
  pronScore: (json['PronScore'] as num).toDouble(),
);

Map<String, dynamic> _$PronunciationAssessmentValueToJson(
  _PronunciationAssessmentValue instance,
) => <String, dynamic>{
  'AccuracyScore': instance.accuracyScore,
  'FluencyScore': instance.fluencyScore,
  'ProsodyScore': instance.prosodyScore,
  'CompletenessScore': instance.completenessScore,
  'PronScore': instance.pronScore,
};

_PronunciationAssessmentWord _$PronunciationAssessmentWordFromJson(
  Map<String, dynamic> json,
) => _PronunciationAssessmentWord(
  offset: (json['Offset'] as num?)?.toInt(),
  pronunciationAssessment: WordPronunciationAssessment.fromJson(
    json['PronunciationAssessment'] as Map<String, dynamic>,
  ),
  duration: (json['Duration'] as num?)?.toInt(),
  syllables: (json['Syllables'] as List<dynamic>?)
      ?.map((e) => Syllable.fromJson(e as Map<String, dynamic>))
      .toList(),
  word: json['Word'] as String,
  phonemes: (json['Phonemes'] as List<dynamic>)
      .map(
        (e) =>
            PronunciationAssessmentPhoneme.fromJson(e as Map<String, dynamic>),
      )
      .toList(),
);

Map<String, dynamic> _$PronunciationAssessmentWordToJson(
  _PronunciationAssessmentWord instance,
) => <String, dynamic>{
  'Offset': instance.offset,
  'PronunciationAssessment': instance.pronunciationAssessment,
  'Duration': instance.duration,
  'Syllables': instance.syllables,
  'Word': instance.word,
  'Phonemes': instance.phonemes,
};

_PronunciationAssessmentPhoneme _$PronunciationAssessmentPhonemeFromJson(
  Map<String, dynamic> json,
) => _PronunciationAssessmentPhoneme(
  phoneme: json['Phoneme'] as String,
  offset: (json['Offset'] as num?)?.toInt(),
  pronunciationAssessment: PhonemePronunciationAssessment.fromJson(
    json['PronunciationAssessment'] as Map<String, dynamic>,
  ),
  duration: (json['Duration'] as num?)?.toInt(),
);

Map<String, dynamic> _$PronunciationAssessmentPhonemeToJson(
  _PronunciationAssessmentPhoneme instance,
) => <String, dynamic>{
  'Phoneme': instance.phoneme,
  'Offset': instance.offset,
  'PronunciationAssessment': instance.pronunciationAssessment,
  'Duration': instance.duration,
};

_PhonemePronunciationAssessment _$PhonemePronunciationAssessmentFromJson(
  Map<String, dynamic> json,
) => _PhonemePronunciationAssessment(
  accuracyScore: (json['AccuracyScore'] as num).toDouble(),
);

Map<String, dynamic> _$PhonemePronunciationAssessmentToJson(
  _PhonemePronunciationAssessment instance,
) => <String, dynamic>{'AccuracyScore': instance.accuracyScore};

_WordPronunciationAssessment _$WordPronunciationAssessmentFromJson(
  Map<String, dynamic> json,
) => _WordPronunciationAssessment(
  errorType: json['ErrorType'] as String,
  accuracyScore: (json['AccuracyScore'] as num?)?.toDouble(),
  feedback: json['Feedback'] == null
      ? null
      : Feedback.fromJson(json['Feedback'] as Map<String, dynamic>),
);

Map<String, dynamic> _$WordPronunciationAssessmentToJson(
  _WordPronunciationAssessment instance,
) => <String, dynamic>{
  'ErrorType': instance.errorType,
  'AccuracyScore': instance.accuracyScore,
  'Feedback': instance.feedback,
};

_Feedback _$FeedbackFromJson(Map<String, dynamic> json) => _Feedback(
  prosody: Prosody.fromJson(json['Prosody'] as Map<String, dynamic>),
);

Map<String, dynamic> _$FeedbackToJson(_Feedback instance) => <String, dynamic>{
  'Prosody': instance.prosody,
};

_Prosody _$ProsodyFromJson(Map<String, dynamic> json) => _Prosody(
  prosodyBreak: Break.fromJson(json['Break'] as Map<String, dynamic>),
  intonation: Intonation.fromJson(json['Intonation'] as Map<String, dynamic>),
);

Map<String, dynamic> _$ProsodyToJson(_Prosody instance) => <String, dynamic>{
  'Break': instance.prosodyBreak,
  'Intonation': instance.intonation,
};

_Intonation _$IntonationFromJson(Map<String, dynamic> json) => _Intonation(
  errorTypes: (json['ErrorTypes'] as List<dynamic>)
      .map((e) => e as String)
      .toList(),
  monotone: Monotone.fromJson(json['Monotone'] as Map<String, dynamic>),
);

Map<String, dynamic> _$IntonationToJson(_Intonation instance) =>
    <String, dynamic>{
      'ErrorTypes': instance.errorTypes,
      'Monotone': instance.monotone,
    };

_Monotone _$MonotoneFromJson(Map<String, dynamic> json) => _Monotone(
  syllablePitchDeltaConfidence: (json['SyllablePitchDeltaConfidence'] as num)
      .toDouble(),
);

Map<String, dynamic> _$MonotoneToJson(_Monotone instance) => <String, dynamic>{
  'SyllablePitchDeltaConfidence': instance.syllablePitchDeltaConfidence,
};

_Break _$BreakFromJson(Map<String, dynamic> json) => _Break(
  errorTypes: (json['ErrorTypes'] as List<dynamic>)
      .map((e) => e as String)
      .toList(),
  unexpectedBreak: json['UnexpectedBreak'] == null
      ? null
      : UnexpectedBreak.fromJson(
          json['UnexpectedBreak'] as Map<String, dynamic>,
        ),
  missingBreak: json['MissingBreak'] == null
      ? null
      : MissingBreak.fromJson(json['MissingBreak'] as Map<String, dynamic>),
  breakLength: (json['BreakLength'] as num).toInt(),
);

Map<String, dynamic> _$BreakToJson(_Break instance) => <String, dynamic>{
  'ErrorTypes': instance.errorTypes,
  'UnexpectedBreak': instance.unexpectedBreak,
  'MissingBreak': instance.missingBreak,
  'BreakLength': instance.breakLength,
};

_UnexpectedBreak _$UnexpectedBreakFromJson(Map<String, dynamic> json) =>
    _UnexpectedBreak(confidence: (json['Confidence'] as num).toDouble());

Map<String, dynamic> _$UnexpectedBreakToJson(_UnexpectedBreak instance) =>
    <String, dynamic>{'Confidence': instance.confidence};

_MissingBreak _$MissingBreakFromJson(Map<String, dynamic> json) =>
    _MissingBreak(confidence: (json['Confidence'] as num).toDouble());

Map<String, dynamic> _$MissingBreakToJson(_MissingBreak instance) =>
    <String, dynamic>{'Confidence': instance.confidence};

_Syllable _$SyllableFromJson(Map<String, dynamic> json) => _Syllable(
  offset: (json['Offset'] as num?)?.toInt(),
  pronunciationAssessment: PhonemePronunciationAssessment.fromJson(
    json['PronunciationAssessment'] as Map<String, dynamic>,
  ),
  grapheme: json['Grapheme'] as String?,
  syllable: json['Syllable'] as String?,
  duration: (json['Duration'] as num?)?.toInt(),
);

Map<String, dynamic> _$SyllableToJson(_Syllable instance) => <String, dynamic>{
  'Offset': instance.offset,
  'PronunciationAssessment': instance.pronunciationAssessment,
  'Grapheme': instance.grapheme,
  'Syllable': instance.syllable,
  'Duration': instance.duration,
};
