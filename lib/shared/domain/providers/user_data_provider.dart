import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:selfeng/shared/domain/models/user-data/user_data.dart'
    as user_model;

part 'user_data_provider.g.dart';

// Notifier for User data using the new Riverpod 3.0 API
@riverpod
class UserDataNotifier extends _$UserDataNotifier {
  @override
  user_model.UserData? build() {
    return null; // Initial state is null (no user)
  }

  // Method to update user data
  void setUser(user_model.UserData user) {
    state = user;
  }

  // Method to clear user data
  void clearUser() {
    state = null;
  }
}
