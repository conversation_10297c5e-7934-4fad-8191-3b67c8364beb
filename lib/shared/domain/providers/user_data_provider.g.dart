// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_data_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, type=warning

@ProviderFor(UserDataNotifier)
const userDataProvider = UserDataNotifierProvider._();

final class UserDataNotifierProvider
    extends $NotifierProvider<UserDataNotifier, user_model.UserData?> {
  const UserDataNotifierProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'userDataProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$userDataNotifierHash();

  @$internal
  @override
  UserDataNotifier create() => UserDataNotifier();

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(user_model.UserData? value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<user_model.UserData?>(value),
    );
  }
}

String _$userDataNotifierHash() => r'2ab733eaf81af5269196c4c332791722f271d9a5';

abstract class _$UserDataNotifier extends $Notifier<user_model.UserData?> {
  user_model.UserData? build();
  @$mustCallSuper
  @override
  void runBuild() {
    final created = build();
    final ref = this.ref as $Ref<user_model.UserData?, user_model.UserData?>;
    final element =
        ref.element
            as $ClassProviderElement<
              AnyNotifier<user_model.UserData?, user_model.UserData?>,
              user_model.UserData?,
              Object?,
              Object?
            >;
    element.handleValue(ref, created);
  }
}
