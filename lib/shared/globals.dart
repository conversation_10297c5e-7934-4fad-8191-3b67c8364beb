// ignore_for_file: constant_identifier_names

import 'dart:io';

final kTestMode = Platform.environment.containsKey('FLUTTER_TEST');
const int PRODUCTS_PER_PAGE = 20;
const String USER_LOCAL_STORAGE_KEY = 'user';
const String Locale_LOCAL_STORAGE_KEY = 'persistentLocale';
const String APP_THEME_STORAGE_KEY = 'AppTheme';
const String GOOGLE_SIGNIN_V7_MIGRATION_KEY = 'google_signin_v7_migrated';

////////////////////////////////Assets////////////////////////////////
const assetImageIcon = 'assets/images/icons';
const assetImageOnboarding = 'assets/images/onboarding';
const assetImageLanguage = 'assets/images/language';
const assetImageSetting = 'assets/images/setting';
const assetImageDashboard = 'assets/images/dashboard';
const assetImageQuestionnaire = 'assets/images/questionnaire';
const assetImageDiagnosticTest = 'assets/images/diagnostic_test';
const assetImageMainLesson = 'assets/images/main_lesson';
const assetImageTOC = 'assets/images/toc';
const assetImageGames = 'assets/images/games';
