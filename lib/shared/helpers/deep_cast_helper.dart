Map<String, dynamic> deepCast(Map<Object?, Object?> source) {
  return source.map((key, value) {
    if (value is Map) {
      return MapEntry(
        key.toString(),
        deepCast(Map<Object?, Object?>.from(value)),
      );
    } else if (value is List) {
      return MapEntry(
        key.toString(),
        value
            .map(
              (item) => item is Map
                  ? deepCast(Map<Object?, Object?>.from(item))
                  : item,
            )
            .toList(),
      );
    } else {
      return MapEntry(key.toString(), value);
    }
  });
}
