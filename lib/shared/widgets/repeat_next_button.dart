import 'package:flutter/material.dart';
import 'package:selfeng/shared/localization/app_localizations_context.dart';

class RepeatNextButton extends StatelessWidget {
  const RepeatNextButton({
    super.key,
    required this.onTapRepeat,
    required this.onTapNext,
    this.leftTitle,
    this.leftActive = true,
    this.rightTitle,
    this.rightActive = true,
    this.removeMargin = false,
  });

  final VoidCallback onTapRepeat;
  final VoidCallback onTapNext;
  final String? leftTitle;
  final String? rightTitle;
  final bool leftActive;
  final bool rightActive;
  final bool removeMargin;

  @override
  Widget build(BuildContext context) {
    final ThemeData theme = Theme.of(context);
    final ColorScheme colorScheme = theme.colorScheme;

    const double buttonHeight = 56.0;
    const double borderRadiusValue = 14.0;
    const double borderWidth = 0.4;

    final BorderRadius leftButtonRadius = const BorderRadius.only(
      topLeft: Radius.circular(borderRadiusValue),
      bottomLeft: Radius.circular(borderRadiusValue),
    );
    final BorderRadius rightButtonRadius = const BorderRadius.only(
      topRight: Radius.circular(borderRadiusValue),
      bottomRight: Radius.circular(borderRadiusValue),
    );

    // Style for the button text
    final TextStyle? buttonTextStyleBase = theme.textTheme.titleMedium
        ?.copyWith(fontWeight: FontWeight.w600);

    return Align(
      alignment: Alignment.bottomCenter,
      child: Container(
        margin: removeMargin
            ? EdgeInsets.zero
            : const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 24,
              ), // Reduced vertical margin slightly
        decoration: BoxDecoration(
          // Optional: Add a subtle shadow to the whole component
          borderRadius: BorderRadius.circular(borderRadiusValue),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: .08),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: ClipRRect(
          // ClipRRect to ensure the shadow respects the border radius
          borderRadius: BorderRadius.circular(borderRadiusValue),
          child: Row(
            children: [
              // --- REPEAT BUTTON (LEFT) ---
              Expanded(
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: leftButtonRadius,
                    border: Border.all(
                      color: leftActive
                          ? colorScheme.primary
                          : colorScheme.onSurface.withValues(alpha: .3),
                      width: borderWidth,
                    ),
                    color: leftActive
                        ? colorScheme
                              .surface // Typically white or light grey
                        : colorScheme.onSurface.withValues(alpha: .05),
                  ),
                  child: MaterialButton(
                    minWidth: double.infinity,
                    height: buttonHeight,
                    onPressed: leftActive ? onTapRepeat : null,
                    shape: RoundedRectangleBorder(
                      borderRadius: leftButtonRadius,
                    ),
                    color:
                        Colors.transparent, // Let Container handle background
                    elevation: 0,
                    highlightElevation: 0,
                    focusElevation: 0,
                    hoverElevation: 0,
                    splashColor: colorScheme.primary.withValues(alpha: .12),
                    highlightColor: colorScheme.primary.withValues(alpha: .06),
                    disabledColor: Colors.transparent,
                    child: Text(
                      softWrap: true,
                      leftTitle ?? context.loc.repeat,
                      style: buttonTextStyleBase?.copyWith(
                        color: leftActive
                            ? colorScheme.primary
                            : colorScheme.onSurface.withValues(alpha: .5),
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
              ),

              // --- NEXT BUTTON (RIGHT) ---
              Expanded(
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: rightButtonRadius,
                    color: rightActive
                        ? Color(0xffC00017) // Gradient handles color
                        : colorScheme.onSurface.withValues(
                            alpha: 0.12,
                          ), // Disabled background
                    // Optional: if you want a border even on the gradient button
                    border: Border.all(
                      color: rightActive
                          ? Colors
                                .transparent // Or a slightly darker shade of primary
                          : colorScheme.onSurface.withValues(alpha: .3),
                      width: borderWidth,
                    ),
                  ),
                  child: MaterialButton(
                    minWidth: double.infinity,
                    height: buttonHeight,
                    onPressed: rightActive ? onTapNext : null,
                    shape: RoundedRectangleBorder(
                      borderRadius: rightButtonRadius,
                    ),
                    color:
                        Colors.transparent, // Let Container handle background
                    elevation: 0,
                    highlightElevation: 0,
                    focusElevation: 0,
                    hoverElevation: 0,
                    splashColor: rightActive
                        ? Colors.white.withValues(alpha: .2)
                        : colorScheme.primary.withValues(alpha: .12),
                    highlightColor: rightActive
                        ? Colors.white.withValues(alpha: .1)
                        : colorScheme.primary.withValues(alpha: .06),
                    disabledColor: Colors.transparent,
                    child: Text(
                      softWrap: true,
                      rightTitle ?? context.loc.next,
                      style: buttonTextStyleBase?.copyWith(
                        color: rightActive
                            ? colorScheme
                                  .onPrimary // Text color on primary background (usually white)
                            : colorScheme.onSurface.withValues(alpha: .5),
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
