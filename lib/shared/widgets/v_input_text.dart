// ignore_for_file: prefer_typing_uninitialized_variables

import 'package:flutter/material.dart';
import 'package:selfeng/shared/localization/app_localizations_context.dart';

class VInputText extends StatelessWidget {
  const VInputText(
    this.hint, {
    super.key,
    this.keyboardType,
    this.readOnly = false,
    this.controller,
    this.validator,
    this.maxLines = 1,
    this.onSaved,
    this.onTap,
    this.onEditingComplete,
    this.initialValue,
    this.hintCustom = false,
    this.enabled = true,
    this.isValid = true,
    this.isNumber = false,
    this.obscureText = false,
    this.stayLabel = false,
    this.onChanged,
    this.capitalization = TextCapitalization.sentences,
    this.suffixIcon,
    this.suffixText,
    this.suffixStyle,
    this.prefixIcon,
    this.prefixText,
    this.prefixStyle,
    this.helperText,
    this.helperStyle,
  });

  final TextInputType? keyboardType;
  final String hint;
  final TextEditingController? controller;
  final TextCapitalization capitalization;
  final validator;
  final maxLines;
  final onSaved;
  final onChanged;
  final onTap;
  final onEditingComplete;
  final initialValue;
  final suffixIcon;
  final suffixText;
  final helperText;
  final helperStyle;
  final suffixStyle;
  final prefixIcon;
  final prefixText;
  final prefixStyle;
  final bool hintCustom;
  final bool enabled;
  final bool readOnly;
  final bool isValid;
  final bool isNumber;
  final bool obscureText;
  final bool stayLabel;

  @override
  Widget build(BuildContext context) {
    return stayLabel
        ? Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Text(hint, style: Theme.of(context).textTheme.titleMedium),
              const SizedBox(height: 7),
              _textInput(context),
            ],
          )
        : _textInput(context);
  }

  Widget _textInput(BuildContext context) => TextFormField(
    enabled: enabled,
    style: Theme.of(context).textTheme.bodyMedium,
    maxLines: maxLines,
    initialValue: initialValue,
    controller: controller,
    obscureText: obscureText,
    decoration: InputDecoration(
      fillColor: Colors.white,
      isDense: true,
      contentPadding: const EdgeInsets.symmetric(vertical: 18, horizontal: 16),
      filled: true,
      // label: Text(getTranslated(context, 'email'),),
      labelText: stayLabel ? null : hint,
      labelStyle: Theme.of(
        context,
      ).textTheme.labelLarge?.copyWith(color: const Color(0xffB4A9A7)),
      hintText: hint,
      hintStyle: Theme.of(
        context,
      ).textTheme.labelLarge?.copyWith(color: const Color(0xffB4A9A7)),
      focusedBorder: OutlineInputBorder(
        borderSide: const BorderSide(color: Color(0xffE82329)),
        borderRadius: BorderRadius.circular(8),
      ),
      border: OutlineInputBorder(
        borderSide: const BorderSide(color: Colors.black12),
        borderRadius: BorderRadius.circular(8),
      ),
      enabledBorder: OutlineInputBorder(
        borderSide: const BorderSide(color: Colors.black12),
        borderRadius: BorderRadius.circular(8),
      ),
      helperText: helperText,
      helperStyle: helperStyle,
      suffixIcon: suffixIcon,
      suffixText: suffixText,
      suffixStyle: suffixStyle,
      prefixIcon: prefixIcon,
      prefixText: prefixText,
      prefixStyle: prefixStyle,
    ),
    keyboardType: isNumber
        ? TextInputType.number
        : keyboardType ?? TextInputType.text,
    textCapitalization: capitalization,
    validator: isValid
        ? validator ??
              (val) => (val!.isEmpty
                  ? context.loc.please_type
                  : isNumber
                  ? int.tryParse(val) == null
                        ? double.tryParse(val) == null
                              ? context.loc.please_type_number
                              : null
                        : null
                  : null)
        : null,
    onSaved: onSaved,
    onChanged: onChanged,
    onTap: onTap,
    readOnly: readOnly,
    onEditingComplete: onEditingComplete,
  );
}
