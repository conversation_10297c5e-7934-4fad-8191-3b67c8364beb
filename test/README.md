# Testing Guidelines

This document provides comprehensive testing guidelines for the SelfEng Flutter mobile application.

## Testing Framework

This project uses the following testing technologies:

- **Flutter Test SDK**: Core testing framework for unit and widget tests
- **Mocktail**: Mocking library for creating test doubles
- **Fake Cloud Firestore**: In-memory Firestore implementation for testing
- **HTTP Mock Adapter**: HTTP response mocking for API tests
- **Riverpod**: State management testing utilities

## Quick Start

### Running Tests

```bash
# Run all tests
flutter test

# Run specific test file
flutter test test/infrastructure_test.dart

# Run tests by pattern
flutter test --name "UserService"

# Run tests with coverage
flutter test --coverage

# Run tests in specific directory
flutter test test/services/

# Generate and view HTML coverage report
flutter test --coverage
genhtml coverage/lcov.info -o coverage/html
open coverage/html/index.html

# Run tests with verbose output
flutter test --reporter expanded
```

### Test Organization

```
test/
├── features/           # Feature-specific tests
├── services/           # Service layer tests
├── shared/             # Shared domain tests
├── helpers/            # Testing utilities
├── mocks/              # Centralized mock objects
└── examples/           # Reference implementations
```

## Testing Standards

### Test Types & Naming
- **Unit Tests**: `test/[layer]/[feature]/[class_name]_test.dart`
- **Widget Tests**: `test/features/[feature]/widgets/[widget_name]_test.dart`
- **Service Tests**: `test/services/[service_name]/[service_name]_test.dart`

### Coverage Requirements
- **Overall**: 80% minimum
- **Critical Components**: 90% (auth, data models, core logic)
- **UI Components**: 70%

### Conventions
- Test cases start with "should"
- Mock variables prefixed with `mock`
- Use `group()` for related tests
- Use `setUp()`/`tearDown()` for initialization

## Mock System

### Centralized Mocks
- **`test/mocks/firebase_mocks.dart`**: Firebase service mocks
- **`test/mocks/service_mocks.dart`**: Service layer mocks
- **`test/mocks/repository_mocks.dart`**: Repository layer mocks
- **`test/helpers/mock_factories.dart`**: Mock creation utilities

This prevents errors like: “The class 'Query' shouldn't be extended, mixed in, or implemented because it's sealed.”

### Firebase Testing
Use `fake_cloud_firestore` for Firestore testing:

```dart
import 'package:fake_cloud_firestore/fake_cloud_firestore.dart';

void main() {
  late FakeFirebaseFirestore fakeFirestore;

  setUp(() => fakeFirestore = FakeFirebaseFirestore());

  test('should save user data', () async {
    await fakeFirestore.collection('users').doc('123').set({'name': 'Test User'});
    final doc = await fakeFirestore.collection('users').doc('123').get();
    expect(doc.data()?['name'], 'Test User');
  });
}
```

### Using Mock Factories
```dart
import 'package:selfeng/test/helpers/mock_factories.dart';

void main() {
  late MockFirebaseAuth mockAuth;

  setUp(() {
    mockAuth = MockFactories.createMockFirebaseAuth();
    MockFactories.setupFirebaseAuthMocks(mockAuth, mockUser);
  });
}
```

## Writing Tests

### Basic Test Structure (AAA Pattern)

```dart
void main() {
  group('UserService Tests', () {
    late UserService userService;
    late MockUserRepository mockRepository;

    setUp(() {
      mockRepository = MockFactories.createMockUserRepository();
      userService = UserService(mockRepository);
    });

    test('should return user data when repository succeeds', () async {
      // Arrange
      final expectedUser = TestDataBuilders.userDataBuilder().build();
      when(() => mockRepository.getUser('123'))
          .thenAnswer((_) async => Right(expectedUser));

      // Act
      final result = await userService.getUser('123');

      // Assert
      expect(result.isRight(), isTrue);
      verify(() => mockRepository.getUser('123')).called(1);
    });
  });
}
```

### Widget Testing

```dart
testWidgets('should display user name', (tester) async {
  final mockUser = TestDataBuilders.userDataBuilder().withDisplayName('John').build();

  await tester.pumpWidget(TestHelpers.createTestWidget(
    child: UserProfileWidget(),
    overrides: [userDataProvider.overrideWithValue(mockUser)],
  ));

  expect(find.text('John'), findsOneWidget);
});
```

### Provider Testing

```dart
test('should load user data successfully', () async {
  final container = ProviderContainer(
    overrides: [userRepositoryProvider.overrideWithValue(mockUserRepository)],
  );

  when(() => mockUserRepository.getUser('123')).thenAnswer((_) async => Right(testUser));

  final controller = container.read(userControllerProvider.notifier);
  await controller.loadUser('123');

  expect(container.read(userControllerProvider), AsyncData(testUser));
  container.dispose();
});
```

## Testing Utilities

### Test Data Builders
```dart
final userData = TestDataBuilders.userDataBuilder()
    .withEmail('<EMAIL>')
    .withCompletedTest()
    .build();
```

### Test Helpers
```dart
// Create test widget with providers
TestHelpers.createTestWidget(child: MyWidget(), overrides: [mockProvider.overrideWithValue(mockValue)]);

// Wait for async operations
await TestHelpers.waitFor(() => condition);
```

## Best Practices

### Core Principles
- Use centralized mock factories from `test/helpers/mock_factories.dart`
- Follow AAA pattern: Arrange, Act, Assert
- Use descriptive test names starting with "should"
- Verify important method calls with `verify()`

### Error Testing
```dart
test('should handle network errors gracefully', () async {
  when(() => mockRepository.getData()).thenAnswer((_) async => const Left(NetworkException()));
  final result = await service.loadData();
  expect(result.isLeft(), isTrue);
});
```

### Stream Testing
```dart
test('should handle stream data correctly', () async {
  final controller = StreamController<String>();
  when(() => mockRepository.watchData()).thenAnswer((_) => controller.stream);

  final events = <String>[];
  final subscription = service.watchData().listen(events.add);
  controller.add('test data');
  await Future.delayed(Duration(milliseconds: 10));

  expect(events, contains('test data'));
  await subscription.cancel();
  await controller.close();
});
```

## Common Testing Patterns

### JSON Serialization
```dart
test('should serialize and deserialize correctly', () {
  final original = TestDataBuilders.userDataBuilder().build();
  final deserialized = UserData.fromJson(original.toJson());
  expect(deserialized, equals(original));
});
```

### Either Pattern
```dart
test('should handle success case with Either', () {
  final result = Either<AppException, String>.right('success');
  final output = result.fold((error) => 'Error: ${error.message}', (success) => 'Success: $success');
  expect(output, equals('Success: success'));
});
```

### Dependency Injection
```dart
test('should create controller with test constructor', () {
  final controller = AuthController.test(userRepository: mockUserRepository, authRepository: mockAuthRepository);
  expect(controller, isA<AuthController>());
});
```

## Project-Specific Guidelines

### Platform & Architecture
- **Mobile-only**: iOS and Android (no web/desktop support)
- **State Management**: Riverpod with provider overrides for testing
- **Error Handling**: Either pattern for success/failure cases
- **Firebase**: Use `fake_cloud_firestore` instead of mocking sealed classes

### Riverpod Testing Pattern
```dart
test('should update provider state correctly', () async {
  final container = ProviderContainer(overrides: [repositoryProvider.overrideWithValue(mockRepository)]);
  final notifier = container.read(dataProvider.notifier);
  await notifier.loadData();
  expect(container.read(dataProvider), isA<AsyncData>());
  container.dispose();
});
```

### Either Pattern Testing
```dart
test('should return Left on error', () async {
  when(() => mockRepository.getData()).thenAnswer((_) async => Left(NetworkException()));
  final result = await service.getData();
  expect(result.isLeft(), isTrue);
  result.fold((error) => expect(error, isA<NetworkException>()), (data) => fail('Expected error'));
});
```

## Quick Reference

### Common Test Patterns Cheat Sheet

```dart
// Basic test structure
test('should do something', () async {
  // Arrange
  when(() => mockRepo.getData()).thenAnswer((_) async => Right(data));

  // Act
  final result = await service.getData();

  // Assert
  expect(result.isRight(), isTrue);
  verify(() => mockRepo.getData()).called(1);
});

// Widget test
testWidgets('should display text', (tester) async {
  await tester.pumpWidget(TestHelpers.createTestWidget(child: MyWidget()));
  expect(find.text('Hello'), findsOneWidget);
});

// Provider test
test('should update state', () async {
  final container = ProviderContainer(overrides: [repo.overrideWithValue(mock)]);
  final notifier = container.read(provider.notifier);
  await notifier.action();
  expect(container.read(provider), expectedState);
  container.dispose();
});
```

### Essential Imports

```dart
// Basic testing
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

// Widget testing
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Project utilities
import 'package:selfeng/test/helpers/mock_factories.dart';
import 'package:selfeng/test/helpers/test_data_builders.dart';
import 'package:selfeng/test/helpers/test_helpers.dart';

// Firebase testing
import 'package:fake_cloud_firestore/fake_cloud_firestore.dart';
```

---

## Reference Examples

For comprehensive testing examples, see:
- `test/examples/comprehensive_test_example.dart` - Complete testing patterns
- `test/infrastructure_test.dart` - Basic infrastructure verification
- `test/helpers/` - Testing utilities and mock factories

## Troubleshooting

### Common Issues

**Firestore Sealed Class Errors**
- Use `fake_cloud_firestore` instead of mocking Firestore classes
- Wrap Firestore operations in repository abstractions

**Provider Testing Issues**
- Always dispose ProviderContainer in tearDown()
- Use proper overrides for dependencies

**Mock Setup Problems**
- Use centralized mock factories from `test/helpers/mock_factories.dart`
- Verify mock interactions with `verify()` calls

### Performance Tips

- Keep tests focused and fast (aim for <100ms per test)
- Use setUp() and tearDown() for common initialization
- Reuse mock scenarios across similar tests
- Dispose resources properly to prevent memory leaks
- Avoid unnecessary async operations in tests
- Use `pumpAndSettle()` sparingly in widget tests

## CI/CD Integration

### GitHub Actions / CI Pipeline

```yaml
# Example test step in CI
- name: Run tests
  run: |
    flutter test --coverage --reporter=github

- name: Check coverage
  run: |
    genhtml coverage/lcov.info -o coverage/html
    # Add coverage threshold check here
```

### Pre-commit Hooks

Consider adding these commands to your pre-commit hooks:

```bash
# Run tests before commit
flutter test

# Check test coverage
flutter test --coverage
lcov --summary coverage/lcov.info
```

---

*This document serves as the definitive guide for testing practices in the SelfEng Flutter project. For additional examples and implementation details, refer to the test files in the respective directories.*

