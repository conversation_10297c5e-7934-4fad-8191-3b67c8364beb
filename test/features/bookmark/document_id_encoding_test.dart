import 'package:flutter_test/flutter_test.dart';

void main() {
  group('Document ID Encoding Tests', () {
    /// Helper method that mimics the _encodeContentPath method
    String encodeContentPath(String contentPath, {int? stage}) {
      final encodedPath = contentPath.replaceAll('/', '_');
      return stage != null ? '${encodedPath}_stage$stage' : encodedPath;
    }

    test('should encode contentPath with forward slashes correctly', () {
      // Test cases that would previously create nested subcollections
      const testCases = [
        'pronunciation/beginner/chapter1/part1/content1',
        'conversation/intermediate/chapter2/dialog1',
        'listening/advanced/chapter3/audio1',
        'speaking/beginner/chapter1/exercise1',
      ];

      final expectedResults = [
        'pronunciation_beginner_chapter1_part1_content1',
        'conversation_intermediate_chapter2_dialog1',
        'listening_advanced_chapter3_audio1',
        'speaking_beginner_chapter1_exercise1',
      ];

      for (int i = 0; i < testCases.length; i++) {
        final result = encodeContentPath(testCases[i]);
        expect(result, expectedResults[i]);

        // Verify no forward slashes remain
        expect(result.contains('/'), false);
      }
    });

    test('should handle speaking content with stages correctly', () {
      const contentPath = 'speaking/beginner/chapter1/exercise1';

      // Test different stages
      final stage1Result = encodeContentPath(contentPath, stage: 1);
      final stage2Result = encodeContentPath(contentPath, stage: 2);
      final stage3Result = encodeContentPath(contentPath, stage: 3);

      expect(stage1Result, 'speaking_beginner_chapter1_exercise1_stage1');
      expect(stage2Result, 'speaking_beginner_chapter1_exercise1_stage2');
      expect(stage3Result, 'speaking_beginner_chapter1_exercise1_stage3');

      // Verify no forward slashes remain
      expect(stage1Result.contains('/'), false);
      expect(stage2Result.contains('/'), false);
      expect(stage3Result.contains('/'), false);
    });

    test('should handle simple paths without forward slashes', () {
      const simplePath = 'simple_content';
      final result = encodeContentPath(simplePath);

      expect(result, 'simple_content');
    });

    test('should demonstrate the problem with original approach', () {
      // This shows what would happen with the original approach
      const contentPath = 'pronunciation/beginner/chapter1/part1/content1';

      // Original approach (problematic)
      final originalDocumentId =
          contentPath; // This creates nested subcollections!

      // New approach (fixed)
      final newDocumentId = encodeContentPath(contentPath);

      // Show the difference
      expect(
        originalDocumentId,
        'pronunciation/beginner/chapter1/part1/content1',
      );
      expect(newDocumentId, 'pronunciation_beginner_chapter1_part1_content1');

      // The original contains forward slashes (bad for Firestore document IDs)
      expect(originalDocumentId.contains('/'), true);

      // The new one doesn't (good for Firestore document IDs)
      expect(newDocumentId.contains('/'), false);
    });
  });
}
