import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:selfeng/features/certificate/presentation/providers/certificate_navigation_provider.dart';
import 'package:selfeng/features/certificate/domain/models/certificate.dart';
import 'package:selfeng/features/certificate/domain/models/certificate_level.dart';

void main() {
  group('CertificateNavigationController', () {
    late ProviderContainer container;

    setUp(() {
      container = ProviderContainer();
    });

    tearDown(() {
      container.dispose();
    });

    test('should initialize with null state', () {
      final controller = container.read(
        certificateNavigationControllerProvider,
      );
      expect(controller, isNull);
    });

    test('should set certificate data correctly', () {
      // Arrange
      final level = CertificateLevel(
        id: 'a1',
        name: 'A1',
        description: 'Beginner',
      );
      final certificates = [
        Certificate(
          id: 'cert1',
          title: 'Certificate 1',
          description: 'Test certificate 1',
          dateIssued: DateTime.now(),
          certificateUrl: 'https://example.com/cert1.pdf',
          level: level,
        ),
        Certificate(
          id: 'cert2',
          title: 'Certificate 2',
          description: 'Test certificate 2',
          dateIssued: DateTime.now(),
          certificateUrl: 'https://example.com/cert2.pdf',
          level: level,
        ),
      ];

      // Act
      container
          .read(certificateNavigationControllerProvider.notifier)
          .setCertificateData(level: level, certificates: certificates);

      // Assert
      final state = container.read(certificateNavigationControllerProvider);
      expect(state, isNotNull);
      expect(state!.level, equals(level));
      expect(state.certificates, equals(certificates));
      expect(state.selectedCertificateId, isNull);
    });

    test('should set certificate data with selected certificate ID', () {
      // Arrange
      final level = CertificateLevel(
        id: 'a1',
        name: 'A1',
        description: 'Beginner',
      );
      final certificates = [
        Certificate(
          id: 'cert1',
          title: 'Certificate 1',
          description: 'Test certificate 1',
          dateIssued: DateTime.now(),
          certificateUrl: 'https://example.com/cert1.pdf',
          level: level,
        ),
        Certificate(
          id: 'cert2',
          title: 'Certificate 2',
          description: 'Test certificate 2',
          dateIssued: DateTime.now(),
          certificateUrl: 'https://example.com/cert2.pdf',
          level: level,
        ),
      ];

      // Act
      container
          .read(certificateNavigationControllerProvider.notifier)
          .setCertificateData(
            level: level,
            certificates: certificates,
            selectedCertificateId: 'cert2',
          );

      // Assert
      final state = container.read(certificateNavigationControllerProvider);
      expect(state, isNotNull);
      expect(state!.level, equals(level));
      expect(state.certificates, equals(certificates));
      expect(state.selectedCertificateId, equals('cert2'));
    });

    test(
      'should update selected certificate ID while preserving other data',
      () {
        // Arrange
        final level = CertificateLevel(
          id: 'a1',
          name: 'A1',
          description: 'Beginner',
        );
        final certificates = [
          Certificate(
            id: 'cert1',
            title: 'Certificate 1',
            description: 'Test certificate 1',
            dateIssued: DateTime.now(),
            certificateUrl: 'https://example.com/cert1.pdf',
            level: level,
          ),
          Certificate(
            id: 'cert2',
            title: 'Certificate 2',
            description: 'Test certificate 2',
            dateIssued: DateTime.now(),
            certificateUrl: 'https://example.com/cert2.pdf',
            level: level,
          ),
        ];

        // Set initial data
        container
            .read(certificateNavigationControllerProvider.notifier)
            .setCertificateData(level: level, certificates: certificates);

        // Act - Update selected certificate
        container
            .read(certificateNavigationControllerProvider.notifier)
            .setSelectedCertificate('cert2');

        // Assert
        final state = container.read(certificateNavigationControllerProvider);
        expect(state, isNotNull);
        expect(state!.level, equals(level));
        expect(state.certificates, equals(certificates));
        expect(state.selectedCertificateId, equals('cert2'));
      },
    );

    test('should clear certificate data', () {
      // Arrange
      final level = CertificateLevel(
        id: 'a1',
        name: 'A1',
        description: 'Beginner',
      );
      final certificates = [
        Certificate(
          id: 'cert1',
          title: 'Certificate 1',
          description: 'Test certificate 1',
          dateIssued: DateTime.now(),
          certificateUrl: 'https://example.com/cert1.pdf',
          level: level,
        ),
      ];

      container
          .read(certificateNavigationControllerProvider.notifier)
          .setCertificateData(level: level, certificates: certificates);

      // Act
      container
          .read(certificateNavigationControllerProvider.notifier)
          .clearCertificateData();

      // Assert
      final state = container.read(certificateNavigationControllerProvider);
      expect(state, isNull);
    });

    test('should not update selected certificate if state is null', () {
      // Act - Try to update selected certificate when state is null
      container
          .read(certificateNavigationControllerProvider.notifier)
          .setSelectedCertificate('cert1');

      // Assert
      final state = container.read(certificateNavigationControllerProvider);
      expect(state, isNull);
    });

    group('getCertificateDataForLevel', () {
      test('should return certificate data when level name matches', () {
        // Arrange
        final level = CertificateLevel(
          id: 'a1',
          name: 'A1',
          description: 'Beginner',
        );
        final certificates = [
          Certificate(
            id: 'cert1',
            title: 'Certificate 1',
            description: 'Test certificate 1',
            dateIssued: DateTime.now(),
            certificateUrl: 'https://example.com/cert1.pdf',
            level: level,
          ),
        ];

        // Set initial data
        container
            .read(certificateNavigationControllerProvider.notifier)
            .setCertificateData(
              level: level,
              certificates: certificates,
              selectedCertificateId: 'cert1',
            );

        // Act
        final result = container
            .read(certificateNavigationControllerProvider.notifier)
            .getCertificateDataForLevel('A1');

        // Assert
        expect(result, isNotNull);
        expect(result!.level, equals(level));
        expect(result.certificates, equals(certificates));
        expect(result.selectedCertificateId, equals('cert1'));
      });

      test('should return null when level name does not match', () {
        // Arrange
        final level = CertificateLevel(
          id: 'a1',
          name: 'A1',
          description: 'Beginner',
        );
        final certificates = [
          Certificate(
            id: 'cert1',
            title: 'Certificate 1',
            description: 'Test certificate 1',
            dateIssued: DateTime.now(),
            certificateUrl: 'https://example.com/cert1.pdf',
            level: level,
          ),
        ];

        // Set initial data
        container
            .read(certificateNavigationControllerProvider.notifier)
            .setCertificateData(level: level, certificates: certificates);

        // Act
        final result = container
            .read(certificateNavigationControllerProvider.notifier)
            .getCertificateDataForLevel('B1');

        // Assert
        expect(result, isNull);
      });

      test('should return null when state is null', () {
        // Act
        final result = container
            .read(certificateNavigationControllerProvider.notifier)
            .getCertificateDataForLevel('A1');

        // Assert
        expect(result, isNull);
      });

      test(
        'should return null when state is null regardless of level name',
        () {
          // Act - Test with different level names
          final result1 = container
              .read(certificateNavigationControllerProvider.notifier)
              .getCertificateDataForLevel('A1');
          final result2 = container
              .read(certificateNavigationControllerProvider.notifier)
              .getCertificateDataForLevel('B1');
          final result3 = container
              .read(certificateNavigationControllerProvider.notifier)
              .getCertificateDataForLevel('C1');

          // Assert
          expect(result1, isNull);
          expect(result2, isNull);
          expect(result3, isNull);
        },
      );
    });

    group('CertificateNavigationData.copyWith', () {
      test('should create a copy with updated values', () {
        // Arrange
        final level1 = CertificateLevel(
          id: 'a1',
          name: 'A1',
          description: 'Beginner',
        );
        final level2 = CertificateLevel(
          id: 'b1',
          name: 'B1',
          description: 'Intermediate',
        );
        final certificates1 = [
          Certificate(
            id: 'cert1',
            title: 'Certificate 1',
            description: 'Test certificate 1',
            dateIssued: DateTime.now(),
            certificateUrl: 'https://example.com/cert1.pdf',
            level: level1,
          ),
        ];
        final certificates2 = [
          Certificate(
            id: 'cert2',
            title: 'Certificate 2',
            description: 'Test certificate 2',
            dateIssued: DateTime.now(),
            certificateUrl: 'https://example.com/cert2.pdf',
            level: level2,
          ),
        ];

        final originalData = CertificateNavigationData(
          level: level1,
          certificates: certificates1,
          selectedCertificateId: 'cert1',
        );

        // Act
        final copiedData = originalData.copyWith(
          level: level2,
          certificates: certificates2,
          selectedCertificateId: 'cert2',
        );

        // Assert
        expect(copiedData.level, equals(level2));
        expect(copiedData.certificates, equals(certificates2));
        expect(copiedData.selectedCertificateId, equals('cert2'));
      });

      test('should preserve original values when not provided in copyWith', () {
        // Arrange
        final level = CertificateLevel(
          id: 'a1',
          name: 'A1',
          description: 'Beginner',
        );
        final certificates = [
          Certificate(
            id: 'cert1',
            title: 'Certificate 1',
            description: 'Test certificate 1',
            dateIssued: DateTime.now(),
            certificateUrl: 'https://example.com/cert1.pdf',
            level: level,
          ),
        ];

        final originalData = CertificateNavigationData(
          level: level,
          certificates: certificates,
          selectedCertificateId: 'cert1',
        );

        // Act
        final copiedData = originalData.copyWith();

        // Assert
        expect(copiedData.level, equals(level));
        expect(copiedData.certificates, equals(certificates));
        expect(copiedData.selectedCertificateId, equals('cert1'));
      });

      test('should allow partial updates with copyWith', () {
        // Arrange
        final level1 = CertificateLevel(
          id: 'a1',
          name: 'A1',
          description: 'Beginner',
        );
        final level2 = CertificateLevel(
          id: 'b1',
          name: 'B1',
          description: 'Intermediate',
        );
        final certificates = [
          Certificate(
            id: 'cert1',
            title: 'Certificate 1',
            description: 'Test certificate 1',
            dateIssued: DateTime.now(),
            certificateUrl: 'https://example.com/cert1.pdf',
            level: level1,
          ),
        ];

        final originalData = CertificateNavigationData(
          level: level1,
          certificates: certificates,
          selectedCertificateId: 'cert1',
        );

        // Act
        final copiedData = originalData.copyWith(
          level: level2,
          // certificates and selectedCertificateId not provided
        );

        // Assert
        expect(copiedData.level, equals(level2));
        expect(copiedData.certificates, equals(certificates));
        expect(copiedData.selectedCertificateId, equals('cert1'));
      });
    });

    group('Provider state management', () {
      test('should maintain state across multiple operations', () async {
        // Arrange
        final levelA1 = CertificateLevel(
          id: 'a1',
          name: 'A1',
          description: 'Beginner',
        );
        final levelB1 = CertificateLevel(
          id: 'b1',
          name: 'B1',
          description: 'Intermediate',
        );
        final certificatesA1 = [
          Certificate(
            id: 'cert1',
            title: 'Certificate 1',
            description: 'Test certificate 1',
            dateIssued: DateTime.now(),
            certificateUrl: 'https://example.com/cert1.pdf',
            level: levelA1,
          ),
        ];
        final certificatesB1 = [
          Certificate(
            id: 'cert2',
            title: 'Certificate 2',
            description: 'Test certificate 2',
            dateIssued: DateTime.now(),
            certificateUrl: 'https://example.com/cert2.pdf',
            level: levelB1,
          ),
        ];

        final notifier = container.read(
          certificateNavigationControllerProvider.notifier,
        );

        // Act & Assert - Set initial state
        notifier.setCertificateData(
          level: levelA1,
          certificates: certificatesA1,
          selectedCertificateId: 'cert1',
        );
        expect(
          container.read(certificateNavigationControllerProvider),
          isNotNull,
        );
        expect(
          container.read(certificateNavigationControllerProvider)!.level,
          equals(levelA1),
        );

        // Act & Assert - Update selected certificate
        notifier.setSelectedCertificate('cert1');
        expect(
          container
              .read(certificateNavigationControllerProvider)!
              .selectedCertificateId,
          equals('cert1'),
        );

        // Act & Assert - Clear state
        notifier.clearCertificateData();
        expect(container.read(certificateNavigationControllerProvider), isNull);

        // Act & Assert - Set new state
        notifier.setCertificateData(
          level: levelB1,
          certificates: certificatesB1,
        );
        expect(
          container.read(certificateNavigationControllerProvider),
          isNotNull,
        );
        expect(
          container.read(certificateNavigationControllerProvider)!.level,
          equals(levelB1),
        );
        expect(
          container
              .read(certificateNavigationControllerProvider)!
              .selectedCertificateId,
          isNull,
        );
      });

      test('should handle edge cases with empty certificates list', () {
        // Arrange
        final level = CertificateLevel(
          id: 'a1',
          name: 'A1',
          description: 'Beginner',
        );
        final emptyCertificates = <Certificate>[];

        // Act
        container
            .read(certificateNavigationControllerProvider.notifier)
            .setCertificateData(level: level, certificates: emptyCertificates);

        // Assert
        final state = container.read(certificateNavigationControllerProvider);
        expect(state, isNotNull);
        expect(state!.certificates, isEmpty);
        expect(state.certificates.length, equals(0));
      });

      test('should handle edge cases with null selectedCertificateId', () {
        // Arrange
        final level = CertificateLevel(
          id: 'a1',
          name: 'A1',
          description: 'Beginner',
        );
        final certificates = [
          Certificate(
            id: 'cert1',
            title: 'Certificate 1',
            description: 'Test certificate 1',
            dateIssued: DateTime.now(),
            certificateUrl: 'https://example.com/cert1.pdf',
            level: level,
          ),
        ];

        // Act
        container
            .read(certificateNavigationControllerProvider.notifier)
            .setCertificateData(
              level: level,
              certificates: certificates,
              selectedCertificateId: null,
            );

        // Assert
        final state = container.read(certificateNavigationControllerProvider);
        expect(state, isNotNull);
        expect(state!.selectedCertificateId, isNull);
      });
    });
  });
}
