import 'dart:convert';

import 'package:flutter_test/flutter_test.dart';
import 'package:selfeng/features/library/domain/models/level_info.dart';
import 'package:selfeng/shared/domain/models/level.dart';

void main() {
  group('LevelLocalizeContent', () {
    const tEn = 'English Content';
    const tId = 'Indonesian Content';
    final tLevelLocalizeContent = LevelLocalizeContent(en: tEn, id: tId);
    final tJson = {'en': tEn, 'id': tId};

    test('should create a valid LevelLocalizeContent object', () {
      // Assert
      expect(tLevelLocalizeContent.en, tEn);
      expect(tLevelLocalizeContent.id, tId);
    });

    test('should return correct content for "en" locale', () {
      // Act
      final result = tLevelLocalizeContent.getByLocale('en');
      // Assert
      expect(result, tEn);
    });

    test('should return correct content for "id" locale', () {
      // Act
      final result = tLevelLocalizeContent.getByLocale('id');
      // Assert
      expect(result, tId);
    });

    test('should return default content (en) for unknown locale', () {
      // Act
      final result = tLevelLocalizeContent.getByLocale('fr');
      // Assert
      expect(result, tEn);
    });

    group('Serialization', () {
      test('should serialize to a map', () {
        // Act
        final result = tLevelLocalizeContent.toJson();
        // Assert
        expect(result, tJson);
      });

      test('should deserialize from a map', () {
        // Act
        final result = LevelLocalizeContent.fromJson(tJson);
        // Assert
        expect(result, tLevelLocalizeContent);
      });

      test('should handle json encoding and decoding', () {
        // Arrange
        final jsonString = json.encode(tJson);
        // Act
        final decoded = json.decode(jsonString);
        final result = LevelLocalizeContent.fromJson(decoded);
        // Assert
        expect(result, tLevelLocalizeContent);
      });
    });
  });

  group('LevelInfo', () {
    final tLevelInfo = LevelInfo(
      level: Level.a1,
      image: 'path/to/image.png',
      title: LevelLocalizeContent(en: 'Beginner', id: 'Pemula'),
      description: LevelLocalizeContent(
        en: 'Beginner level description',
        id: 'Deskripsi level pemula',
      ),
      startChapter: 1,
      endChapter: 10,
    );

    final tJson = {
      'level': 'a1',
      'image': 'path/to/image.png',
      'title': {'en': 'Beginner', 'id': 'Pemula'},
      'description': {
        'en': 'Beginner level description',
        'id': 'Deskripsi level pemula',
      },
      'startChapter': 1,
      'endChapter': 10,
    };

    test('should create a valid LevelInfo object', () {
      // Assert
      expect(tLevelInfo.level, Level.a1);
      expect(tLevelInfo.image, 'path/to/image.png');
      expect(tLevelInfo.title.en, 'Beginner');
      expect(tLevelInfo.description.id, 'Deskripsi level pemula');
      expect(tLevelInfo.startChapter, 1);
      expect(tLevelInfo.endChapter, 10);
    });

    group('Serialization', () {
      test('should serialize to a map', () {
        // Act
        final result = tLevelInfo.toJson();
        // Assert
        expect(result['level'], 'a1');
        expect(result['image'], tJson['image']);
        expect(result['title'], tJson['title']);
        expect(result['description'], tJson['description']);
        expect(result['startChapter'], tJson['startChapter']);
        expect(result['endChapter'], tJson['endChapter']);
      });

      test('should deserialize from a map', () {
        // Act
        final result = LevelInfo.fromJson(tJson);
        // Assert
        expect(result, tLevelInfo);
      });

      test('should handle json encoding and decoding', () {
        // Arrange
        final jsonString = json.encode(tJson);
        // Act
        final decoded = json.decode(jsonString);
        final result = LevelInfo.fromJson(decoded);
        // Assert
        expect(result, tLevelInfo);
      });
    });
  });
}
