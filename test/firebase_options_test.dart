import 'package:flutter/foundation.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:selfeng/firebase_options.dart';
import 'package:selfeng/main/app_env.dart';

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();

  setUpAll(() async {
    // Initialize environment for testing
    await dotenv.load(fileName: '.env.dev');
    EnvInfo.initialize(AppEnvironment.DEV);
  });

  group('DefaultFirebaseOptions', () {
    group('currentPlatform getter', () {
      test('should return android options when platform is Android', () {
        // Arrange
        debugDefaultTargetPlatformOverride = TargetPlatform.android;

        // Act
        final options = DefaultFirebaseOptions.currentPlatform;

        // Assert
        expect(options, equals(DefaultFirebaseOptions.android));
        expect(
          options.apiKey,
          equals('AIzaSyCY9f2-jZfAlLtYbVG-NsMkzdVOT_SUKFE'),
        );
        expect(
          options.appId,
          equals('1:432462853701:android:94918fb171d7f45c8bd52c'),
        );
        expect(options.messagingSenderId, equals('432462853701'));
        expect(options.projectId, equals('selfeng-dev'));
        expect(options.storageBucket, equals('selfeng-dev.appspot.com'));

        // Cleanup
        debugDefaultTargetPlatformOverride = null;
      });

      test('should return ios options when platform is iOS', () {
        // Arrange
        debugDefaultTargetPlatformOverride = TargetPlatform.iOS;

        // Act
        final options = DefaultFirebaseOptions.currentPlatform;

        // Assert
        expect(options, equals(DefaultFirebaseOptions.ios));
        expect(
          options.apiKey,
          equals('AIzaSyA9s36vROrAkhGG4j1VW9THho5Md9LW07g'),
        );
        expect(
          options.appId,
          equals('1:432462853701:ios:b4075526b2b45bf88bd52c'),
        );
        expect(options.messagingSenderId, equals('432462853701'));
        expect(options.projectId, equals('selfeng-dev'));
        expect(options.storageBucket, equals('selfeng-dev.appspot.com'));
        expect(
          options.iosClientId,
          equals(
            '432462853701-r5h7rc0199tpses7d73ch5afqs11aq5o.apps.googleusercontent.com',
          ),
        );
        expect(options.iosBundleId, equals('com.example.selfeng'));

        // Cleanup
        debugDefaultTargetPlatformOverride = null;
      });

      test('should throw UnsupportedError for Fuchsia platform', () {
        // Arrange
        debugDefaultTargetPlatformOverride = TargetPlatform.fuchsia;

        // Act & Assert
        expect(
          () => DefaultFirebaseOptions.currentPlatform,
          throwsA(isA<UnsupportedError>()),
        );

        // Cleanup
        debugDefaultTargetPlatformOverride = null;
      });
    });

    group('Static FirebaseOptions configurations', () {
      test('android options should have correct configuration', () {
        // Act
        final options = DefaultFirebaseOptions.android;

        // Assert
        expect(
          options.apiKey,
          equals('AIzaSyCY9f2-jZfAlLtYbVG-NsMkzdVOT_SUKFE'),
        );
        expect(
          options.appId,
          equals('1:432462853701:android:94918fb171d7f45c8bd52c'),
        );
        expect(options.messagingSenderId, equals('432462853701'));
        expect(options.projectId, equals('selfeng-dev'));
        expect(options.storageBucket, equals('selfeng-dev.appspot.com'));
        expect(options.authDomain, isNull);
        expect(options.databaseURL, isNull);
        expect(options.trackingId, isNull);
        expect(options.deepLinkURLScheme, isNull);
        expect(options.androidClientId, isNull);
        expect(options.iosClientId, isNull);
        expect(options.iosBundleId, isNull);
        expect(options.appGroupId, isNull);
      });

      test('ios options should have correct configuration', () {
        // Act
        final options = DefaultFirebaseOptions.ios;

        // Assert
        expect(
          options.apiKey,
          equals('AIzaSyA9s36vROrAkhGG4j1VW9THho5Md9LW07g'),
        );
        expect(
          options.appId,
          equals('1:432462853701:ios:b4075526b2b45bf88bd52c'),
        );
        expect(options.messagingSenderId, equals('432462853701'));
        expect(options.projectId, equals('selfeng-dev'));
        expect(options.storageBucket, equals('selfeng-dev.appspot.com'));
        expect(
          options.iosClientId,
          equals(
            '432462853701-r5h7rc0199tpses7d73ch5afqs11aq5o.apps.googleusercontent.com',
          ),
        );
        expect(options.iosBundleId, equals('com.example.selfeng'));
        expect(options.authDomain, isNull);
        expect(options.databaseURL, isNull);
        expect(options.trackingId, isNull);
        expect(options.deepLinkURLScheme, isNull);
        expect(
          options.androidClientId,
          equals(
            '432462853701-kn3cb2nvm3q8nr7sjfatd2a2mc9mpu5e.apps.googleusercontent.com',
          ),
        );
        expect(options.appGroupId, isNull);
      });

      test('android and ios options should have different apiKeys', () {
        // Act
        final androidOptions = DefaultFirebaseOptions.android;
        final iosOptions = DefaultFirebaseOptions.ios;

        // Assert
        expect(androidOptions.apiKey, isNot(equals(iosOptions.apiKey)));
        expect(androidOptions.appId, isNot(equals(iosOptions.appId)));
      });

      test(
        'android and ios options should share same project configuration',
        () {
          // Act
          final androidOptions = DefaultFirebaseOptions.android;
          final iosOptions = DefaultFirebaseOptions.ios;

          // Assert
          expect(androidOptions.projectId, equals(iosOptions.projectId));
          expect(
            androidOptions.storageBucket,
            equals(iosOptions.storageBucket),
          );
          expect(
            androidOptions.messagingSenderId,
            equals(iosOptions.messagingSenderId),
          );
        },
      );
    });

    group('Platform support validation', () {
      test('should only support Android and iOS mobile platforms', () {
        // Arrange
        final supportedPlatforms = [TargetPlatform.android, TargetPlatform.iOS];
        final unsupportedPlatforms = [
          TargetPlatform.macOS,
          TargetPlatform.windows,
          TargetPlatform.linux,
          TargetPlatform.fuchsia,
        ];

        // Act & Assert - Supported mobile platforms
        for (final platform in supportedPlatforms) {
          debugDefaultTargetPlatformOverride = platform;
          expect(
            () => DefaultFirebaseOptions.currentPlatform,
            returnsNormally,
            reason: 'Platform $platform should be supported',
          );
          debugDefaultTargetPlatformOverride = null;
        }

        // Act & Assert - Unsupported platforms (including web)
        for (final platform in unsupportedPlatforms) {
          debugDefaultTargetPlatformOverride = platform;
          expect(
            () => DefaultFirebaseOptions.currentPlatform,
            throwsA(isA<UnsupportedError>()),
            reason: 'Platform $platform should not be supported',
          );
          debugDefaultTargetPlatformOverride = null;
        }
      });
    });

    group('Error handling and edge cases', () {
      test(
        'should throw UnsupportedError with correct message for unsupported platforms',
        () {
          // Arrange
          debugDefaultTargetPlatformOverride = TargetPlatform.fuchsia;

          // Act
          try {
            DefaultFirebaseOptions.currentPlatform;
            fail('Expected UnsupportedError to be thrown');
          } catch (e) {
            // Assert
            expect(e, isA<UnsupportedError>());
            expect(
              (e as UnsupportedError).message,
              equals(
                'DefaultFirebaseOptions are not supported for this platform.',
              ),
            );
          }

          // Cleanup
          debugDefaultTargetPlatformOverride = null;
        },
      );

      test('should handle platform override cleanup properly', () {
        // Arrange - Set platform override
        debugDefaultTargetPlatformOverride = TargetPlatform.android;

        // Act - Get current platform
        final options = DefaultFirebaseOptions.currentPlatform;

        // Assert - Should return android options
        expect(options, equals(DefaultFirebaseOptions.android));

        // Cleanup
        debugDefaultTargetPlatformOverride = null;

        // Verify cleanup worked (this test ensures no state pollution)
        expect(true, isTrue); // If we reach here, cleanup worked
      });

      test('should maintain consistent behavior across multiple calls', () {
        // Arrange
        debugDefaultTargetPlatformOverride = TargetPlatform.iOS;

        // Act - Call multiple times
        final options1 = DefaultFirebaseOptions.currentPlatform;
        final options2 = DefaultFirebaseOptions.currentPlatform;
        final options3 = DefaultFirebaseOptions.currentPlatform;

        // Assert - All calls should return same result
        expect(options1, equals(options2));
        expect(options2, equals(options3));
        expect(options1, equals(DefaultFirebaseOptions.ios));

        // Cleanup
        debugDefaultTargetPlatformOverride = null;
      });
    });

    group('Configuration validation', () {
      test('should have non-empty required fields for mobile platforms', () {
        // Act
        final androidOptions = DefaultFirebaseOptions.android;
        final iosOptions = DefaultFirebaseOptions.ios;

        // Assert - Mobile platforms should have required fields
        expect(androidOptions.apiKey, isNotEmpty);
        expect(androidOptions.appId, isNotEmpty);
        expect(androidOptions.projectId, isNotEmpty);
        expect(androidOptions.storageBucket, isNotEmpty);

        expect(iosOptions.apiKey, isNotEmpty);
        expect(iosOptions.appId, isNotEmpty);
        expect(iosOptions.projectId, isNotEmpty);
        expect(iosOptions.storageBucket, isNotEmpty);
      });

      test('should have platform-specific configurations', () {
        // Act
        final androidOptions = DefaultFirebaseOptions.android;
        final iosOptions = DefaultFirebaseOptions.ios;

        // Assert - iOS-specific fields
        expect(iosOptions.iosClientId, isNotNull);
        expect(iosOptions.iosBundleId, isNotNull);

        // Assert - Android-specific fields should be null for android platform
        expect(androidOptions.androidClientId, isNull);
        // iOS options do have androidClientId for cross-platform authentication
        expect(
          iosOptions.androidClientId,
          equals(
            '432462853701-kn3cb2nvm3q8nr7sjfatd2a2mc9mpu5e.apps.googleusercontent.com',
          ),
        );

        // Assert - Web-specific fields should be null for mobile platforms
        expect(androidOptions.authDomain, isNull);
        expect(androidOptions.measurementId, isNull);
        expect(iosOptions.authDomain, isNull);
        expect(iosOptions.measurementId, isNull);
      });
    });
  });
}
