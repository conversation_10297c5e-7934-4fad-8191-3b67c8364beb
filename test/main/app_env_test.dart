import 'package:flutter_test/flutter_test.dart';
import 'package:selfeng/main/app_env.dart';

void main() {
  tearDown(() {
    // Reset the environment to DEV after each test
    EnvInfo.initialize(AppEnvironment.DEV);
  });

  group('AppEnvironment Enum', () {
    test('should have correct enum values', () {
      expect(AppEnvironment.DEV.index, 0);
      expect(AppEnvironment.STAGING.index, 1);
      expect(AppEnvironment.PROD.index, 2);

      expect(AppEnvironment.values.length, 3);
      expect(AppEnvironment.values, contains(AppEnvironment.DEV));
      expect(AppEnvironment.values, contains(AppEnvironment.STAGING));
      expect(AppEnvironment.values, contains(AppEnvironment.PROD));
    });
  });

  group('EnvInfo Class', () {
    group('initialize method', () {
      test('should initialize with DEV environment', () {
        EnvInfo.initialize(AppEnvironment.DEV);

        expect(EnvInfo.environment, AppEnvironment.DEV);
      });

      test('should initialize with STAGING environment', () {
        EnvInfo.initialize(AppEnvironment.STAGING);

        expect(EnvInfo.environment, AppEnvironment.STAGING);
      });

      test('should initialize with PROD environment', () {
        EnvInfo.initialize(AppEnvironment.PROD);

        expect(EnvInfo.environment, AppEnvironment.PROD);
      });
    });

    group('environment getter', () {
      test('should return current environment', () {
        EnvInfo.initialize(AppEnvironment.STAGING);

        expect(EnvInfo.environment, AppEnvironment.STAGING);
      });
    });

    group('isProduction getter', () {
      test('should return false for DEV environment', () {
        EnvInfo.initialize(AppEnvironment.DEV);

        expect(EnvInfo.isProduction, isFalse);
      });

      test('should return false for STAGING environment', () {
        EnvInfo.initialize(AppEnvironment.STAGING);

        expect(EnvInfo.isProduction, isFalse);
      });

      test('should return true for PROD environment', () {
        EnvInfo.initialize(AppEnvironment.PROD);

        expect(EnvInfo.isProduction, isTrue);
      });
    });

    group('envName getter', () {
      test('should return correct env name for DEV', () {
        EnvInfo.initialize(AppEnvironment.DEV);

        expect(EnvInfo.envName, '.env.dev');
      });

      test('should return correct env name for STAGING', () {
        EnvInfo.initialize(AppEnvironment.STAGING);

        expect(EnvInfo.envName, '.env.stg');
      });

      test('should return correct env name for PROD', () {
        EnvInfo.initialize(AppEnvironment.PROD);

        expect(EnvInfo.envName, '.env.prod');
      });
    });
  });

  group('Environment Extension Properties', () {
    group('Environment mapping', () {
      test('should map DEV to .env.dev', () {
        EnvInfo.initialize(AppEnvironment.DEV);

        expect(EnvInfo.envName, '.env.dev');
      });

      test('should map STAGING to .env.stg', () {
        EnvInfo.initialize(AppEnvironment.STAGING);

        expect(EnvInfo.envName, '.env.stg');
      });

      test('should map PROD to .env.prod', () {
        EnvInfo.initialize(AppEnvironment.PROD);

        expect(EnvInfo.envName, '.env.prod');
      });
    });
  });

  group('Environment Variables Integration', () {
    test('should provide access to all configuration getters', () {
      EnvInfo.initialize(AppEnvironment.DEV);

      // Test that the class provides access to all expected configuration getters
      // Note: These getters depend on dotenv being properly initialized with .env files
      // In a real application, these would return actual values or fallback messages

      // Test that the getters exist and can be called (will throw if dotenv not initialized)
      try {
        // These will throw NotInitializedError if dotenv is not initialized
        // which is expected in unit tests without proper .env file setup
        EnvInfo.appName;
        EnvInfo.appUrl;
        EnvInfo.firebaseProjectId;
        EnvInfo.firebaseAuthDomain;
        EnvInfo.firebaseStorageBucket;
        EnvInfo.firebaseApiKeyWeb;
        EnvInfo.firebaseAppIdWeb;
        EnvInfo.firebaseMessagingSenderIdWeb;
        EnvInfo.firebaseMeasurementIdWeb;
        EnvInfo.firebaseApiKeyAndroid;
        EnvInfo.firebaseAppIdAndroid;
        EnvInfo.firebaseMessagingSenderIdAndroid;
        EnvInfo.firebaseApiKeyIos;
        EnvInfo.firebaseAppIdIos;
        EnvInfo.firebaseMessagingSenderIdIos;
        EnvInfo.firebaseAndroidClientId;
        EnvInfo.firebaseIosClientId;
        EnvInfo.firebaseIosBundleId;

        // If we reach here, dotenv is initialized (unexpected in unit tests)
        fail('Expected NotInitializedError to be thrown');
      } catch (e) {
        // Expected behavior in unit tests - dotenv not initialized
        expect(e, isA<Error>());
      }
    });
  });

  group('Environment Switching', () {
    test('should allow switching between environments', () {
      // Start with DEV
      EnvInfo.initialize(AppEnvironment.DEV);
      expect(EnvInfo.environment, AppEnvironment.DEV);
      expect(EnvInfo.isProduction, isFalse);
      expect(EnvInfo.envName, '.env.dev');

      // Switch to STAGING
      EnvInfo.initialize(AppEnvironment.STAGING);
      expect(EnvInfo.environment, AppEnvironment.STAGING);
      expect(EnvInfo.isProduction, isFalse);
      expect(EnvInfo.envName, '.env.stg');

      // Switch to PROD
      EnvInfo.initialize(AppEnvironment.PROD);
      expect(EnvInfo.environment, AppEnvironment.PROD);
      expect(EnvInfo.isProduction, isTrue);
      expect(EnvInfo.envName, '.env.prod');

      // Switch back to DEV
      EnvInfo.initialize(AppEnvironment.DEV);
      expect(EnvInfo.environment, AppEnvironment.DEV);
      expect(EnvInfo.isProduction, isFalse);
      expect(EnvInfo.envName, '.env.dev');
    });
  });

  group('Default Environment Behavior', () {
    test('should default to DEV environment initially', () {
      // The tearDown sets it to DEV, so this tests the default state
      expect(EnvInfo.environment, AppEnvironment.DEV);
      expect(EnvInfo.isProduction, isFalse);
      expect(EnvInfo.envName, '.env.dev');
    });
  });

  group('Environment Configuration Validation', () {
    test('should maintain environment state across getter calls', () {
      EnvInfo.initialize(AppEnvironment.PROD);

      // Multiple calls should return consistent results
      expect(EnvInfo.environment, AppEnvironment.PROD);
      expect(EnvInfo.environment, AppEnvironment.PROD);
      expect(EnvInfo.isProduction, isTrue);
      expect(EnvInfo.isProduction, isTrue);
      expect(EnvInfo.envName, '.env.prod');
      expect(EnvInfo.envName, '.env.prod');
    });
  });

  group('Integration Test Notes', () {
    test('dotenv integration requires manual testing', () {
      // NOTE: The following behaviors should be tested manually with actual .env files:
      // 1. When .env files exist with proper values:
      //    - appName should return the APP_TITLE value
      //    - appUrl should return the APP_URL value
      //    - All Firebase getters should return their respective values
      //
      // 2. When .env files are missing or values are not set:
      //    - All getters should return fallback messages like "APP_TITLE not defined"
      //
      // 3. Environment-specific .env files:
      //    - .env.dev should be loaded for DEV environment
      //    - .env.stg should be loaded for STAGING environment
      //    - .env.prod should be loaded for PROD environment

      // This test serves as documentation for manual testing requirements
      expect(true, isTrue); // Placeholder assertion
    });
  });
}
