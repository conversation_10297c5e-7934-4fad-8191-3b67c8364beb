import 'package:mocktail/mocktail.dart';
import 'package:selfeng/services/crashlytics_service/domain/repositories/crashlytics_repository.dart';
import 'package:selfeng/services/fcm_service/domain/repositories/fcm_service_repository.dart';
import 'package:selfeng/services/firestore_service_service/domain/repositories/firestore_service_repository.dart';
import 'package:selfeng/services/notification_service/domain/repositories/notification_service_repository.dart';
import 'package:selfeng/services/setting_cache_service/data/datasource/setting_local_datasource.dart';
import 'package:selfeng/services/user_cache_service/data/datasource/user_local_datasource.dart';
import 'package:selfeng/services/user_cache_service/domain/repositories/user_cache_repository.dart';
import 'package:selfeng/services/user_data_service/domain/repositories/user_data_service_repository.dart';
import 'firebase_mocks.dart';

// Repository Mocks

/// Mock implementation of UserRepository for testing
class MockUserRepository extends Mock implements UserRepository {}

/// Mock implementation of FCMServiceRepository for testing
class MockFCMServiceRepository extends Mock implements FCMServiceRepository {}

/// Mock implementation of CrashlyticsRepository for testing
class MockCrashlyticsRepository extends Mock implements CrashlyticsRepository {}

/// Mock implementation of FirestoreServiceRepository for testing
class MockFirestoreServiceRepository extends Mock
    implements FirestoreServiceRepository {
  @override
  final MockFirebaseAuth firebaseAuth = MockFirebaseAuth();
}

/// Mock implementation of UserDataServiceRepository for testing
class MockUserDataServiceRepository extends Mock
    implements UserDataServiceRepository {
  @override
  final MockFirestoreServiceRepository dataSource =
      MockFirestoreServiceRepository();
}

/// Mock implementation of NotificationServiceRepository for testing
class MockNotificationServiceRepository extends Mock
    implements NotificationServiceRepository {}

// Data Source Mocks

/// Mock implementation of UserDataSource for testing
class MockUserDataSource extends Mock implements UserDataSource {}

/// Mock implementation of SettingDataSource for testing
class MockSettingDataSource extends Mock implements SettingDataSource {}
