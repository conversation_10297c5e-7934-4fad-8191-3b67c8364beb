import 'package:device_info_plus/device_info_plus.dart';
import 'package:in_app_update/in_app_update.dart';
import 'package:mocktail/mocktail.dart';
import 'package:selfeng/services/in_app_update_service/domain/repositories/i_in_app_update_service.dart';
import 'package:selfeng/services/in_app_update_service/domain/repositories/i_package_info_service.dart';
import 'package:selfeng/shared/domain/i_platform_service.dart';
import 'package:selfeng/shared/data/local/shared_prefs_storage_service.dart';
import 'package:selfeng/shared/data/local/storage_service.dart';

// Service Mocks

/// Mock implementation of SharedPrefsService for testing
class MockSharedPrefsService extends Mock implements SharedPrefsService {}

/// Mock implementation of StorageService for testing
class MockStorageService extends Mock implements StorageService {}

// Device Info Mocks

/// Mock implementation of DeviceInfoPlugin for testing
class MockDeviceInfoPlugin extends Mock implements DeviceInfoPlugin {}

/// Mock implementation of AndroidDeviceInfo for testing
class MockAndroidDeviceInfo extends Mock implements AndroidDeviceInfo {}

/// Mock implementation of IosDeviceInfo for testing
class MockIosDeviceInfo extends Mock implements IosDeviceInfo {}

// In-App Update Service Mocks

/// Mock implementation of AppUpdateInfo for testing
class MockAppUpdateInfo extends Mock implements AppUpdateInfo {}

/// Mock implementation of IInAppUpdateService for testing
class MockInAppUpdateService extends Mock implements IInAppUpdateService {}

/// Mock implementation of IPackageInfoService for testing
class MockPackageInfoService extends Mock implements IPackageInfoService {}

/// Mock implementation of IPlatformService for testing
class MockIPlatformService extends Mock implements IPlatformService {}
