import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/foundation.dart';
import 'package:selfeng/services/crashlytics_service/domain/repositories/crashlytics_repository.dart';
import 'package:selfeng/services/crashlytics_service/utils/error_handler.dart';

/// Mock implementation for testing
class MockCrashlyticsRepository implements CrashlyticsRepository {
  final List<String> loggedMessages = [];
  final List<Map<String, dynamic>> reportedErrors = [];
  final Map<String, dynamic> customKeys = {};
  String userId = '';
  bool isCollectionEnabled = true;

  @override
  Future<void> initialize() async {
    loggedMessages.add('Crashlytics service initialized');
  }

  @override
  Future<void> setUserId(String userId) async {
    this.userId = userId;
  }

  @override
  Future<void> setUserEmail(String email) async {
    customKeys['user_email'] = email;
  }

  @override
  Future<void> setCustomKey(String key, dynamic value) async {
    customKeys[key] = value;
  }

  @override
  Future<void> setCustomKeys(Map<String, dynamic> keys) async {
    customKeys.addAll(keys);
  }

  @override
  void log(String message) {
    loggedMessages.add(message);
  }

  @override
  Future<void> recordError(
    dynamic exception,
    StackTrace? stackTrace, {
    String? reason,
    Map<String, dynamic>? context,
    bool fatal = false,
  }) async {
    reportedErrors.add({
      'exception': exception,
      'stackTrace': stackTrace,
      'reason': reason,
      'context': context,
      'fatal': fatal,
    });
  }

  @override
  Future<void> recordFlutterError(FlutterErrorDetails errorDetails) async {
    reportedErrors.add({
      'exception': errorDetails.exception,
      'stackTrace': errorDetails.stack,
      'reason': 'Flutter Error',
      'context': {'error_summary': errorDetails.toString()},
      'fatal': true,
    });
  }

  @override
  Future<void> setCrashlyticsCollectionEnabled(bool enabled) async {
    isCollectionEnabled = enabled;
  }

  @override
  Future<bool> isCrashlyticsCollectionEnabled() async {
    return isCollectionEnabled;
  }

  @override
  Future<void> clearUserData() async {
    userId = '';
    customKeys.clear();
  }

  @override
  Future<void> sendUnsentReports() async {
    loggedMessages.add('Unsent reports sent');
  }

  @override
  Future<bool> checkForUnsentReports() async {
    return false;
  }
}

void main() {
  group('Crashlytics Service Tests', () {
    late MockCrashlyticsRepository mockRepository;
    late ErrorHandler errorHandler;

    setUp(() {
      mockRepository = MockCrashlyticsRepository();
      errorHandler = ErrorHandler(mockRepository);
    });

    test('should initialize crashlytics service', () async {
      await mockRepository.initialize();

      expect(
        mockRepository.loggedMessages,
        contains('Crashlytics service initialized'),
      );
    });

    test('should set user context', () async {
      const userId = 'test_user_123';
      const email = '<EMAIL>';

      await mockRepository.setUserId(userId);
      await mockRepository.setUserEmail(email);

      expect(mockRepository.userId, equals(userId));
      expect(mockRepository.customKeys['user_email'], equals(email));
    });

    test('should set custom keys', () async {
      final testKeys = {
        'app_version': '1.0.0',
        'environment': 'test',
        'feature': 'authentication',
      };

      await mockRepository.setCustomKeys(testKeys);

      expect(mockRepository.customKeys, containsPair('app_version', '1.0.0'));
      expect(mockRepository.customKeys, containsPair('environment', 'test'));
      expect(
        mockRepository.customKeys,
        containsPair('feature', 'authentication'),
      );
    });

    test('should record error with context', () async {
      final exception = Exception('Test error');
      const reason = 'Test error occurred';
      final context = {'category': 'test', 'user_id': 'test_user'};

      await mockRepository.recordError(
        exception,
        StackTrace.current,
        reason: reason,
        context: context,
        fatal: false,
      );

      expect(mockRepository.reportedErrors, hasLength(1));
      final reportedError = mockRepository.reportedErrors.first;
      expect(reportedError['exception'], equals(exception));
      expect(reportedError['reason'], equals(reason));
      expect(reportedError['context'], equals(context));
      expect(reportedError['fatal'], equals(false));
    });

    test('should log messages', () {
      const message = 'Test log message';

      mockRepository.log(message);

      expect(mockRepository.loggedMessages, contains(message));
    });

    test('should handle collection enabled/disabled', () async {
      expect(await mockRepository.isCrashlyticsCollectionEnabled(), isTrue);

      await mockRepository.setCrashlyticsCollectionEnabled(false);
      expect(await mockRepository.isCrashlyticsCollectionEnabled(), isFalse);

      await mockRepository.setCrashlyticsCollectionEnabled(true);
      expect(await mockRepository.isCrashlyticsCollectionEnabled(), isTrue);
    });

    test('should clear user data', () async {
      // Set some user data first
      await mockRepository.setUserId('test_user');
      await mockRepository.setCustomKey('user_type', 'premium');

      // Clear user data
      await mockRepository.clearUserData();

      expect(mockRepository.userId, isEmpty);
      expect(mockRepository.customKeys, isEmpty);
    });

    group('ErrorHandler Tests', () {
      test('should handle async errors and report them', () async {
        const errorMessage = 'Async operation failed';

        final result = await errorHandler.handleAsync(
          () => throw Exception(errorMessage),
          context: 'Test Operation',
          metadata: {'operation_type': 'async_test'},
          fatal: false,
          shouldRethrow: false,
        );

        expect(result, isNull);
        expect(mockRepository.reportedErrors, hasLength(1));

        final reportedError = mockRepository.reportedErrors.first;
        expect(reportedError['exception'].toString(), contains(errorMessage));
        expect(reportedError['fatal'], equals(false));
      });

      test('should handle sync errors and report them', () {
        const errorMessage = 'Sync operation failed';

        final result = errorHandler.handleSync(
          () => throw Exception(errorMessage),
          context: 'Sync Test Operation',
          metadata: {'operation_type': 'sync_test'},
          fatal: false,
          shouldRethrow: false,
        );

        expect(result, isNull);

        // Allow some time for async error reporting
        Future.delayed(const Duration(milliseconds: 100), () {
          expect(mockRepository.reportedErrors, hasLength(1));
        });
      });

      test('should provide fallback values when errors occur', () async {
        const fallbackValue = 'fallback_result';

        final result = await errorHandler.handleAsync(
          () => throw Exception('Error occurred'),
          fallbackValue: fallbackValue,
          shouldRethrow: false,
        );

        expect(result, equals(fallbackValue));
      });
    });
  });
}
