import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:selfeng/services/fcm_service/domain/repositories/fcm_service_repository.dart';
import 'package:selfeng/shared/domain/models/either.dart';
import 'package:selfeng/shared/exceptions/http_exception.dart';
import '../../../../mocks/service_mocks.dart';
import '../../../../mocks/repository_mocks.dart';
import '../../../../mocks/firebase_mocks.dart';

void main() {
  group('FCMServiceRepository', () {
    late FCMServiceRepository repository;
    late MockUserDataServiceRepository mockUserDataService;
    late MockFirebaseMessaging mockFirebaseMessaging;
    late MockDeviceInfoPlugin mockDeviceInfo;
    late MockFirestoreServiceRepository mockFirestoreService;
    late MockFirebaseAuth mockFirebaseAuth;
    late MockUser mockUser;
    late MockAndroidDeviceInfo mockAndroidDeviceInfo;
    late MockIosDeviceInfo mockIosDeviceInfo;
    late MockIPlatformService mockPlatformService;

    setUp(() {
      mockUserDataService = MockUserDataServiceRepository();
      mockFirebaseMessaging = MockFirebaseMessaging();
      mockDeviceInfo = MockDeviceInfoPlugin();
      mockFirestoreService = mockUserDataService.dataSource;
      mockFirebaseAuth = mockFirestoreService.firebaseAuth;
      mockUser = MockUser();
      mockAndroidDeviceInfo = MockAndroidDeviceInfo();
      mockIosDeviceInfo = MockIosDeviceInfo();
      mockPlatformService = MockIPlatformService();

      repository = FCMServiceRepository(
        mockUserDataService,
        mockFirebaseMessaging,
        mockDeviceInfo,
        mockPlatformService,
      );

      // Default mocks
      when(() => mockUser.uid).thenReturn('test_uid');
      when(() => mockFirebaseAuth.currentUser).thenReturn(mockUser);
      when(
        () => mockFirebaseMessaging.getToken(),
      ).thenAnswer((_) async => 'test_fcm_token');
      when(() => mockAndroidDeviceInfo.id).thenReturn('test_android_id');
      when(
        () => mockIosDeviceInfo.identifierForVendor,
      ).thenReturn('test_ios_id');
      when(
        () => mockDeviceInfo.androidInfo,
      ).thenAnswer((_) async => mockAndroidDeviceInfo);
      when(
        () => mockDeviceInfo.iosInfo,
      ).thenAnswer((_) async => mockIosDeviceInfo);
      when(
        () => mockUserDataService.saveFCMToken(
          token: any(named: 'token'),
          deviceId: any(named: 'deviceId'),
        ),
      ).thenAnswer((_) async => const Right(null));
      when(
        () => mockUserDataService.removeFCMToken(
          deviceId: any(named: 'deviceId'),
        ),
      ).thenAnswer((_) async => const Right(null));
      when(
        () => mockUserDataService.getFCMTokens(),
      ).thenAnswer((_) async => Right(<String, dynamic>{}));
      when(
        () => mockFirebaseMessaging.onTokenRefresh,
      ).thenAnswer((_) => Stream.empty());
    });

    group('initializeFCMToken', () {
      test(
        'should successfully initialize and save FCM token for authenticated user on Android',
        () async {
          // Arrange
          when(() => mockPlatformService.isAndroid).thenReturn(true);
          when(() => mockPlatformService.isIOS).thenReturn(false);

          // Act
          final result = await repository.initializeFCMToken();

          // Assert
          expect(result, isA<Right<AppException, void>>());
          verify(() => mockFirebaseMessaging.getToken()).called(1);
          verify(() => mockDeviceInfo.androidInfo).called(1);
          verify(
            () => mockUserDataService.saveFCMToken(
              token: 'test_fcm_token',
              deviceId: 'test_android_id',
            ),
          ).called(1);
        },
      );

      test(
        'should successfully initialize and save FCM token for authenticated user on iOS',
        () async {
          // Arrange
          when(() => mockPlatformService.isAndroid).thenReturn(false);
          when(() => mockPlatformService.isIOS).thenReturn(true);

          // Act
          final result = await repository.initializeFCMToken();

          // Assert
          expect(result, isA<Right<AppException, void>>());
          verify(() => mockFirebaseMessaging.getToken()).called(1);
          verify(() => mockDeviceInfo.iosInfo).called(1);
          verify(
            () => mockUserDataService.saveFCMToken(
              token: 'test_fcm_token',
              deviceId: 'test_ios_id',
            ),
          ).called(1);
        },
      );

      test(
        'should skip FCM token save when user is not authenticated',
        () async {
          // Arrange
          when(() => mockFirebaseAuth.currentUser).thenReturn(null);

          // Act
          final result = await repository.initializeFCMToken();

          // Assert
          expect(result, isA<Right<AppException, void>>());
          verify(() => mockFirebaseAuth.currentUser).called(1);
          verifyNever(() => mockFirebaseMessaging.getToken());
        },
      );

      test('should return Left when FCM token is empty', () async {
        // Arrange
        when(
          () => mockFirebaseMessaging.getToken(),
        ).thenAnswer((_) async => '');

        // Act
        final result = await repository.initializeFCMToken();

        // Assert
        expect(result, isA<Left<AppException, void>>());
        expect(
          (result as Left).value.identifier,
          'FCM token initialization failed',
        );
      });

      test('should return Left when getting FCM token fails', () async {
        // Arrange
        when(
          () => mockFirebaseMessaging.getToken(),
        ).thenThrow(Exception('Failure'));

        // Act
        final result = await repository.initializeFCMToken();

        // Assert
        expect(result, isA<Left<AppException, void>>());
        expect((result as Left).value.identifier, 'FCM token retrieval failed');
      });

      test('should return Left when device ID is empty', () async {
        // Arrange
        when(() => mockPlatformService.isAndroid).thenReturn(true);
        when(() => mockPlatformService.isIOS).thenReturn(false);
        when(() => mockAndroidDeviceInfo.id).thenReturn('');

        // Act
        final result = await repository.initializeFCMToken();

        // Assert
        expect(result, isA<Left<AppException, void>>());
        expect((result as Left).value.identifier, 'Device ID retrieval failed');
      });
    });

    group('getFCMToken', () {
      test('should return FCM token on non-web platform', () async {
        // Arrange
        when(
          () => mockFirebaseMessaging.getToken(),
        ).thenAnswer((_) async => 'mobile_token');
        // Act
        final result = await repository.getFCMToken();
        // Assert
        expect((result as Right).value, 'mobile_token');
      });

      test('should return empty string when FCM token is null', () async {
        // Arrange
        when(
          () => mockFirebaseMessaging.getToken(),
        ).thenAnswer((_) async => null);
        // Act
        final result = await repository.getFCMToken();
        // Assert
        expect((result as Right).value, '');
      });

      test(
        'should return Left with AppException when getting FCM token fails',
        () async {
          // Arrange
          when(
            () => mockFirebaseMessaging.getToken(),
          ).thenThrow(Exception('Failed to get token'));
          // Act
          final result = await repository.getFCMToken();
          // Assert
          expect(result, isA<Left<AppException, String>>());
          expect(
            (result as Left<AppException, String>).value.identifier,
            equals('FCM token retrieval failed'),
          );
        },
      );
    });

    group('getDeviceId', () {
      test('should return Android device ID', () async {
        // Arrange
        when(() => mockPlatformService.isAndroid).thenReturn(true);
        when(() => mockPlatformService.isIOS).thenReturn(false);
        // Act
        final result = await repository.getDeviceId();
        // Assert
        expect((result as Right).value, 'test_android_id');
      });

      test('should return iOS device ID', () async {
        // Arrange
        when(() => mockPlatformService.isAndroid).thenReturn(false);
        when(() => mockPlatformService.isIOS).thenReturn(true);
        // Act
        final result = await repository.getDeviceId();
        // Assert
        expect((result as Right).value, 'test_ios_id');
      });

      test('should return Left for unsupported platform', () async {
        // Arrange
        when(() => mockPlatformService.isAndroid).thenReturn(false);
        when(() => mockPlatformService.isIOS).thenReturn(false);
        // Act
        final result = await repository.getDeviceId();
        // Assert
        expect(result, isA<Left<AppException, String>>());
        expect(
          (result as Left).value.identifier,
          equals('Unsupported platform'),
        );
      });

      test(
        'should return empty string when iOS identifierForVendor is null',
        () async {
          // Arrange
          when(() => mockPlatformService.isAndroid).thenReturn(false);
          when(() => mockPlatformService.isIOS).thenReturn(true);
          when(() => mockIosDeviceInfo.identifierForVendor).thenReturn(null);

          // Act
          final result = await repository.getDeviceId();

          // Assert
          expect(result, isA<Right<AppException, String>>());
          expect((result as Right).value, '');
        },
      );

      test(
        'should return Left when Android device info retrieval fails',
        () async {
          // Arrange
          when(() => mockPlatformService.isAndroid).thenReturn(true);
          when(() => mockPlatformService.isIOS).thenReturn(false);
          when(
            () => mockDeviceInfo.androidInfo,
          ).thenThrow(Exception('Android info failed'));

          // Act
          final result = await repository.getDeviceId();

          // Assert
          expect(result, isA<Left<AppException, String>>());
          expect(
            (result as Left).value.identifier,
            'Device ID retrieval failed',
          );
        },
      );

      test('should return Left when iOS device info retrieval fails', () async {
        // Arrange
        when(() => mockPlatformService.isAndroid).thenReturn(false);
        when(() => mockPlatformService.isIOS).thenReturn(true);
        when(
          () => mockDeviceInfo.iosInfo,
        ).thenThrow(Exception('iOS info failed'));

        // Act
        final result = await repository.getDeviceId();

        // Assert
        expect(result, isA<Left<AppException, String>>());
        expect((result as Left).value.identifier, 'Device ID retrieval failed');
      });
    });

    group('requestPermissions', () {
      test('should request notification permissions successfully', () async {
        // Arrange
        final notificationSettings = MockNotificationSettings();
        when(
          () => mockFirebaseMessaging.requestPermission(
            alert: true,
            announcement: true,
            badge: true,
            carPlay: true,
            criticalAlert: true,
            provisional: false,
            sound: true,
          ),
        ).thenAnswer((_) async => notificationSettings);
        // Act
        final result = await repository.requestPermissions();
        // Assert
        expect(result, isA<Right<AppException, NotificationSettings>>());
        verify(
          () => mockFirebaseMessaging.requestPermission(
            alert: true,
            announcement: true,
            badge: true,
            carPlay: true,
            criticalAlert: true,
            provisional: false,
            sound: true,
          ),
        ).called(1);
      });

      test(
        'should return Left with AppException when requesting permissions fails',
        () async {
          // Arrange
          when(
            () => mockFirebaseMessaging.requestPermission(
              alert: any(named: 'alert'),
              announcement: any(named: 'announcement'),
              badge: any(named: 'badge'),
              carPlay: any(named: 'carPlay'),
              criticalAlert: any(named: 'criticalAlert'),
              provisional: any(named: 'provisional'),
              sound: any(named: 'sound'),
            ),
          ).thenThrow(Exception('Permission request failed'));
          // Act
          final result = await repository.requestPermissions();
          // Assert
          expect(result, isA<Left<AppException, NotificationSettings>>());
          expect(
            (result as Left<AppException, NotificationSettings>)
                .value
                .identifier,
            equals('Permission request failed'),
          );
        },
      );
    });

    group('refreshFCMToken', () {
      test('should successfully refresh and save FCM token', () async {
        // Arrange
        when(() => mockPlatformService.isAndroid).thenReturn(true);
        when(() => mockPlatformService.isIOS).thenReturn(false);

        // Act
        final result = await repository.refreshFCMToken();

        // Assert
        expect(result, isA<Right<AppException, void>>());
        verify(() => mockFirebaseMessaging.getToken()).called(1);
        verify(() => mockDeviceInfo.androidInfo).called(1);
        verify(
          () => mockUserDataService.saveFCMToken(
            token: 'test_fcm_token',
            deviceId: 'test_android_id',
          ),
        ).called(1);
      });

      test('should return Left when FCM token retrieval fails', () async {
        // Arrange
        when(
          () => mockFirebaseMessaging.getToken(),
        ).thenThrow(Exception('Token retrieval failed'));

        // Act
        final result = await repository.refreshFCMToken();

        // Assert
        expect(result, isA<Left<AppException, void>>());
        expect((result as Left).value.identifier, 'FCM token retrieval failed');
      });

      test('should handle empty device ID gracefully', () async {
        // Arrange
        when(() => mockPlatformService.isAndroid).thenReturn(true);
        when(() => mockPlatformService.isIOS).thenReturn(false);
        when(() => mockAndroidDeviceInfo.id).thenReturn('');

        // Act
        final result = await repository.refreshFCMToken();

        // Assert
        expect(result, isA<Right<AppException, void>>());
        verify(
          () => mockUserDataService.saveFCMToken(
            token: 'test_fcm_token',
            deviceId: '',
          ),
        ).called(1);
      });

      test('should return Left when save FCM token fails', () async {
        // Arrange
        when(() => mockPlatformService.isAndroid).thenReturn(true);
        when(() => mockPlatformService.isIOS).thenReturn(false);
        when(
          () => mockUserDataService.saveFCMToken(
            token: any(named: 'token'),
            deviceId: any(named: 'deviceId'),
          ),
        ).thenAnswer(
          (_) async => Left(
            AppException(
              identifier: 'Save failed',
              statusCode: 0,
              message: 'Failed to save token',
            ),
          ),
        );

        // Act
        final result = await repository.refreshFCMToken();

        // Assert
        expect(result, isA<Left<AppException, void>>());
        expect((result as Left).value.identifier, 'Save failed');
      });
    });

    group('removeFCMTokenForCurrentDevice', () {
      test('should successfully remove FCM token for current device', () async {
        // Arrange
        when(() => mockPlatformService.isAndroid).thenReturn(true);
        when(() => mockPlatformService.isIOS).thenReturn(false);
        when(
          () => mockUserDataService.removeFCMToken(
            deviceId: any(named: 'deviceId'),
          ),
        ).thenAnswer((_) async => const Right(null));

        // Act
        final result = await repository.removeFCMTokenForCurrentDevice();

        // Assert
        expect(result, isA<Right<AppException, void>>());
        verify(() => mockDeviceInfo.androidInfo).called(1);
        verify(
          () => mockUserDataService.removeFCMToken(deviceId: 'test_android_id'),
        ).called(1);
      });

      test('should handle empty device ID gracefully', () async {
        // Arrange
        when(() => mockPlatformService.isAndroid).thenReturn(true);
        when(() => mockPlatformService.isIOS).thenReturn(false);
        when(() => mockAndroidDeviceInfo.id).thenReturn('');

        // Act
        final result = await repository.removeFCMTokenForCurrentDevice();

        // Assert
        expect(result, isA<Right<AppException, void>>());
        verify(
          () => mockUserDataService.removeFCMToken(deviceId: ''),
        ).called(1);
      });

      test('should return Left when remove FCM token fails', () async {
        // Arrange
        when(() => mockPlatformService.isAndroid).thenReturn(true);
        when(() => mockPlatformService.isIOS).thenReturn(false);
        when(
          () => mockUserDataService.removeFCMToken(
            deviceId: any(named: 'deviceId'),
          ),
        ).thenAnswer(
          (_) async => Left(
            AppException(
              identifier: 'Remove failed',
              statusCode: 0,
              message: 'Failed to remove token',
            ),
          ),
        );

        // Act
        final result = await repository.removeFCMTokenForCurrentDevice();

        // Assert
        expect(result, isA<Left<AppException, void>>());
        expect((result as Left).value.identifier, 'Remove failed');
      });
    });

    group('getAllFCMTokens', () {
      test('should successfully get all FCM tokens', () async {
        // Arrange
        final mockTokens = {'device1': 'token1', 'device2': 'token2'};
        when(
          () => mockUserDataService.getFCMTokens(),
        ).thenAnswer((_) async => Right(mockTokens));

        // Act
        final result = await repository.getAllFCMTokens();

        // Assert
        expect(result, isA<Right<AppException, Map<String, dynamic>>>());
        expect((result as Right).value, mockTokens);
        verify(() => mockUserDataService.getFCMTokens()).called(1);
      });

      test('should return Left when get FCM tokens fails', () async {
        // Arrange
        when(() => mockUserDataService.getFCMTokens()).thenAnswer(
          (_) async => Left(
            AppException(
              identifier: 'Get tokens failed',
              statusCode: 0,
              message: 'Failed to get tokens',
            ),
          ),
        );

        // Act
        final result = await repository.getAllFCMTokens();

        // Assert
        expect(result, isA<Left<AppException, Map<String, dynamic>>>());
        expect((result as Left).value.identifier, 'Get tokens failed');
      });
    });

    group('areNotificationsEnabled', () {
      test('should return true when notifications are authorized', () async {
        // Arrange
        final mockSettings = MockNotificationSettings();
        when(
          () => mockFirebaseMessaging.getNotificationSettings(),
        ).thenAnswer((_) async => mockSettings);
        when(
          () => mockSettings.authorizationStatus,
        ).thenReturn(AuthorizationStatus.authorized);

        // Act
        final result = await repository.areNotificationsEnabled();

        // Assert
        expect(result, isA<Right<AppException, bool>>());
        expect((result as Right).value, true);
        verify(() => mockFirebaseMessaging.getNotificationSettings()).called(1);
      });

      test(
        'should return false when notifications are not authorized',
        () async {
          // Arrange
          final mockSettings = MockNotificationSettings();
          when(
            () => mockFirebaseMessaging.getNotificationSettings(),
          ).thenAnswer((_) async => mockSettings);
          when(
            () => mockSettings.authorizationStatus,
          ).thenReturn(AuthorizationStatus.denied);

          // Act
          final result = await repository.areNotificationsEnabled();

          // Assert
          expect(result, isA<Right<AppException, bool>>());
          expect((result as Right).value, false);
          verify(
            () => mockFirebaseMessaging.getNotificationSettings(),
          ).called(1);
        },
      );

      test(
        'should return Left when getting notification settings fails',
        () async {
          // Arrange
          when(
            () => mockFirebaseMessaging.getNotificationSettings(),
          ).thenThrow(Exception('Settings retrieval failed'));

          // Act
          final result = await repository.areNotificationsEnabled();

          // Assert
          expect(result, isA<Left<AppException, bool>>());
          expect((result as Left).value.identifier, 'Permission check failed');
        },
      );
    });

    group('initializeFCMTokenAfterAuth', () {
      test(
        'should successfully initialize FCM token after authentication',
        () async {
          // Arrange
          when(() => mockPlatformService.isAndroid).thenReturn(true);
          when(() => mockPlatformService.isIOS).thenReturn(false);
          when(
            () => mockFirebaseMessaging.subscribeToTopic(any()),
          ).thenAnswer((_) async {});

          // Act
          final result = await repository.initializeFCMTokenAfterAuth();

          // Assert
          expect(result, isA<Right<AppException, void>>());
          verify(() => mockFirebaseMessaging.getToken()).called(1);
          verify(() => mockDeviceInfo.androidInfo).called(1);
          verify(
            () => mockUserDataService.saveFCMToken(
              token: 'test_fcm_token',
              deviceId: 'test_android_id',
            ),
          ).called(1);
          verify(
            () =>
                mockFirebaseMessaging.subscribeToTopic('general_notification'),
          ).called(1);
        },
      );

      test('should return Left when user is not authenticated', () async {
        // Arrange
        when(() => mockFirebaseAuth.currentUser).thenReturn(null);

        // Act
        final result = await repository.initializeFCMTokenAfterAuth();

        // Assert
        expect(result, isA<Left<AppException, void>>());
        expect((result as Left).value.identifier, 'User not authenticated');
        expect((result as Left).value.statusCode, 401);
      });

      test('should return Left when FCM token is empty', () async {
        // Arrange
        when(
          () => mockFirebaseMessaging.getToken(),
        ).thenAnswer((_) async => '');

        // Act
        final result = await repository.initializeFCMTokenAfterAuth();

        // Assert
        expect(result, isA<Left<AppException, void>>());
        expect(
          (result as Left).value.identifier,
          'FCM token initialization failed',
        );
      });

      test('should return Left when device ID is empty', () async {
        // Arrange
        when(() => mockPlatformService.isAndroid).thenReturn(true);
        when(() => mockPlatformService.isIOS).thenReturn(false);
        when(() => mockAndroidDeviceInfo.id).thenReturn('');

        // Act
        final result = await repository.initializeFCMTokenAfterAuth();

        // Assert
        expect(result, isA<Left<AppException, void>>());
        expect((result as Left).value.identifier, 'Device ID retrieval failed');
      });

      test('should return Left when save FCM token fails', () async {
        // Arrange
        when(() => mockPlatformService.isAndroid).thenReturn(true);
        when(() => mockPlatformService.isIOS).thenReturn(false);
        when(
          () => mockUserDataService.saveFCMToken(
            token: any(named: 'token'),
            deviceId: any(named: 'deviceId'),
          ),
        ).thenAnswer(
          (_) async => Left(
            AppException(
              identifier: 'Save failed',
              statusCode: 0,
              message: 'Failed to save token',
            ),
          ),
        );

        // Act
        final result = await repository.initializeFCMTokenAfterAuth();

        // Assert
        expect(result, isA<Left<AppException, void>>());
        expect((result as Left).value.identifier, 'Save failed');
        verifyNever(() => mockFirebaseMessaging.subscribeToTopic(any()));
      });

      test('should handle topic subscription failure gracefully', () async {
        // Arrange
        when(() => mockPlatformService.isAndroid).thenReturn(true);
        when(() => mockPlatformService.isIOS).thenReturn(false);
        when(
          () => mockFirebaseMessaging.subscribeToTopic(any()),
        ).thenThrow(Exception('Topic subscription failed'));

        // Act
        final result = await repository.initializeFCMTokenAfterAuth();

        // Assert
        expect(result, isA<Right<AppException, void>>());
        verify(
          () => mockFirebaseMessaging.subscribeToTopic('general_notification'),
        ).called(1);
      });
    });

    group('subscribeToTopic', () {
      test('should successfully subscribe to topic', () async {
        // Arrange
        when(
          () => mockFirebaseMessaging.subscribeToTopic(any()),
        ).thenAnswer((_) async {});

        // Act
        final result = await repository.subscribeToTopic('test_topic');

        // Assert
        expect(result, isA<Right<AppException, void>>());
        verify(
          () => mockFirebaseMessaging.subscribeToTopic('test_topic'),
        ).called(1);
      });

      test('should return Left when topic subscription fails', () async {
        // Arrange
        when(
          () => mockFirebaseMessaging.subscribeToTopic(any()),
        ).thenThrow(Exception('Subscription failed'));

        // Act
        final result = await repository.subscribeToTopic('test_topic');

        // Assert
        expect(result, isA<Left<AppException, void>>());
        expect((result as Left).value.identifier, 'Topic subscription failed');
      });
    });

    group('unsubscribeFromTopic', () {
      test('should successfully unsubscribe from topic', () async {
        // Arrange
        when(
          () => mockFirebaseMessaging.unsubscribeFromTopic(any()),
        ).thenAnswer((_) async {});

        // Act
        final result = await repository.unsubscribeFromTopic('test_topic');

        // Assert
        expect(result, isA<Right<AppException, void>>());
        verify(
          () => mockFirebaseMessaging.unsubscribeFromTopic('test_topic'),
        ).called(1);
      });

      test('should return Left when topic unsubscription fails', () async {
        // Arrange
        when(
          () => mockFirebaseMessaging.unsubscribeFromTopic(any()),
        ).thenThrow(Exception('Unsubscription failed'));

        // Act
        final result = await repository.unsubscribeFromTopic('test_topic');

        // Assert
        expect(result, isA<Left<AppException, void>>());
        expect(
          (result as Left).value.identifier,
          'Topic unsubscription failed',
        );
      });
    });
  });
}
