import 'package:flutter_test/flutter_test.dart';
import 'package:in_app_update/in_app_update.dart';
import 'package:mocktail/mocktail.dart';
import 'package:selfeng/services/in_app_update_service/domain/repositories/in_app_update_service_repository.dart';
import 'package:selfeng/shared/domain/models/either.dart';
import 'package:selfeng/shared/exceptions/http_exception.dart';
import '../../../../mocks/service_mocks.dart';

void main() {
  group('InAppUpdateServiceRepository', () {
    late InAppUpdateServiceRepository repository;
    late MockInAppUpdateService mockInAppUpdateService;
    late MockPackageInfoService mockPackageInfoService;
    late MockIPlatformService mockPlatformService;

    setUp(() {
      mockInAppUpdateService = MockInAppUpdateService();
      mockPackageInfoService = MockPackageInfoService();
      mockPlatformService = MockIPlatformService();

      repository = InAppUpdateServiceRepository(
        inAppUpdateService: mockInAppUpdateService,
        packageInfoService: mockPackageInfoService,
        platformService: mockPlatformService,
      );
    });

    group('initialize', () {
      test('should initialize successfully on supported platforms', () async {
        // Arrange
        when(() => mockPlatformService.isAndroid).thenReturn(true);
        when(() => mockPlatformService.isIOS).thenReturn(false);
        when(
          () => mockPackageInfoService.getBuildNumber(),
        ).thenAnswer((_) async => '1');

        // Act
        final result = await repository.initialize();

        // Assert
        expect(result, isA<Right<AppException, void>>());
        verify(() => mockPackageInfoService.getBuildNumber()).called(1);
      });

      test('should return success on unsupported platforms', () async {
        // Arrange
        when(() => mockPlatformService.isAndroid).thenReturn(false);
        when(() => mockPlatformService.isIOS).thenReturn(false);

        // Act
        final result = await repository.initialize();

        // Assert
        expect(result, isA<Right<AppException, void>>());
        verifyNever(() => mockPackageInfoService.getBuildNumber());
      });
    });

    group('checkForUpdate', () {
      test(
        'should check for updates successfully when update is available',
        () async {
          // Arrange
          final mockUpdateInfo = MockAppUpdateInfo();
          when(
            () => mockUpdateInfo.updateAvailability,
          ).thenReturn(UpdateAvailability.updateAvailable);
          when(() => mockUpdateInfo.availableVersionCode).thenReturn(2);
          when(() => mockUpdateInfo.updatePriority).thenReturn(1);
          when(() => mockUpdateInfo.immediateUpdateAllowed).thenReturn(true);
          when(() => mockUpdateInfo.flexibleUpdateAllowed).thenReturn(true);

          when(() => mockPlatformService.isAndroid).thenReturn(true);
          when(() => mockPlatformService.isIOS).thenReturn(false);
          when(
            () => mockInAppUpdateService.checkForUpdate(),
          ).thenAnswer((_) async => mockUpdateInfo);

          // Act
          final result = await repository.checkForUpdate();

          // Assert
          expect(result, isA<Right<AppException, AppUpdateInfo?>>());
          expect(
            (result as Right<AppException, AppUpdateInfo?>).value,
            equals(mockUpdateInfo),
          );
          expect(repository.isUpdateAvailable(mockUpdateInfo), isTrue);
        },
      );

      test(
        'should check for updates successfully when no update is available',
        () async {
          // Arrange
          final mockUpdateInfo = MockAppUpdateInfo();
          when(
            () => mockUpdateInfo.updateAvailability,
          ).thenReturn(UpdateAvailability.unknown);

          when(() => mockPlatformService.isAndroid).thenReturn(true);
          when(() => mockPlatformService.isIOS).thenReturn(false);
          when(
            () => mockInAppUpdateService.checkForUpdate(),
          ).thenAnswer((_) async => mockUpdateInfo);

          // Act
          final result = await repository.checkForUpdate();

          // Assert
          expect(result, isA<Right<AppException, AppUpdateInfo?>>());
          expect(
            (result as Right<AppException, AppUpdateInfo?>).value,
            equals(mockUpdateInfo),
          );
          expect(repository.isUpdateAvailable(mockUpdateInfo), isFalse);
        },
      );

      test('should return success on unsupported platforms', () async {
        // Arrange
        when(() => mockPlatformService.isAndroid).thenReturn(false);
        when(() => mockPlatformService.isIOS).thenReturn(false);

        // Act
        final result = await repository.checkForUpdate();

        // Assert
        expect(result, isA<Right<AppException, AppUpdateInfo?>>());
        expect((result as Right<AppException, AppUpdateInfo?>).value, isNull);
        verifyNever(() => mockInAppUpdateService.checkForUpdate());
      });

      test(
        'should return Left with AppException when checking for updates fails',
        () async {
          // Arrange
          when(() => mockPlatformService.isAndroid).thenReturn(true);
          when(() => mockPlatformService.isIOS).thenReturn(false);
          when(
            () => mockInAppUpdateService.checkForUpdate(),
          ).thenThrow(Exception('Failed to check for updates'));

          // Act
          final result = await repository.checkForUpdate();

          // Assert
          expect(result, isA<Left<AppException, AppUpdateInfo?>>());
          expect(
            (result as Left<AppException, AppUpdateInfo?>).value.identifier,
            equals('in_app_update_check_error'),
          );
        },
      );
    });

    group('startImmediateUpdate', () {
      test('should start immediate update successfully', () async {
        // Arrange
        final mockUpdateInfo = MockAppUpdateInfo();
        when(
          () => mockUpdateInfo.updateAvailability,
        ).thenReturn(UpdateAvailability.updateAvailable);
        when(() => mockUpdateInfo.immediateUpdateAllowed).thenReturn(true);

        when(
          () => mockInAppUpdateService.performImmediateUpdate(),
        ).thenAnswer((_) async => Future.value());

        // Act
        final result = await repository.startImmediateUpdate(mockUpdateInfo);

        // Assert
        expect(result, isA<Right<AppException, void>>());
        verify(() => mockInAppUpdateService.performImmediateUpdate()).called(1);
      });

      test(
        'should return Left with AppException when no update is available',
        () async {
          // Arrange
          final mockUpdateInfo = MockAppUpdateInfo();
          when(
            () => mockUpdateInfo.updateAvailability,
          ).thenReturn(UpdateAvailability.unknown);

          // Act
          final result = await repository.startImmediateUpdate(mockUpdateInfo);

          // Assert
          expect(result, isA<Left<AppException, void>>());
          expect(
            (result as Left<AppException, void>).value.identifier,
            equals('no_update_available'),
          );
        },
      );

      test(
        'should return Left with AppException when immediate update is not allowed',
        () async {
          // Arrange
          final mockUpdateInfo = MockAppUpdateInfo();
          when(
            () => mockUpdateInfo.updateAvailability,
          ).thenReturn(UpdateAvailability.updateAvailable);
          when(() => mockUpdateInfo.immediateUpdateAllowed).thenReturn(false);

          // Act
          final result = await repository.startImmediateUpdate(mockUpdateInfo);

          // Assert
          expect(result, isA<Left<AppException, void>>());
          expect(
            (result as Left<AppException, void>).value.identifier,
            equals('immediate_update_not_allowed'),
          );
        },
      );

      test(
        'should return Left with AppException when starting immediate update fails',
        () async {
          // Arrange
          final mockUpdateInfo = MockAppUpdateInfo();
          when(
            () => mockUpdateInfo.updateAvailability,
          ).thenReturn(UpdateAvailability.updateAvailable);
          when(() => mockUpdateInfo.immediateUpdateAllowed).thenReturn(true);

          when(
            () => mockInAppUpdateService.performImmediateUpdate(),
          ).thenThrow(Exception('Failed to start immediate update'));

          // Act
          final result = await repository.startImmediateUpdate(mockUpdateInfo);

          // Assert
          expect(result, isA<Left<AppException, void>>());
          expect(
            (result as Left<AppException, void>).value.identifier,
            equals('immediate_update_failed'),
          );
        },
      );
    });

    group('startFlexibleUpdate', () {
      test('should start flexible update successfully', () async {
        // Arrange
        final mockUpdateInfo = MockAppUpdateInfo();
        when(
          () => mockUpdateInfo.updateAvailability,
        ).thenReturn(UpdateAvailability.updateAvailable);
        when(() => mockUpdateInfo.flexibleUpdateAllowed).thenReturn(true);

        when(
          () => mockInAppUpdateService.startFlexibleUpdate(),
        ).thenAnswer((_) async => Future.value());

        // Act
        final result = await repository.startFlexibleUpdate(mockUpdateInfo);

        // Assert
        expect(result, isA<Right<AppException, void>>());
        verify(() => mockInAppUpdateService.startFlexibleUpdate()).called(1);
      });

      test(
        'should return Left with AppException when no update is available',
        () async {
          // Arrange
          final mockUpdateInfo = MockAppUpdateInfo();
          when(
            () => mockUpdateInfo.updateAvailability,
          ).thenReturn(UpdateAvailability.unknown);

          // Act
          final result = await repository.startFlexibleUpdate(mockUpdateInfo);

          // Assert
          expect(result, isA<Left<AppException, void>>());
          expect(
            (result as Left<AppException, void>).value.identifier,
            equals('no_update_available'),
          );
        },
      );

      test(
        'should return Left with AppException when flexible update is not allowed',
        () async {
          // Arrange
          final mockUpdateInfo = MockAppUpdateInfo();
          when(
            () => mockUpdateInfo.updateAvailability,
          ).thenReturn(UpdateAvailability.updateAvailable);
          when(() => mockUpdateInfo.flexibleUpdateAllowed).thenReturn(false);

          // Act
          final result = await repository.startFlexibleUpdate(mockUpdateInfo);

          // Assert
          expect(result, isA<Left<AppException, void>>());
          expect(
            (result as Left<AppException, void>).value.identifier,
            equals('flexible_update_not_allowed'),
          );
        },
      );

      test(
        'should return Left with AppException when starting flexible update fails',
        () async {
          // Arrange
          final mockUpdateInfo = MockAppUpdateInfo();
          when(
            () => mockUpdateInfo.updateAvailability,
          ).thenReturn(UpdateAvailability.updateAvailable);
          when(() => mockUpdateInfo.flexibleUpdateAllowed).thenReturn(true);

          when(
            () => mockInAppUpdateService.startFlexibleUpdate(),
          ).thenThrow(Exception('Failed to start flexible update'));

          // Act
          final result = await repository.startFlexibleUpdate(mockUpdateInfo);

          // Assert
          expect(result, isA<Left<AppException, void>>());
          expect(
            (result as Left<AppException, void>).value.identifier,
            equals('flexible_update_failed'),
          );
        },
      );
    });

    group('completeFlexibleUpdate', () {
      test('should complete flexible update successfully', () async {
        // Arrange
        when(
          () => mockInAppUpdateService.completeFlexibleUpdate(),
        ).thenAnswer((_) async => Future.value());

        // Act
        final result = await repository.completeFlexibleUpdate();

        // Assert
        expect(result, isA<Right<AppException, void>>());
        verify(() => mockInAppUpdateService.completeFlexibleUpdate()).called(1);
      });

      test(
        'should return Left with AppException when completing flexible update fails',
        () async {
          // Arrange
          when(
            () => mockInAppUpdateService.completeFlexibleUpdate(),
          ).thenThrow(Exception('Failed to complete flexible update'));

          // Act
          final result = await repository.completeFlexibleUpdate();

          // Assert
          expect(result, isA<Left<AppException, void>>());
          expect(
            (result as Left<AppException, void>).value.identifier,
            equals('complete_flexible_update_failed'),
          );
        },
      );
    });

    group('isFlexibleUpdateDownloaded', () {
      test('should return true when flexible update is downloaded', () async {
        // Arrange
        final mockUpdateInfo = MockAppUpdateInfo();
        when(
          () => mockUpdateInfo.installStatus,
        ).thenReturn(InstallStatus.downloaded);

        // Act
        final result = await repository.isFlexibleUpdateDownloaded(
          mockUpdateInfo,
        );

        // Assert
        expect(result, isA<Right<AppException, bool>>());
        expect((result as Right<AppException, bool>).value, isTrue);
      });

      test(
        'should return false when flexible update is not downloaded',
        () async {
          // Arrange
          final mockUpdateInfo = MockAppUpdateInfo();
          when(
            () => mockUpdateInfo.installStatus,
          ).thenReturn(InstallStatus.pending);

          // Act
          final result = await repository.isFlexibleUpdateDownloaded(
            mockUpdateInfo,
          );

          // Assert
          expect(result, isA<Right<AppException, bool>>());
          expect((result as Right<AppException, bool>).value, isFalse);
        },
      );

      test(
        'should return Left with AppException when checking flexible update status fails',
        () async {
          // Arrange
          final mockUpdateInfo = MockAppUpdateInfo();
          when(
            () => mockUpdateInfo.installStatus,
          ).thenThrow(Exception('Status check failed'));

          // Act
          final result = await repository.isFlexibleUpdateDownloaded(
            mockUpdateInfo,
          );

          // Assert
          expect(result, isA<Left<AppException, bool>>());
          expect(
            (result as Left<AppException, bool>).value.identifier,
            equals('check_flexible_update_status_failed'),
          );
        },
      );
    });

    group('isCriticalUpdate', () {
      test(
        'should return true for critical update (version difference > 5)',
        () async {
          // Arrange
          final mockUpdateInfo = MockAppUpdateInfo();
          when(() => mockUpdateInfo.availableVersionCode).thenReturn(10);
          when(() => mockPlatformService.isAndroid).thenReturn(true);
          when(() => mockPlatformService.isIOS).thenReturn(false);
          when(
            () => mockPackageInfoService.getBuildNumber(),
          ).thenAnswer((_) async => '1');

          // Act
          final result = await repository.isCriticalUpdate(mockUpdateInfo);

          // Assert
          expect(result, isA<Right<AppException, bool>>());
          expect((result as Right<AppException, bool>).value, isTrue);
        },
      );

      test(
        'should return false for normal update (version difference <= 5)',
        () async {
          // Arrange
          final mockUpdateInfo = MockAppUpdateInfo();
          when(() => mockUpdateInfo.availableVersionCode).thenReturn(3);
          when(() => mockPlatformService.isAndroid).thenReturn(true);
          when(() => mockPlatformService.isIOS).thenReturn(false);
          when(
            () => mockPackageInfoService.getBuildNumber(),
          ).thenAnswer((_) async => '1');

          // Act
          final result = await repository.isCriticalUpdate(mockUpdateInfo);

          // Assert
          expect(result, isA<Right<AppException, bool>>());
          expect((result as Right<AppException, bool>).value, isFalse);
        },
      );

      test('should return false when available version code is null', () async {
        // Arrange
        final mockUpdateInfo = MockAppUpdateInfo();
        when(() => mockUpdateInfo.availableVersionCode).thenReturn(null);
        when(() => mockPlatformService.isAndroid).thenReturn(true);
        when(() => mockPlatformService.isIOS).thenReturn(false);
        when(
          () => mockPackageInfoService.getBuildNumber(),
        ).thenAnswer((_) async => '1');

        // Act
        final result = await repository.isCriticalUpdate(mockUpdateInfo);

        // Assert
        expect(result, isA<Right<AppException, bool>>());
        expect((result as Right<AppException, bool>).value, isFalse);
      });

      test(
        'should return Left with AppException when getting current version fails',
        () async {
          // Arrange
          final mockUpdateInfo = MockAppUpdateInfo();
          when(() => mockUpdateInfo.availableVersionCode).thenReturn(10);
          when(() => mockPlatformService.isAndroid).thenReturn(true);
          when(() => mockPlatformService.isIOS).thenReturn(false);
          when(
            () => mockPackageInfoService.getBuildNumber(),
          ).thenThrow(Exception('Failed to get build number'));

          // Act
          final result = await repository.isCriticalUpdate(mockUpdateInfo);

          // Assert
          expect(result, isA<Left<AppException, bool>>());
          expect(
            (result as Left<AppException, bool>).value.identifier,
            equals('get_version_code_failed'),
          );
        },
      );
    });

    group('isNormalUpdate', () {
      test('should return true for normal update', () async {
        // Arrange
        final mockUpdateInfo = MockAppUpdateInfo();
        when(() => mockUpdateInfo.availableVersionCode).thenReturn(3);
        when(() => mockPlatformService.isAndroid).thenReturn(true);
        when(() => mockPlatformService.isIOS).thenReturn(false);
        when(
          () => mockPackageInfoService.getBuildNumber(),
        ).thenAnswer((_) async => '1');

        // Act
        final result = await repository.isNormalUpdate(mockUpdateInfo);

        // Assert
        expect(result, isA<Right<AppException, bool>>());
        expect((result as Right<AppException, bool>).value, isTrue);
      });

      test('should return false for critical update', () async {
        // Arrange
        final mockUpdateInfo = MockAppUpdateInfo();
        when(() => mockUpdateInfo.availableVersionCode).thenReturn(10);
        when(() => mockPlatformService.isAndroid).thenReturn(true);
        when(() => mockPlatformService.isIOS).thenReturn(false);
        when(
          () => mockPackageInfoService.getBuildNumber(),
        ).thenAnswer((_) async => '1');

        // Act
        final result = await repository.isNormalUpdate(mockUpdateInfo);

        // Assert
        expect(result, isA<Right<AppException, bool>>());
        expect((result as Right<AppException, bool>).value, isFalse);
      });

      test(
        'should return Left with AppException when isCriticalUpdate fails',
        () async {
          // Arrange
          final mockUpdateInfo = MockAppUpdateInfo();
          when(() => mockUpdateInfo.availableVersionCode).thenReturn(10);
          when(() => mockPlatformService.isAndroid).thenReturn(true);
          when(() => mockPlatformService.isIOS).thenReturn(false);
          when(
            () => mockPackageInfoService.getBuildNumber(),
          ).thenThrow(Exception('Failed to get build number'));

          // Act
          final result = await repository.isNormalUpdate(mockUpdateInfo);

          // Assert
          expect(result, isA<Left<AppException, bool>>());
          expect(
            (result as Left<AppException, bool>).value.identifier,
            equals('get_version_code_failed'),
          );
        },
      );
    });

    group('getUpdatePriority', () {
      test('should return 5 for critical updates', () async {
        // Arrange
        final mockUpdateInfo = MockAppUpdateInfo();
        when(() => mockUpdateInfo.availableVersionCode).thenReturn(10);
        when(() => mockPlatformService.isAndroid).thenReturn(true);
        when(() => mockPlatformService.isIOS).thenReturn(false);
        when(
          () => mockPackageInfoService.getBuildNumber(),
        ).thenAnswer((_) async => '1');

        // Act
        final result = await repository.getUpdatePriority(mockUpdateInfo);

        // Assert
        expect(result, isA<Right<AppException, int>>());
        expect((result as Right<AppException, int>).value, equals(5));
      });

      test('should return 2 for normal updates', () async {
        // Arrange
        final mockUpdateInfo = MockAppUpdateInfo();
        when(() => mockUpdateInfo.availableVersionCode).thenReturn(3);
        when(() => mockPlatformService.isAndroid).thenReturn(true);
        when(() => mockPlatformService.isIOS).thenReturn(false);
        when(
          () => mockPackageInfoService.getBuildNumber(),
        ).thenAnswer((_) async => '1');

        // Act
        final result = await repository.getUpdatePriority(mockUpdateInfo);

        // Assert
        expect(result, isA<Right<AppException, int>>());
        expect((result as Right<AppException, int>).value, equals(2));
      });

      test(
        'should return Left with AppException when isCriticalUpdate fails',
        () async {
          // Arrange
          final mockUpdateInfo = MockAppUpdateInfo();
          when(() => mockUpdateInfo.availableVersionCode).thenReturn(10);
          when(() => mockPlatformService.isAndroid).thenReturn(true);
          when(() => mockPlatformService.isIOS).thenReturn(false);
          when(
            () => mockPackageInfoService.getBuildNumber(),
          ).thenThrow(Exception('Failed to get build number'));

          // Act
          final result = await repository.getUpdatePriority(mockUpdateInfo);

          // Assert
          expect(result, isA<Left<AppException, int>>());
          expect(
            (result as Left<AppException, int>).value.identifier,
            equals('get_version_code_failed'),
          );
        },
      );
    });

    group('isHighPriorityUpdate', () {
      test('should return true for critical updates', () async {
        // Arrange
        final mockUpdateInfo = MockAppUpdateInfo();
        when(() => mockUpdateInfo.availableVersionCode).thenReturn(10);
        when(() => mockPlatformService.isAndroid).thenReturn(true);
        when(() => mockPlatformService.isIOS).thenReturn(false);
        when(
          () => mockPackageInfoService.getBuildNumber(),
        ).thenAnswer((_) async => '1');

        // Act
        final result = await repository.isHighPriorityUpdate(mockUpdateInfo);

        // Assert
        expect(result, isA<Right<AppException, bool>>());
        expect((result as Right<AppException, bool>).value, isTrue);
      });

      test('should return false for normal updates', () async {
        // Arrange
        final mockUpdateInfo = MockAppUpdateInfo();
        when(() => mockUpdateInfo.availableVersionCode).thenReturn(3);
        when(() => mockPlatformService.isAndroid).thenReturn(true);
        when(() => mockPlatformService.isIOS).thenReturn(false);
        when(
          () => mockPackageInfoService.getBuildNumber(),
        ).thenAnswer((_) async => '1');

        // Act
        final result = await repository.isHighPriorityUpdate(mockUpdateInfo);

        // Assert
        expect(result, isA<Right<AppException, bool>>());
        expect((result as Right<AppException, bool>).value, isFalse);
      });

      test(
        'should return Left with AppException when isCriticalUpdate fails',
        () async {
          // Arrange
          final mockUpdateInfo = MockAppUpdateInfo();
          when(() => mockUpdateInfo.availableVersionCode).thenReturn(10);
          when(() => mockPlatformService.isAndroid).thenReturn(true);
          when(() => mockPlatformService.isIOS).thenReturn(false);
          when(
            () => mockPackageInfoService.getBuildNumber(),
          ).thenThrow(Exception('Failed to get build number'));

          // Act
          final result = await repository.isHighPriorityUpdate(mockUpdateInfo);

          // Assert
          expect(result, isA<Left<AppException, bool>>());
          expect(
            (result as Left<AppException, bool>).value.identifier,
            equals('get_version_code_failed'),
          );
        },
      );
    });

    group('Utility Methods', () {
      test('isUpdateAvailable should return true when update is available', () {
        // Arrange
        final mockUpdateInfo = MockAppUpdateInfo();
        when(
          () => mockUpdateInfo.updateAvailability,
        ).thenReturn(UpdateAvailability.updateAvailable);

        // Act
        final result = repository.isUpdateAvailable(mockUpdateInfo);

        // Assert
        expect(result, isTrue);
      });

      test(
        'isUpdateAvailable should return false when no update is available',
        () {
          // Arrange
          final mockUpdateInfo = MockAppUpdateInfo();
          when(
            () => mockUpdateInfo.updateAvailability,
          ).thenReturn(UpdateAvailability.unknown);

          // Act
          final result = repository.isUpdateAvailable(mockUpdateInfo);

          // Assert
          expect(result, isFalse);
        },
      );

      test('isImmediateUpdateAllowed should return true when allowed', () {
        // Arrange
        final mockUpdateInfo = MockAppUpdateInfo();
        when(() => mockUpdateInfo.immediateUpdateAllowed).thenReturn(true);

        // Act
        final result = repository.isImmediateUpdateAllowed(mockUpdateInfo);

        // Assert
        expect(result, isTrue);
      });

      test('isImmediateUpdateAllowed should return false when not allowed', () {
        // Arrange
        final mockUpdateInfo = MockAppUpdateInfo();
        when(() => mockUpdateInfo.immediateUpdateAllowed).thenReturn(false);

        // Act
        final result = repository.isImmediateUpdateAllowed(mockUpdateInfo);

        // Assert
        expect(result, isFalse);
      });

      test('isFlexibleUpdateAllowed should return true when allowed', () {
        // Arrange
        final mockUpdateInfo = MockAppUpdateInfo();
        when(() => mockUpdateInfo.flexibleUpdateAllowed).thenReturn(true);

        // Act
        final result = repository.isFlexibleUpdateAllowed(mockUpdateInfo);

        // Assert
        expect(result, isTrue);
      });

      test('isFlexibleUpdateAllowed should return false when not allowed', () {
        // Arrange
        final mockUpdateInfo = MockAppUpdateInfo();
        when(() => mockUpdateInfo.flexibleUpdateAllowed).thenReturn(false);

        // Act
        final result = repository.isFlexibleUpdateAllowed(mockUpdateInfo);

        // Assert
        expect(result, isFalse);
      });
    });

    group('initialize - Error Scenarios', () {
      test(
        'should return Left with AppException when package info service fails',
        () async {
          // Arrange
          when(() => mockPlatformService.isAndroid).thenReturn(true);
          when(() => mockPlatformService.isIOS).thenReturn(false);
          when(
            () => mockPackageInfoService.getBuildNumber(),
          ).thenThrow(Exception('Failed to get build number'));

          // Act
          final result = await repository.initialize();

          // Assert
          expect(result, isA<Left<AppException, void>>());
          expect(
            (result as Left<AppException, void>).value.identifier,
            equals('in_app_update_init_error'),
          );
        },
      );
    });

    group('checkForUpdate - Additional Edge Cases', () {
      test('should handle update with install status correctly', () async {
        // Arrange
        final mockUpdateInfo = MockAppUpdateInfo();
        when(
          () => mockUpdateInfo.updateAvailability,
        ).thenReturn(UpdateAvailability.updateAvailable);
        when(() => mockUpdateInfo.availableVersionCode).thenReturn(2);
        when(() => mockUpdateInfo.updatePriority).thenReturn(3);
        when(() => mockUpdateInfo.immediateUpdateAllowed).thenReturn(true);
        when(() => mockUpdateInfo.flexibleUpdateAllowed).thenReturn(true);
        when(
          () => mockUpdateInfo.installStatus,
        ).thenReturn(InstallStatus.downloaded);

        when(() => mockPlatformService.isAndroid).thenReturn(true);
        when(() => mockPlatformService.isIOS).thenReturn(false);
        when(
          () => mockInAppUpdateService.checkForUpdate(),
        ).thenAnswer((_) async => mockUpdateInfo);

        // Act
        final result = await repository.checkForUpdate();

        // Assert
        expect(result, isA<Right<AppException, AppUpdateInfo?>>());
        expect(
          (result as Right<AppException, AppUpdateInfo?>).value,
          equals(mockUpdateInfo),
        );
        expect(repository.isUpdateAvailable(mockUpdateInfo), isTrue);
      });

      test('should handle update with different update priorities', () async {
        // Arrange
        final mockUpdateInfo = MockAppUpdateInfo();
        when(
          () => mockUpdateInfo.updateAvailability,
        ).thenReturn(UpdateAvailability.updateAvailable);
        when(() => mockUpdateInfo.availableVersionCode).thenReturn(2);
        when(
          () => mockUpdateInfo.updatePriority,
        ).thenReturn(5); // High priority
        when(() => mockUpdateInfo.immediateUpdateAllowed).thenReturn(true);
        when(() => mockUpdateInfo.flexibleUpdateAllowed).thenReturn(true);

        when(() => mockPlatformService.isAndroid).thenReturn(true);
        when(() => mockPlatformService.isIOS).thenReturn(false);
        when(
          () => mockInAppUpdateService.checkForUpdate(),
        ).thenAnswer((_) async => mockUpdateInfo);

        // Act
        final result = await repository.checkForUpdate();

        // Assert
        expect(result, isA<Right<AppException, AppUpdateInfo?>>());
        expect(
          (result as Right<AppException, AppUpdateInfo?>).value,
          equals(mockUpdateInfo),
        );
      });
    });
  });
}
