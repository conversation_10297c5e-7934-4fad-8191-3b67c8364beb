import 'package:flutter_test/flutter_test.dart';
import 'package:selfeng/services/in_app_update_service/presentation/controllers/in_app_update_controller.dart';

void main() {
  group('InAppUpdateController', () {
    group('InAppUpdateState', () {
      test('should initialize with default values', () {
        // Act
        const state = InAppUpdateState();

        // Assert
        expect(state.isLoading, false);
        expect(state.isUpdateAvailable, false);
        expect(state.isUpdateDownloaded, false);
        expect(state.isHighPriority, false);
        expect(state.updateInfo, null);
        expect(state.error, null);
      });

      test('copyWith should create new state with updated values', () {
        // Arrange
        const originalState = InAppUpdateState();

        // Act
        final newState = originalState.copyWith(
          isLoading: true,
          error: 'Test error',
        );

        // Assert
        expect(newState.isLoading, true);
        expect(newState.error, 'Test error');
        expect(newState.isUpdateAvailable, false); // unchanged
        expect(newState.isUpdateDownloaded, false); // unchanged
        expect(newState.isHighPriority, false); // unchanged
        expect(newState.updateInfo, null); // unchanged
      });

      test('copyWith should preserve existing values when not specified', () {
        // Arrange
        const originalState = InAppUpdateState(
          isLoading: true,
          isUpdateAvailable: true,
          error: 'Original error',
        );

        // Act
        final newState = originalState.copyWith(isHighPriority: true);

        // Assert
        expect(newState.isLoading, true); // preserved
        expect(newState.isUpdateAvailable, true); // preserved
        expect(newState.error, 'Original error'); // preserved
        expect(newState.isHighPriority, true); // updated
      });

      test('copyWith should use null-coalescing behavior', () {
        // Arrange
        const originalState = InAppUpdateState(error: 'Some error');

        // Act - passing null should preserve original value due to ?? operator
        final newState = originalState.copyWith(error: null);

        // Assert - null values are ignored, original values preserved
        expect(newState.error, 'Some error');
      });
    });

    group('Controller Initialization', () {
      test('should not throw during state creation', () {
        // This test verifies that the state can be created
        // without throwing errors, which validates our fix
        expect(() {
          const InAppUpdateState();
        }, returnsNormally);
      });

      test('state should be immutable', () {
        // Arrange
        const state1 = InAppUpdateState();
        const state2 = InAppUpdateState();

        // Assert - same values should be equal
        expect(state1.isLoading, equals(state2.isLoading));
        expect(state1.isUpdateAvailable, equals(state2.isUpdateAvailable));
        expect(state1.isUpdateDownloaded, equals(state2.isUpdateDownloaded));
        expect(state1.isHighPriority, equals(state2.isHighPriority));
        expect(state1.updateInfo, equals(state2.updateInfo));
        expect(state1.error, equals(state2.error));
      });
    });
  });
}
