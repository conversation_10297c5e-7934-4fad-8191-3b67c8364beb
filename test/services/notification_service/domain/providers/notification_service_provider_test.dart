import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:selfeng/services/crashlytics_service/domain/providers/crashlytics_service_provider.dart';
import 'package:selfeng/services/fcm_service/domain/providers/fcm_service_provider.dart';
import 'package:selfeng/services/notification_service/domain/providers/notification_service_provider.dart';
import 'package:selfeng/services/notification_service/domain/repositories/notification_service_repository.dart';
import '../../../../mocks/repository_mocks.dart';
import '../../../../mocks/firebase_mocks.dart';

void main() {
  late ProviderContainer container;
  late MockFCMServiceRepository mockFCMService;
  late MockFirebaseMessaging mockFirebaseMessaging;
  late MockCrashlyticsRepository mockCrashlyticsRepository;

  setUp(() {
    mockFCMService = MockFCMServiceRepository();
    mockFirebaseMessaging = MockFirebaseMessaging();
    mockCrashlyticsRepository = MockCrashlyticsRepository();

    container = ProviderContainer(
      overrides: [
        fcmServiceProvider.overrideWithValue(mockFCMService),
        firebaseMessagingProvider.overrideWithValue(mockFirebaseMessaging),
        crashlyticsServiceProvider.overrideWithValue(mockCrashlyticsRepository),
      ],
    );
  });

  tearDown(() {
    container.dispose();
  });

  group('NotificationServiceProvider Tests', () {
    group('Provider Definition and Structure', () {
      test(
        'should be defined as a Provider<NotificationServiceRepository>',
        () {
          // Act & Assert
          expect(
            notificationServiceProvider,
            isA<Provider<NotificationServiceRepository>>(),
          );
        },
      );

      test('should have correct provider name', () {
        // Act & Assert
        expect(
          notificationServiceProvider.name,
          isNull,
        ); // Default provider name
      });

      test('should have correct provider arguments', () {
        // Act & Assert
        expect(
          notificationServiceProvider.argument,
          isNull,
        ); // No arguments expected
      });

      test('should be a synchronous provider', () {
        // Act & Assert
        expect(
          notificationServiceProvider,
          isA<Provider<NotificationServiceRepository>>(),
        );
        expect(notificationServiceProvider, isNot(isA<FutureProvider>()));
        expect(notificationServiceProvider, isNot(isA<StreamProvider>()));
      });

      test('should have correct dependencies', () {
        // Act & Assert - Provider should depend on FCM service and Firebase messaging
        expect(
          notificationServiceProvider,
          isA<Provider<NotificationServiceRepository>>(),
        );
        // The provider function should watch the required dependencies
      });
    });

    group('Provider Resolution and Instance Creation', () {
      test('should resolve to NotificationServiceRepository instance', () {
        // Act
        final repository = container.read(notificationServiceProvider);

        // Assert
        expect(repository, isA<NotificationServiceRepository>());
        expect(repository, isNotNull);
      });

      test('should return NotificationServiceRepository instance', () {
        // Act
        final repository = container.read(notificationServiceProvider);

        // Assert
        expect(repository, isA<NotificationServiceRepository>());
        expect(repository.runtimeType, NotificationServiceRepository);
      });

      test('should return same instance on each read (Provider caching)', () {
        // Act
        final repository1 = container.read(notificationServiceProvider);
        final repository2 = container.read(notificationServiceProvider);
        final repository3 = container.read(notificationServiceProvider);

        // Assert - Riverpod Provider caches the result, so same instance is returned
        expect(repository1, same(repository2));
        expect(repository2, same(repository3));
        expect(repository1, same(repository3));

        // But all should be NotificationServiceRepository instances
        expect(repository1, isA<NotificationServiceRepository>());
        expect(repository2, isA<NotificationServiceRepository>());
        expect(repository3, isA<NotificationServiceRepository>());
      });

      test('should create repository with correct dependencies', () {
        // Act
        final repository = container.read(notificationServiceProvider);

        // Assert
        expect(repository, isA<NotificationServiceRepository>());
        // The repository should be created with FCM service and Firebase messaging instances
      });
    });

    group('Dependency Injection and Provider Integration', () {
      test('should watch fcmServiceProvider dependency', () {
        // Act
        final fcmService = container.read(fcmServiceProvider);
        final repository = container.read(notificationServiceProvider);

        // Assert
        expect(fcmService, same(mockFCMService));
        expect(repository, isA<NotificationServiceRepository>());
        // The repository should use the FCM service from the provider
      });

      test('should watch firebaseMessagingProvider dependency', () {
        // Act
        final firebaseMessaging = container.read(firebaseMessagingProvider);
        final repository = container.read(notificationServiceProvider);

        // Assert
        expect(firebaseMessaging, same(mockFirebaseMessaging));
        expect(repository, isA<NotificationServiceRepository>());
        // The repository should use Firebase messaging from the provider
      });

      test('should work with provider overrides', () {
        // Arrange
        final mockFCMService = MockFCMServiceRepository();
        final mockFirebaseMessaging = MockFirebaseMessaging();
        final mockRepository = MockNotificationServiceRepository();

        final fcmOverride = fcmServiceProvider.overrideWithValue(
          mockFCMService,
        );
        final firebaseOverride = firebaseMessagingProvider.overrideWithValue(
          mockFirebaseMessaging,
        );
        final repositoryOverride = notificationServiceProvider
            .overrideWithValue(mockRepository);

        final testContainer = ProviderContainer(
          overrides: [fcmOverride, firebaseOverride, repositoryOverride],
        );

        // Act
        final repository = testContainer.read(notificationServiceProvider);

        // Assert
        expect(repository, same(mockRepository));
        expect(repository, isA<NotificationServiceRepository>());

        // Cleanup
        testContainer.dispose();
      });

      test('should handle dependency failures gracefully', () {
        // This test verifies that if dependencies fail, the provider handles it appropriately
        // In a real scenario, this would test error propagation from dependencies

        // Act
        final repository = container.read(notificationServiceProvider);

        // Assert
        expect(repository, isA<NotificationServiceRepository>());
        // Repository should be created successfully with working dependencies
      });
    });

    group('Provider Behavior and Lifecycle', () {
      test('should work correctly with ProviderScope', () {
        // Act
        final repository = container.read(notificationServiceProvider);

        // Assert
        expect(repository, isA<NotificationServiceRepository>());
        // Should work seamlessly within ProviderScope
      });

      test('should handle multiple container instances', () {
        // Act
        final container1 = ProviderContainer(
          overrides: [
            fcmServiceProvider.overrideWithValue(mockFCMService),
            firebaseMessagingProvider.overrideWithValue(mockFirebaseMessaging),
            crashlyticsServiceProvider.overrideWithValue(
              mockCrashlyticsRepository,
            ),
          ],
        );
        final container2 = ProviderContainer(
          overrides: [
            fcmServiceProvider.overrideWithValue(mockFCMService),
            firebaseMessagingProvider.overrideWithValue(mockFirebaseMessaging),
            crashlyticsServiceProvider.overrideWithValue(
              mockCrashlyticsRepository,
            ),
          ],
        );

        final repository1 = container1.read(notificationServiceProvider);
        final repository2 = container2.read(notificationServiceProvider);

        // Assert
        expect(repository1, isA<NotificationServiceRepository>());
        expect(repository2, isA<NotificationServiceRepository>());
        expect(
          repository1,
          isNot(same(repository2)),
        ); // Different containers, different instances

        // Cleanup
        container1.dispose();
        container2.dispose();
      });

      test('should work with provider dependency overrides', () {
        // Arrange
        final mockFCMService = MockFCMServiceRepository();
        final mockFirebaseMessaging = MockFirebaseMessaging();
        final mockCrashlyticsRepository = MockCrashlyticsRepository();

        final fcmOverride = fcmServiceProvider.overrideWithValue(
          mockFCMService,
        );
        final firebaseOverride = firebaseMessagingProvider.overrideWithValue(
          mockFirebaseMessaging,
        );
        final crashlyticsOverride = crashlyticsServiceProvider
            .overrideWithValue(mockCrashlyticsRepository);

        final testContainer = ProviderContainer(
          overrides: [fcmOverride, firebaseOverride, crashlyticsOverride],
        );

        // Act
        final repository = testContainer.read(notificationServiceProvider);

        // Assert
        expect(repository, isA<NotificationServiceRepository>());
        // Repository should be created with the mocked dependencies

        // Cleanup
        testContainer.dispose();
      });

      test('should handle provider disposal gracefully', () {
        // Act
        final repository = container.read(notificationServiceProvider);

        // Assert - Repository should still work after container operations
        expect(repository, isA<NotificationServiceRepository>());
      });
    });

    group('Provider Dependencies and Integration', () {
      test(
        'should not depend on other providers beyond FCM and Firebase messaging',
        () {
          // Act
          final repository = container.read(notificationServiceProvider);

          // Assert - Should work without any additional dependencies
          expect(repository, isA<NotificationServiceRepository>());
        },
      );

      test('should work in isolation from other providers', () {
        // Act - Read the provider multiple times
        final repositories = List.generate(
          5,
          (_) => container.read(notificationServiceProvider),
        );

        // Assert - All repositories should be valid and independent
        for (final repository in repositories) {
          expect(repository, isA<NotificationServiceRepository>());
        }

        // Verify they are the same instance (due to Provider caching)
        for (int i = 0; i < repositories.length - 1; i++) {
          for (int j = i + 1; j < repositories.length; j++) {
            expect(repositories[i], same(repositories[j]));
          }
        }
      });

      test('should maintain repository interface contract', () {
        // Act
        final repository = container.read(notificationServiceProvider);

        // Assert - Should work seamlessly with repository interface
        expect(repository, isA<NotificationServiceRepository>());
      });

      test('should integrate properly with FCM service provider', () {
        // Act
        final fcmService = container.read(fcmServiceProvider);
        final repository = container.read(notificationServiceProvider);

        // Assert
        expect(fcmService, same(mockFCMService));
        expect(repository, isA<NotificationServiceRepository>());
        // Both providers should work together seamlessly
      });

      test('should integrate properly with Firebase messaging provider', () {
        // Act
        final firebaseMessaging = container.read(firebaseMessagingProvider);
        final repository = container.read(notificationServiceProvider);

        // Assert
        expect(firebaseMessaging, same(mockFirebaseMessaging));
        expect(repository, isA<NotificationServiceRepository>());
        // Both providers should work together seamlessly
      });
    });

    group('Error Handling and Edge Cases', () {
      test('should handle rapid consecutive reads', () {
        // Act - Rapid reads to test for any performance or stability issues
        final repositories = <NotificationServiceRepository>[];
        for (int i = 0; i < 100; i++) {
          repositories.add(container.read(notificationServiceProvider));
        }

        // Assert - All repositories should be valid
        for (final repository in repositories) {
          expect(repository, isA<NotificationServiceRepository>());
        }

        // Verify all are the same instance (due to Provider caching)
        final uniqueRepositories = repositories.toSet();
        expect(uniqueRepositories.length, equals(1));
        expect(
          repositories.every((repo) => repo == repositories.first),
          isTrue,
        );
      });

      test('should work correctly in async context', () async {
        // Act - Test in async context
        NotificationServiceRepository? asyncRepository;

        await Future.microtask(() {
          asyncRepository = container.read(notificationServiceProvider);
        });

        // Assert
        expect(asyncRepository, isNotNull);
        expect(asyncRepository, isA<NotificationServiceRepository>());
      });

      test('should handle container recreation', () {
        // Act - Dispose and recreate container
        container.dispose();
        final newContainer = ProviderContainer(
          overrides: [
            fcmServiceProvider.overrideWithValue(mockFCMService),
            firebaseMessagingProvider.overrideWithValue(mockFirebaseMessaging),
            crashlyticsServiceProvider.overrideWithValue(
              mockCrashlyticsRepository,
            ),
          ],
        );

        final repository = newContainer.read(notificationServiceProvider);

        // Assert
        expect(repository, isA<NotificationServiceRepository>());

        // Cleanup
        newContainer.dispose();
      });

      test('should handle provider access after container disposal', () {
        // Act - Dispose container first
        container.dispose();

        // Assert - Should throw StateError when accessing disposed container
        expect(
          () => container.read(notificationServiceProvider),
          throwsA(isA<StateError>()),
        );
      });
    });

    group('Provider Documentation and Examples', () {
      test('should demonstrate typical provider usage pattern', () {
        // Act - Demonstrate common usage
        final notificationRepository = container.read(
          notificationServiceProvider,
        );
        final fcmService = container.read(fcmServiceProvider);
        final firebaseMessaging = container.read(firebaseMessagingProvider);

        // Assert - Verify the provider ecosystem works as expected
        expect(notificationRepository, isA<NotificationServiceRepository>());
        expect(fcmService, same(mockFCMService));
        expect(firebaseMessaging, same(mockFirebaseMessaging));
      });

      test('should provide clear notification service capabilities', () {
        // Act
        final repository = container.read(notificationServiceProvider);

        // Assert & Document - Provider should enable clear notification service access
        expect(
          repository,
          isA<NotificationServiceRepository>(),
          reason: 'Provider should enable notification service access',
        );
      });

      test('should support dependency injection patterns', () {
        // Act - Simulate dependency injection usage
        final injectedRepository = container.read(notificationServiceProvider);

        // Assert - Should work correctly when injected
        expect(injectedRepository, isA<NotificationServiceRepository>());
      });

      test('should demonstrate provider composition', () {
        // Act - Show how providers compose together
        final fcmService = container.read(fcmServiceProvider);
        final firebaseMessaging = container.read(firebaseMessagingProvider);
        final notificationRepository = container.read(
          notificationServiceProvider,
        );

        // Assert - All providers should work together
        expect(fcmService, same(mockFCMService));
        expect(firebaseMessaging, same(mockFirebaseMessaging));
        expect(notificationRepository, isA<NotificationServiceRepository>());
      });
    });

    group('Provider Testing Best Practices', () {
      test('should support unit testing with mocks', () {
        // Arrange
        final mockFCMService = MockFCMServiceRepository();
        final mockFirebaseMessaging = MockFirebaseMessaging();
        final mockRepository = MockNotificationServiceRepository();

        final fcmOverride = fcmServiceProvider.overrideWithValue(
          mockFCMService,
        );
        final firebaseOverride = firebaseMessagingProvider.overrideWithValue(
          mockFirebaseMessaging,
        );
        final repositoryOverride = notificationServiceProvider
            .overrideWithValue(mockRepository);

        final testContainer = ProviderContainer(
          overrides: [fcmOverride, firebaseOverride, repositoryOverride],
        );

        // Act
        final repository = testContainer.read(notificationServiceProvider);

        // Assert
        expect(repository, same(mockRepository));
        expect(repository, isA<NotificationServiceRepository>());

        // Cleanup
        testContainer.dispose();
      });

      test('should work with basic provider testing', () {
        // Act
        final repository = container.read(notificationServiceProvider);

        // Assert
        expect(repository, isA<NotificationServiceRepository>());
      });

      test('should support provider disposal testing', () {
        // Arrange
        final testContainer = ProviderContainer(
          overrides: [
            fcmServiceProvider.overrideWithValue(mockFCMService),
            firebaseMessagingProvider.overrideWithValue(mockFirebaseMessaging),
            crashlyticsServiceProvider.overrideWithValue(
              mockCrashlyticsRepository,
            ),
          ],
        );

        // Act
        final repository = testContainer.read(notificationServiceProvider);
        testContainer.dispose();

        // Assert - Should not throw after disposal
        expect(repository, isA<NotificationServiceRepository>());
      });

      test('should support dependency verification', () {
        // Act - Dependencies should be accessible
        expect(container.read(fcmServiceProvider), same(mockFCMService));
        expect(
          container.read(firebaseMessagingProvider),
          same(mockFirebaseMessaging),
        );
      });

      test('should support basic mocking scenarios', () {
        // Arrange - Create basic mock scenario
        final mockFCMService = MockFCMServiceRepository();
        final mockFirebaseMessaging = MockFirebaseMessaging();
        final mockCrashlyticsRepository = MockCrashlyticsRepository();

        final fcmOverride = fcmServiceProvider.overrideWithValue(
          mockFCMService,
        );
        final firebaseOverride = firebaseMessagingProvider.overrideWithValue(
          mockFirebaseMessaging,
        );
        final crashlyticsOverride = crashlyticsServiceProvider
            .overrideWithValue(mockCrashlyticsRepository);

        final testContainer = ProviderContainer(
          overrides: [fcmOverride, firebaseOverride, crashlyticsOverride],
        );

        // Act
        final repository = testContainer.read(notificationServiceProvider);

        // Assert
        expect(repository, isA<NotificationServiceRepository>());
        // Repository should be created with mocked dependencies

        // Cleanup
        testContainer.dispose();
      });
    });

    group('Provider Integration Testing', () {
      test('should work with complete provider ecosystem', () {
        // Act - Test the complete provider chain
        final fcmService = container.read(fcmServiceProvider);
        final firebaseMessaging = container.read(firebaseMessagingProvider);
        final notificationRepository = container.read(
          notificationServiceProvider,
        );

        // Assert - All parts of the ecosystem should work together
        expect(fcmService, same(mockFCMService));
        expect(firebaseMessaging, same(mockFirebaseMessaging));
        expect(notificationRepository, isA<NotificationServiceRepository>());

        // Verify the repository was created with the correct dependencies
        expect(notificationRepository, isNotNull);
      });

      test('should handle provider refresh scenarios', () {
        // Act - Read provider multiple times to simulate refresh
        final repository1 = container.read(notificationServiceProvider);
        final repository2 = container.read(notificationServiceProvider);

        // Assert - Should return the same instance (Provider behavior)
        expect(repository1, same(repository2));
        expect(repository1, isA<NotificationServiceRepository>());
        expect(repository2, isA<NotificationServiceRepository>());
      });

      test('should support provider family patterns if extended', () {
        // This test documents how the provider could be extended to support families
        // Currently it's a simple provider, but this shows the pattern for future extension

        // Act
        final repository = container.read(notificationServiceProvider);

        // Assert
        expect(repository, isA<NotificationServiceRepository>());
        // Provider should work consistently regardless of future extensions
      });
    });
  });
}
