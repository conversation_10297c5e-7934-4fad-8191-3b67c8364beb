import 'dart:convert';
import 'dart:ui';

import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:selfeng/services/setting_cache_service/data/datasource/setting_local_datasource.dart';
import 'package:selfeng/shared/exceptions/http_exception.dart';
import 'package:selfeng/shared/globals.dart';
import '../../../../helpers/mock_factories.dart';
import '../../../../mocks/service_mocks.dart';

/// Comprehensive test suite for SettingLocalDatasource
/// Tests all methods including locale and audio settings management
/// Follows the centralized mock system guidelines from test/README.md
void main() {
  group('SettingLocalDatasource Tests', () {
    late MockStorageService mockStorageService;
    late SettingLocalDatasource datasource;

    setUp(() {
      // Create mock using centralized factory
      mockStorageService = MockFactories.createMockStorageService();

      // Setup default mock behaviors
      MockFactories.setupStorageServiceMocks(mockStorageService);

      // Create datasource with mocked dependency
      datasource = SettingLocalDatasource(mockStorageService);
    });

    tearDown(() {
      // Reset mocks between tests
      reset(mockStorageService);
    });

    group('Storage Key Getters', () {
      test('should return correct locale storage key', () {
        // Act
        final key = datasource.storageKeyLocale;

        // Assert
        expect(key, equals(Locale_LOCAL_STORAGE_KEY));
      });

      test('should return correct audio enabled storage key', () {
        // Act
        final key = datasource.storageKeyAudioEnabled;

        // Assert
        expect(key, equals(AUDIO_ENABLED_LOCAL_STORAGE_KEY));
      });
    });

    group('fetchLocale', () {
      test('should return locale when data exists in storage', () async {
        // Arrange
        final testLocale = const Locale('en', 'US');
        final jsonString = '{"languageCode":"en","countryCode":"US"}';

        when(
          () => mockStorageService.get(Locale_LOCAL_STORAGE_KEY),
        ).thenAnswer((_) async => jsonString);

        // Act
        final result = await datasource.fetchLocale();

        // Assert
        expect(result.isRight(), isTrue);
        result.fold((error) => fail('Expected Right but got Left: $error'), (
          locale,
        ) {
          expect(locale.languageCode, equals(testLocale.languageCode));
          expect(locale.countryCode, equals(testLocale.countryCode));
        });

        // Verify
        verify(
          () => mockStorageService.get(Locale_LOCAL_STORAGE_KEY),
        ).called(1);
      });

      test('should return locale with null country code', () async {
        // Arrange
        final testLocale = const Locale('en');
        final jsonString = '{"languageCode":"en","countryCode":null}';

        when(
          () => mockStorageService.get(Locale_LOCAL_STORAGE_KEY),
        ).thenAnswer((_) async => jsonString);

        // Act
        final result = await datasource.fetchLocale();

        // Assert
        expect(result.isRight(), isTrue);
        result.fold((error) => fail('Expected Right but got Left: $error'), (
          locale,
        ) {
          expect(locale.languageCode, equals(testLocale.languageCode));
          expect(locale.countryCode, isNull);
        });
      });

      test('should return AppException when no locale data exists', () async {
        // Arrange
        when(
          () => mockStorageService.get(Locale_LOCAL_STORAGE_KEY),
        ).thenAnswer((_) async => null);

        // Act
        final result = await datasource.fetchLocale();

        // Assert
        expect(result.isLeft(), isTrue);
        result.fold((error) {
          expect(error, isA<AppException>());
          expect(error.statusCode, equals(404));
          expect(error.message, contains('Locale not found'));
          expect(error.identifier, equals('SettingLocalDatasource'));
        }, (locale) => fail('Expected Left but got Right: $locale'));
      });

      test(
        'should return AppException when locale data is invalid JSON',
        () async {
          // Arrange
          when(
            () => mockStorageService.get(Locale_LOCAL_STORAGE_KEY),
          ).thenAnswer((_) async => 'invalid json');

          // Act
          final result = await datasource.fetchLocale();

          // Assert
          expect(result.isLeft(), isTrue);
          result.fold((error) {
            expect(error, isA<AppException>());
            expect(error.statusCode, equals(500));
          }, (locale) => fail('Expected Left but got Right: $locale'));
        },
      );

      test(
        'should return AppException when locale data is missing languageCode',
        () async {
          // Arrange
          final jsonString = '{"countryCode":"US"}'; // Missing languageCode

          when(
            () => mockStorageService.get(Locale_LOCAL_STORAGE_KEY),
          ).thenAnswer((_) async => jsonString);

          // Act
          final result = await datasource.fetchLocale();

          // Assert
          expect(result.isLeft(), isTrue);
          result.fold((error) {
            expect(error, isA<AppException>());
            expect(error.statusCode, equals(400));
          }, (locale) => fail('Expected Left but got Right: $locale'));
        },
      );
    });

    group('saveLocale', () {
      test('should save locale successfully', () async {
        // Arrange
        final testLocale = const Locale('fr', 'FR');
        final expectedJson = {
          'languageCode': testLocale.languageCode,
          'countryCode': testLocale.countryCode,
        };

        when(
          () => mockStorageService.set(Locale_LOCAL_STORAGE_KEY, any()),
        ).thenAnswer((_) async => true);

        // Act
        final result = await datasource.saveLocale(locale: testLocale);

        // Assert
        expect(result, isTrue);

        // Verify the correct data was saved
        verify(
          () => mockStorageService.set(
            Locale_LOCAL_STORAGE_KEY,
            jsonEncode(expectedJson),
          ),
        ).called(1);
      });

      test('should save locale with null country code', () async {
        // Arrange
        final testLocale = const Locale('de');
        when(
          () => mockStorageService.set(Locale_LOCAL_STORAGE_KEY, any()),
        ).thenAnswer((_) async => true);

        // Act
        final result = await datasource.saveLocale(locale: testLocale);

        // Assert
        expect(result, isTrue);
        verify(
          () => mockStorageService.set(Locale_LOCAL_STORAGE_KEY, any()),
        ).called(1);
      });

      test('should return false when storage set fails', () async {
        // Arrange
        final testLocale = const Locale('es', 'ES');
        when(
          () => mockStorageService.set(Locale_LOCAL_STORAGE_KEY, any()),
        ).thenAnswer((_) async => false);

        // Act
        final result = await datasource.saveLocale(locale: testLocale);

        // Assert
        expect(result, isFalse);
      });
    });

    group('deleteLocale', () {
      test('should delete locale successfully', () async {
        // Arrange
        when(
          () => mockStorageService.remove(Locale_LOCAL_STORAGE_KEY),
        ).thenAnswer((_) async => true);

        // Act
        final result = await datasource.deleteLocale();

        // Assert
        expect(result, isTrue);
        verify(
          () => mockStorageService.remove(Locale_LOCAL_STORAGE_KEY),
        ).called(1);
      });

      test('should return false when storage remove fails', () async {
        // Arrange
        when(
          () => mockStorageService.remove(Locale_LOCAL_STORAGE_KEY),
        ).thenAnswer((_) async => false);

        // Act
        final result = await datasource.deleteLocale();

        // Assert
        expect(result, isFalse);
      });
    });

    group('hasLocale', () {
      test('should return true when locale exists', () async {
        // Arrange
        when(
          () => mockStorageService.has(Locale_LOCAL_STORAGE_KEY),
        ).thenAnswer((_) async => true);

        // Act
        final result = await datasource.hasLocale();

        // Assert
        expect(result, isTrue);
        verify(
          () => mockStorageService.has(Locale_LOCAL_STORAGE_KEY),
        ).called(1);
      });

      test('should return false when locale does not exist', () async {
        // Arrange
        when(
          () => mockStorageService.has(Locale_LOCAL_STORAGE_KEY),
        ).thenAnswer((_) async => false);

        // Act
        final result = await datasource.hasLocale();

        // Assert
        expect(result, isFalse);
      });
    });

    group('getAudioEnabled', () {
      test(
        'should return true when audio enabled data is null (default)',
        () async {
          // Arrange
          when(
            () => mockStorageService.get(AUDIO_ENABLED_LOCAL_STORAGE_KEY),
          ).thenAnswer((_) async => null);

          // Act
          final result = await datasource.getAudioEnabled();

          // Assert
          expect(result.isRight(), isTrue);
          result.fold(
            (error) => fail('Expected Right but got Left: $error'),
            (enabled) => expect(enabled, isTrue),
          );
        },
      );

      test(
        'should return true when audio enabled is stored as bool true',
        () async {
          // Arrange
          when(
            () => mockStorageService.get(AUDIO_ENABLED_LOCAL_STORAGE_KEY),
          ).thenAnswer((_) async => true);

          // Act
          final result = await datasource.getAudioEnabled();

          // Assert
          expect(result.isRight(), isTrue);
          result.fold(
            (error) => fail('Expected Right but got Left: $error'),
            (enabled) => expect(enabled, isTrue),
          );
        },
      );

      test(
        'should return false when audio enabled is stored as bool false',
        () async {
          // Arrange
          when(
            () => mockStorageService.get(AUDIO_ENABLED_LOCAL_STORAGE_KEY),
          ).thenAnswer((_) async => false);

          // Act
          final result = await datasource.getAudioEnabled();

          // Assert
          expect(result.isRight(), isTrue);
          result.fold(
            (error) => fail('Expected Right but got Left: $error'),
            (enabled) => expect(enabled, isFalse),
          );
        },
      );

      test(
        'should return true when audio enabled is stored as string "true"',
        () async {
          // Arrange
          when(
            () => mockStorageService.get(AUDIO_ENABLED_LOCAL_STORAGE_KEY),
          ).thenAnswer((_) async => 'true');

          // Act
          final result = await datasource.getAudioEnabled();

          // Assert
          expect(result.isRight(), isTrue);
          result.fold(
            (error) => fail('Expected Right but got Left: $error'),
            (enabled) => expect(enabled, isTrue),
          );
        },
      );

      test(
        'should return false when audio enabled is stored as string "false"',
        () async {
          // Arrange
          when(
            () => mockStorageService.get(AUDIO_ENABLED_LOCAL_STORAGE_KEY),
          ).thenAnswer((_) async => 'false');

          // Act
          final result = await datasource.getAudioEnabled();

          // Assert
          expect(result.isRight(), isTrue);
          result.fold(
            (error) => fail('Expected Right but got Left: $error'),
            (enabled) => expect(enabled, isFalse),
          );
        },
      );

      test(
        'should return true when audio enabled is stored as string "TRUE" (case insensitive)',
        () async {
          // Arrange
          when(
            () => mockStorageService.get(AUDIO_ENABLED_LOCAL_STORAGE_KEY),
          ).thenAnswer((_) async => 'TRUE');

          // Act
          final result = await datasource.getAudioEnabled();

          // Assert
          expect(result.isRight(), isTrue);
          result.fold(
            (error) => fail('Expected Right but got Left: $error'),
            (enabled) => expect(enabled, isTrue),
          );
        },
      );

      test(
        'should return true when audio enabled data type is unexpected',
        () async {
          // Arrange
          when(
            () => mockStorageService.get(AUDIO_ENABLED_LOCAL_STORAGE_KEY),
          ).thenAnswer((_) async => 123); // Unexpected type

          // Act
          final result = await datasource.getAudioEnabled();

          // Assert
          expect(result.isRight(), isTrue);
          result.fold(
            (error) => fail('Expected Right but got Left: $error'),
            (enabled) => expect(enabled, isTrue), // Should default to true
          );
        },
      );

      test('should return true when storage get throws exception', () async {
        // Arrange
        when(
          () => mockStorageService.get(AUDIO_ENABLED_LOCAL_STORAGE_KEY),
        ).thenThrow(Exception('Storage error'));

        // Act
        final result = await datasource.getAudioEnabled();

        // Assert
        expect(result.isRight(), isTrue);
        result.fold(
          (error) => fail('Expected Right but got Left: $error'),
          (enabled) => expect(enabled, isTrue), // Should default to true
        );
      });
    });

    group('saveAudioEnabled', () {
      test('should save audio enabled as true successfully', () async {
        // Arrange
        when(
          () => mockStorageService.set(AUDIO_ENABLED_LOCAL_STORAGE_KEY, any()),
        ).thenAnswer((_) async => true);

        // Act
        final result = await datasource.saveAudioEnabled(enabled: true);

        // Assert
        expect(result, isTrue);

        // Verify the correct string value was saved
        verify(
          () => mockStorageService.set(AUDIO_ENABLED_LOCAL_STORAGE_KEY, 'true'),
        ).called(1);
      });

      test('should save audio enabled as false successfully', () async {
        // Arrange
        when(
          () => mockStorageService.set(AUDIO_ENABLED_LOCAL_STORAGE_KEY, any()),
        ).thenAnswer((_) async => true);

        // Act
        final result = await datasource.saveAudioEnabled(enabled: false);

        // Assert
        expect(result, isTrue);

        // Verify the correct string value was saved
        verify(
          () =>
              mockStorageService.set(AUDIO_ENABLED_LOCAL_STORAGE_KEY, 'false'),
        ).called(1);
      });

      test('should return false when storage set fails', () async {
        // Arrange
        when(
          () => mockStorageService.set(AUDIO_ENABLED_LOCAL_STORAGE_KEY, any()),
        ).thenAnswer((_) async => false);

        // Act
        final result = await datasource.saveAudioEnabled(enabled: true);

        // Assert
        expect(result, isFalse);
      });
    });

    group('Integration Tests', () {
      test('should handle complete locale workflow', () async {
        // Arrange
        final testLocale = const Locale('ja', 'JP');
        final jsonString = '{"languageCode":"ja","countryCode":"JP"}';

        // Setup mocks for the complete workflow
        when(
          () => mockStorageService.set(Locale_LOCAL_STORAGE_KEY, any()),
        ).thenAnswer((_) async => true);
        when(
          () => mockStorageService.get(Locale_LOCAL_STORAGE_KEY),
        ).thenAnswer((_) async => jsonString);
        when(
          () => mockStorageService.has(Locale_LOCAL_STORAGE_KEY),
        ).thenAnswer((_) async => true);

        // Act & Assert - Save locale
        final saveResult = await datasource.saveLocale(locale: testLocale);
        expect(saveResult, isTrue);

        // Act & Assert - Check if exists
        final hasResult = await datasource.hasLocale();
        expect(hasResult, isTrue);

        // Act & Assert - Fetch locale
        final fetchResult = await datasource.fetchLocale();
        expect(fetchResult.isRight(), isTrue);
        fetchResult.fold(
          (error) => fail('Expected Right but got Left: $error'),
          (locale) {
            expect(locale.languageCode, equals(testLocale.languageCode));
            expect(locale.countryCode, equals(testLocale.countryCode));
          },
        );
      });

      test('should handle complete audio workflow', () async {
        // Arrange
        when(
          () => mockStorageService.set(AUDIO_ENABLED_LOCAL_STORAGE_KEY, any()),
        ).thenAnswer((_) async => true);
        when(
          () => mockStorageService.get(AUDIO_ENABLED_LOCAL_STORAGE_KEY),
        ).thenAnswer((_) async => 'false');

        // Act & Assert - Save audio setting
        final saveResult = await datasource.saveAudioEnabled(enabled: false);
        expect(saveResult, isTrue);

        // Act & Assert - Get audio setting
        final getResult = await datasource.getAudioEnabled();
        expect(getResult.isRight(), isTrue);
        getResult.fold(
          (error) => fail('Expected Right but got Left: $error'),
          (enabled) => expect(enabled, isFalse),
        );
      });
    });

    group('Error Handling', () {
      test('should handle storage service exceptions in fetchLocale', () async {
        // Arrange
        when(
          () => mockStorageService.get(Locale_LOCAL_STORAGE_KEY),
        ).thenThrow(Exception('Storage service error'));

        // Act
        final result = await datasource.fetchLocale();

        // Assert
        expect(result.isLeft(), isTrue);
        result.fold((error) {
          expect(error, isA<AppException>());
          expect(error.statusCode, equals(500));
        }, (locale) => fail('Expected Left but got Right: $locale'));
      });

      test('should handle storage service exceptions in saveLocale', () async {
        // Arrange
        final testLocale = const Locale('ko', 'KR');
        when(
          () => mockStorageService.set(Locale_LOCAL_STORAGE_KEY, any()),
        ).thenThrow(Exception('Storage service error'));

        // Act
        final result = await datasource.saveLocale(locale: testLocale);

        // Assert
        expect(result, isFalse);
      });

      test(
        'should handle storage service exceptions in deleteLocale',
        () async {
          // Arrange
          when(
            () => mockStorageService.remove(Locale_LOCAL_STORAGE_KEY),
          ).thenThrow(Exception('Storage service error'));

          // Act
          final result = await datasource.deleteLocale();

          // Assert
          expect(result, isFalse);
        },
      );

      test('should handle storage service exceptions in hasLocale', () async {
        // Arrange
        when(
          () => mockStorageService.has(Locale_LOCAL_STORAGE_KEY),
        ).thenThrow(Exception('Storage service error'));

        // Act
        final result = await datasource.hasLocale();

        // Assert
        expect(result, isFalse); // Should default to false on error
      });

      test(
        'should handle storage service exceptions in saveAudioEnabled',
        () async {
          // Arrange
          when(
            () =>
                mockStorageService.set(AUDIO_ENABLED_LOCAL_STORAGE_KEY, any()),
          ).thenThrow(Exception('Storage service error'));

          // Act
          final result = await datasource.saveAudioEnabled(enabled: true);

          // Assert
          expect(result, isFalse);
        },
      );
    });
  });
}
