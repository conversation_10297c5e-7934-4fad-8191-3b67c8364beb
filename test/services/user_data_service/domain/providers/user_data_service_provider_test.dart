import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Import the provider under test
import 'package:selfeng/services/user_data_service/domain/providers/user_data_service_provider.dart';

// Import dependencies
import 'package:selfeng/services/firestore_service_service/domain/providers/firestore_service_provider.dart';
import 'package:selfeng/services/user_data_service/domain/repositories/user_data_service_repository.dart';

// Import centralized mock system
import '../../../../mocks/repository_mocks.dart';

void main() {
  group('UserDataServiceProvider Tests', () {
    late ProviderContainer container;
    late MockFirestoreServiceRepository mockFirestoreRepository;

    setUp(() {
      mockFirestoreRepository = MockFirestoreServiceRepository();

      container = ProviderContainer(
        overrides: [
          firestoreServiceRepositoryProvider.overrideWithValue(
            mockFirestoreRepository,
          ),
        ],
      );
    });

    tearDown(() {
      container.dispose();
    });

    group('Provider Definition and Structure', () {
      test('should be defined as a Provider<UserDataServiceRepository>', () {
        expect(
          userDataServiceProvider,
          isA<Provider<UserDataServiceRepository>>(),
        );
      });

      test('should have correct provider name', () {
        expect(
          userDataServiceProvider.name,
          isNull, // Default provider name
        );
      });

      test('should have correct provider arguments', () {
        expect(
          userDataServiceProvider.argument,
          isNull, // No arguments expected
        );
      });

      test('should be a synchronous provider', () {
        expect(
          userDataServiceProvider,
          isA<Provider<UserDataServiceRepository>>(),
        );
        expect(userDataServiceProvider, isNot(isA<FutureProvider>()));
        expect(userDataServiceProvider, isNot(isA<StreamProvider>()));
      });

      test('should have correct dependencies', () {
        expect(
          userDataServiceProvider,
          isA<Provider<UserDataServiceRepository>>(),
        );
        // The provider function should watch the firestore service provider
      });
    });

    group('Provider Resolution and Instance Creation', () {
      test('should resolve to UserDataServiceRepository instance', () {
        final repository = container.read(userDataServiceProvider);

        expect(repository, isA<UserDataServiceRepository>());
        expect(repository, isNotNull);
      });

      test('should return UserDataServiceRepository instance', () {
        final repository = container.read(userDataServiceProvider);

        expect(repository, isA<UserDataServiceRepository>());
        expect(repository.runtimeType, UserDataServiceRepository);
      });

      test('should return same instance on each read (Provider caching)', () {
        final repository1 = container.read(userDataServiceProvider);
        final repository2 = container.read(userDataServiceProvider);
        final repository3 = container.read(userDataServiceProvider);

        expect(repository1, same(repository2));
        expect(repository2, same(repository3));
        expect(repository1, same(repository3));

        expect(repository1, isA<UserDataServiceRepository>());
        expect(repository2, isA<UserDataServiceRepository>());
        expect(repository3, isA<UserDataServiceRepository>());
      });

      test('should create repository with correct dependencies', () {
        final repository = container.read(userDataServiceProvider);

        expect(repository, isA<UserDataServiceRepository>());
        // The repository should be created with firestore service instance
      });
    });

    group('Dependency Injection and Provider Integration', () {
      test('should watch firestoreServiceRepositoryProvider dependency', () {
        final firestoreService = container.read(
          firestoreServiceRepositoryProvider,
        );
        final repository = container.read(userDataServiceProvider);

        expect(firestoreService, same(mockFirestoreRepository));
        expect(repository, isA<UserDataServiceRepository>());
      });

      test('should work with provider overrides', () {
        final mockFirestore = MockFirestoreServiceRepository();
        final mockRepository = MockUserDataServiceRepository();

        final firestoreOverride = firestoreServiceRepositoryProvider
            .overrideWithValue(mockFirestore);
        final repositoryOverride = userDataServiceProvider.overrideWithValue(
          mockRepository,
        );

        final testContainer = ProviderContainer(
          overrides: [firestoreOverride, repositoryOverride],
        );

        final repository = testContainer.read(userDataServiceProvider);

        expect(repository, same(mockRepository));
        expect(repository, isA<UserDataServiceRepository>());

        testContainer.dispose();
      });

      test('should handle dependency failures gracefully', () {
        final repository = container.read(userDataServiceProvider);

        expect(repository, isA<UserDataServiceRepository>());
        // Repository should be created successfully with working dependencies
      });
    });

    group('Provider Behavior and Lifecycle', () {
      test('should work correctly with ProviderScope', () {
        final repository = container.read(userDataServiceProvider);

        expect(repository, isA<UserDataServiceRepository>());
      });

      test('should handle multiple container instances', () {
        final container1 = ProviderContainer(
          overrides: [
            firestoreServiceRepositoryProvider.overrideWithValue(
              mockFirestoreRepository,
            ),
          ],
        );
        final container2 = ProviderContainer(
          overrides: [
            firestoreServiceRepositoryProvider.overrideWithValue(
              mockFirestoreRepository,
            ),
          ],
        );

        final repository1 = container1.read(userDataServiceProvider);
        final repository2 = container2.read(userDataServiceProvider);

        expect(repository1, isA<UserDataServiceRepository>());
        expect(repository2, isA<UserDataServiceRepository>());
        expect(
          repository1,
          isNot(same(repository2)),
        ); // Different containers, different instances

        container1.dispose();
        container2.dispose();
      });

      test('should work with provider dependency overrides', () {
        final mockFirestore = MockFirestoreServiceRepository();

        final firestoreOverride = firestoreServiceRepositoryProvider
            .overrideWithValue(mockFirestore);

        final testContainer = ProviderContainer(overrides: [firestoreOverride]);

        final repository = testContainer.read(userDataServiceProvider);

        expect(repository, isA<UserDataServiceRepository>());

        testContainer.dispose();
      });

      test('should handle provider disposal gracefully', () {
        final repository = container.read(userDataServiceProvider);

        expect(repository, isA<UserDataServiceRepository>());
      });
    });

    group('Provider Dependencies and Integration', () {
      test('should not depend on other providers beyond firestore service', () {
        final repository = container.read(userDataServiceProvider);

        expect(repository, isA<UserDataServiceRepository>());
      });

      test('should work in isolation from other providers', () {
        final repositories = List.generate(
          5,
          (_) => container.read(userDataServiceProvider),
        );

        for (final repository in repositories) {
          expect(repository, isA<UserDataServiceRepository>());
        }

        // Verify they are the same instance (due to Provider caching)
        for (int i = 0; i < repositories.length - 1; i++) {
          for (int j = i + 1; j < repositories.length; j++) {
            expect(repositories[i], same(repositories[j]));
          }
        }
      });

      test('should maintain repository interface contract', () {
        final repository = container.read(userDataServiceProvider);

        expect(repository, isA<UserDataServiceRepository>());
      });

      test('should integrate properly with firestore service provider', () {
        final firestoreService = container.read(
          firestoreServiceRepositoryProvider,
        );
        final repository = container.read(userDataServiceProvider);

        expect(firestoreService, same(mockFirestoreRepository));
        expect(repository, isA<UserDataServiceRepository>());
      });
    });

    group('Error Handling and Edge Cases', () {
      test('should handle rapid consecutive reads', () {
        final repositories = <UserDataServiceRepository>[];
        for (int i = 0; i < 100; i++) {
          repositories.add(container.read(userDataServiceProvider));
        }

        for (final repository in repositories) {
          expect(repository, isA<UserDataServiceRepository>());
        }

        final uniqueRepositories = repositories.toSet();
        expect(uniqueRepositories.length, equals(1));
        expect(
          repositories.every((repo) => repo == repositories.first),
          isTrue,
        );
      });

      test('should work correctly in async context', () async {
        UserDataServiceRepository? asyncRepository;

        await Future.microtask(() {
          asyncRepository = container.read(userDataServiceProvider);
        });

        expect(asyncRepository, isNotNull);
        expect(asyncRepository, isA<UserDataServiceRepository>());
      });

      test('should handle container recreation', () {
        container.dispose();
        final newContainer = ProviderContainer(
          overrides: [
            firestoreServiceRepositoryProvider.overrideWithValue(
              mockFirestoreRepository,
            ),
          ],
        );

        final repository = newContainer.read(userDataServiceProvider);

        expect(repository, isA<UserDataServiceRepository>());

        newContainer.dispose();
      });

      test('should handle provider access after container disposal', () {
        container.dispose();

        expect(
          () => container.read(userDataServiceProvider),
          throwsA(isA<StateError>()),
        );
      });
    });

    group('Provider Documentation and Examples', () {
      test('should demonstrate typical provider usage pattern', () {
        final userDataRepository = container.read(userDataServiceProvider);
        final firestoreService = container.read(
          firestoreServiceRepositoryProvider,
        );

        expect(userDataRepository, isA<UserDataServiceRepository>());
        expect(firestoreService, same(mockFirestoreRepository));
      });

      test('should provide clear user data service capabilities', () {
        final repository = container.read(userDataServiceProvider);

        expect(
          repository,
          isA<UserDataServiceRepository>(),
          reason: 'Provider should enable user data service access',
        );
      });

      test('should support dependency injection patterns', () {
        final injectedRepository = container.read(userDataServiceProvider);

        expect(injectedRepository, isA<UserDataServiceRepository>());
      });

      test('should demonstrate provider composition', () {
        final firestoreService = container.read(
          firestoreServiceRepositoryProvider,
        );
        final userDataRepository = container.read(userDataServiceProvider);

        expect(firestoreService, same(mockFirestoreRepository));
        expect(userDataRepository, isA<UserDataServiceRepository>());
      });
    });

    group('Provider Testing Best Practices', () {
      test('should support unit testing with mocks', () {
        final mockFirestore = MockFirestoreServiceRepository();
        final mockRepository = MockUserDataServiceRepository();

        final firestoreOverride = firestoreServiceRepositoryProvider
            .overrideWithValue(mockFirestore);
        final repositoryOverride = userDataServiceProvider.overrideWithValue(
          mockRepository,
        );

        final testContainer = ProviderContainer(
          overrides: [firestoreOverride, repositoryOverride],
        );

        final repository = testContainer.read(userDataServiceProvider);

        expect(repository, same(mockRepository));
        expect(repository, isA<UserDataServiceRepository>());

        testContainer.dispose();
      });

      test('should work with basic provider testing', () {
        final repository = container.read(userDataServiceProvider);

        expect(repository, isA<UserDataServiceRepository>());
      });

      test('should support provider disposal testing', () {
        final testContainer = ProviderContainer(
          overrides: [
            firestoreServiceRepositoryProvider.overrideWithValue(
              mockFirestoreRepository,
            ),
          ],
        );

        final repository = testContainer.read(userDataServiceProvider);
        testContainer.dispose();

        expect(repository, isA<UserDataServiceRepository>());
      });

      test('should support dependency verification', () {
        expect(
          container.read(firestoreServiceRepositoryProvider),
          same(mockFirestoreRepository),
        );
      });

      test('should support basic mocking scenarios', () {
        final mockFirestore = MockFirestoreServiceRepository();

        final firestoreOverride = firestoreServiceRepositoryProvider
            .overrideWithValue(mockFirestore);

        final testContainer = ProviderContainer(overrides: [firestoreOverride]);

        final repository = testContainer.read(userDataServiceProvider);

        expect(repository, isA<UserDataServiceRepository>());

        testContainer.dispose();
      });
    });

    group('Provider Integration Testing', () {
      test('should work with complete provider ecosystem', () {
        final firestoreService = container.read(
          firestoreServiceRepositoryProvider,
        );
        final userDataRepository = container.read(userDataServiceProvider);

        expect(firestoreService, same(mockFirestoreRepository));
        expect(userDataRepository, isA<UserDataServiceRepository>());

        expect(userDataRepository, isNotNull);
      });

      test('should handle provider refresh scenarios', () {
        final repository1 = container.read(userDataServiceProvider);
        final repository2 = container.read(userDataServiceProvider);

        expect(repository1, same(repository2));
        expect(repository1, isA<UserDataServiceRepository>());
        expect(repository2, isA<UserDataServiceRepository>());
      });

      test('should support provider family patterns if extended', () {
        final repository = container.read(userDataServiceProvider);

        expect(repository, isA<UserDataServiceRepository>());
      });
    });
  });
}
