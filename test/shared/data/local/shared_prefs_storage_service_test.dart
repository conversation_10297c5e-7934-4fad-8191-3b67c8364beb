import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:selfeng/shared/data/local/shared_prefs_storage_service.dart';
import 'package:selfeng/shared/data/local/storage_service.dart';

void main() {
  group('SharedPrefsService Tests', () {
    late SharedPrefsService sharedPrefsService;

    setUp(() {
      // Create service instance
      sharedPrefsService = SharedPrefsService();
    });

    group('Interface Implementation', () {
      test('should implement StorageService interface', () {
        expect(sharedPrefsService, isA<StorageService>());
      });

      test('should have hasInitialized property', () {
        expect(sharedPrefsService.hasInitialized, isA<bool>());
      });
    });

    group('Initialization', () {
      test(
        'should initialize SharedPreferences when init() is called',
        () async {
          // Setup mock values
          SharedPreferences.setMockInitialValues({});

          // Act
          sharedPrefsService.init();
          final prefs = await sharedPrefsService.initCompleter.future;

          // Assert
          expect(prefs, isA<SharedPreferences>());
          expect(sharedPrefsService.hasInitialized, isFalse);
        },
      );

      test('should handle initialization errors gracefully', () async {
        // This test verifies that the service can handle SharedPreferences errors
        // In a real scenario, SharedPreferences.getInstance() might fail
        SharedPreferences.setMockInitialValues({});

        sharedPrefsService.init();

        // The completer should complete successfully with mock SharedPreferences
        expect(sharedPrefsService.initCompleter.future, completes);
      });
    });

    group('Basic Operations with Mock Data', () {
      setUp(() async {
        // Setup mock initial values for each test
        SharedPreferences.setMockInitialValues({
          'existing_string': 'test_value',
          'existing_int': 42,
          'existing_bool': true,
          'existing_list': ['item1', 'item2'],
        });

        // Initialize the service
        sharedPrefsService.init();
        await sharedPrefsService.initCompleter.future;
      });

      test('should get existing string value', () async {
        // Act
        final result = await sharedPrefsService.get('existing_string');

        // Assert
        expect(result, equals('test_value'));
      });

      test('should get existing int value', () async {
        // Act
        final result = await sharedPrefsService.get('existing_int');

        // Assert
        expect(result, equals(42));
        expect(result, isA<int>());
      });

      test('should get existing bool value', () async {
        // Act
        final result = await sharedPrefsService.get('existing_bool');

        // Assert
        expect(result, isTrue);
        expect(result, isA<bool>());
      });

      test('should get existing list value', () async {
        // Act
        final result = await sharedPrefsService.get('existing_list');

        // Assert
        expect(result, equals(['item1', 'item2']));
        expect(result, isA<List<String>>());
      });

      test('should return null for non-existent key', () async {
        // Act
        final result = await sharedPrefsService.get('non_existent_key');

        // Assert
        expect(result, isNull);
      });

      test('should check if key exists', () async {
        // Act
        final exists = await sharedPrefsService.has('existing_string');
        final notExists = await sharedPrefsService.has('non_existent_key');

        // Assert
        expect(exists, isTrue);
        expect(notExists, isFalse);
      });

      test('should set string value successfully', () async {
        // Act
        final result = await sharedPrefsService.set('new_key', 'new_value');

        // Assert
        expect(result, isTrue);

        // Verify the value was set
        final retrieved = await sharedPrefsService.get('new_key');
        expect(retrieved, equals('new_value'));
      });

      test('should set hasInitialized to true after first operation', () async {
        // Initially should be false
        expect(sharedPrefsService.hasInitialized, isFalse);

        // After calling any method that uses sharedPreferences, it should be true
        await sharedPrefsService.get('existing_string');

        // Now should be true
        expect(sharedPrefsService.hasInitialized, isTrue);
      });

      test('should remove existing key', () async {
        // Act
        final result = await sharedPrefsService.remove('existing_string');

        // Assert
        expect(result, isTrue);

        // Verify the key was removed
        final exists = await sharedPrefsService.has('existing_string');
        expect(exists, isFalse);
      });

      test('should handle removing non-existent key', () async {
        // Act
        final result = await sharedPrefsService.remove('non_existent_key');

        // Assert
        expect(result, isTrue);
      });

      test('should clear all data', () async {
        // Act
        await sharedPrefsService.clear();

        // Assert - all keys should be gone
        final stringExists = await sharedPrefsService.has('existing_string');
        final intExists = await sharedPrefsService.has('existing_int');
        final boolExists = await sharedPrefsService.has('existing_bool');

        expect(stringExists, isFalse);
        expect(intExists, isFalse);
        expect(boolExists, isFalse);
      });
    });

    group('Data Type Handling', () {
      setUp(() async {
        SharedPreferences.setMockInitialValues({});
        sharedPrefsService.init();
        await sharedPrefsService.initCompleter.future;
      });

      test('should handle various string data types', () async {
        // Test different string values
        final testCases = {
          'simple_string': 'hello world',
          'number_string': '12345',
          'boolean_string': 'true',
          'json_string': '{"key": "value", "number": 42}',
          'empty_string': '',
          'special_chars': 'special@chars#here!',
        };

        for (final entry in testCases.entries) {
          // Act
          final setResult = await sharedPrefsService.set(
            entry.key,
            entry.value,
          );
          final getResult = await sharedPrefsService.get(entry.key);

          // Assert
          expect(setResult, isTrue, reason: 'Failed to set ${entry.key}');
          expect(
            getResult,
            equals(entry.value),
            reason: 'Wrong value for ${entry.key}',
          );
        }
      });

      test('should handle null data conversion', () async {
        // The interface expects String, so this test verifies the service handles string data
        // In the actual implementation, the service converts data to string using toString()
        final testValue = 'null_representation';

        // Act
        final result = await sharedPrefsService.set('null_test', testValue);

        // Assert
        expect(result, isTrue);
        final retrieved = await sharedPrefsService.get('null_test');
        expect(retrieved, equals(testValue));
      });
    });

    group('Error Scenarios', () {
      test(
        'should handle operations before initialization with timeout',
        () async {
          // Create a new service without calling init()
          final uninitializedService = SharedPrefsService();

          // These operations will hang because the completer is never completed
          // We test with a timeout to ensure they don't complete unexpectedly
          final getFuture = uninitializedService.get('test_key');
          final hasFuture = uninitializedService.has('test_key');

          // Use a short timeout to verify the operations don't complete immediately
          // (they should hang waiting for initialization)
          await Future.delayed(const Duration(milliseconds: 100));

          // Verify the futures are still pending (not completed)
          expect(getFuture, isNotNull);
          expect(hasFuture, isNotNull);

          // The operations should still be pending since init() was never called
          // In a real application, this would be an error condition
        },
      );
    });

    group('Concurrent Operations', () {
      setUp(() async {
        SharedPreferences.setMockInitialValues({});
        sharedPrefsService.init();
        await sharedPrefsService.initCompleter.future;
      });

      test('should handle multiple concurrent get operations', () async {
        // Setup test data
        await sharedPrefsService.set('concurrent_key', 'concurrent_value');

        // Act - perform multiple concurrent gets
        final futures = List.generate(
          10,
          (_) => sharedPrefsService.get('concurrent_key'),
        );
        final results = await Future.wait(futures);

        // Assert
        expect(results.length, equals(10));
        for (var result in results) {
          expect(result, equals('concurrent_value'));
        }
      });

      test('should handle multiple concurrent set operations', () async {
        // Act - perform multiple concurrent sets
        final futures = List.generate(
          5,
          (index) =>
              sharedPrefsService.set('concurrent_set_$index', 'value_$index'),
        );
        final results = await Future.wait(futures);

        // Assert
        expect(results.length, equals(5));
        for (var result in results) {
          expect(result, isTrue);
        }

        // Verify all values were set
        for (int i = 0; i < 5; i++) {
          final value = await sharedPrefsService.get('concurrent_set_$i');
          expect(value, equals('value_$i'));
        }
      });
    });

    group('Integration Scenarios', () {
      setUp(() async {
        SharedPreferences.setMockInitialValues({});
        sharedPrefsService.init();
        await sharedPrefsService.initCompleter.future;
      });

      test('should maintain data consistency across operations', () async {
        const testKey = 'consistency_test';
        const testValue = 'consistency_value';

        // Act & Assert sequence
        // 1. Set value
        final setResult = await sharedPrefsService.set(testKey, testValue);
        expect(setResult, isTrue);

        // 2. Verify it exists
        final hasResult = await sharedPrefsService.has(testKey);
        expect(hasResult, isTrue);

        // 3. Get the value
        final getResult = await sharedPrefsService.get(testKey);
        expect(getResult, equals(testValue));

        // 4. Remove the value
        final removeResult = await sharedPrefsService.remove(testKey);
        expect(removeResult, isTrue);

        // 5. Verify it's gone
        final hasAfterRemove = await sharedPrefsService.has(testKey);
        expect(hasAfterRemove, isFalse);

        final getAfterRemove = await sharedPrefsService.get(testKey);
        expect(getAfterRemove, isNull);
      });

      test('should handle complex data workflow', () async {
        // Simulate a typical app workflow
        final userData = {
          'user_id': 'user_123',
          'user_name': 'John Doe',
          'user_email': '<EMAIL>',
          'is_premium': 'true',
          'login_count': '5',
        };

        // Set all user data
        for (final entry in userData.entries) {
          final result = await sharedPrefsService.set(entry.key, entry.value);
          expect(result, isTrue, reason: 'Failed to set ${entry.key}');
        }

        // Verify all data is stored correctly
        for (final entry in userData.entries) {
          final value = await sharedPrefsService.get(entry.key);
          expect(
            value,
            equals(entry.value),
            reason: 'Wrong value for ${entry.key}',
          );
        }

        // Simulate user logout - clear all data
        await sharedPrefsService.clear();

        // Verify all data is cleared
        for (final key in userData.keys) {
          final exists = await sharedPrefsService.has(key);
          expect(exists, isFalse, reason: '$key should not exist after clear');
        }
      });
    });
  });
}
