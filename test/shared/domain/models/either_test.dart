import 'package:flutter_test/flutter_test.dart';
import 'package:selfeng/shared/domain/models/either.dart';
import 'package:selfeng/shared/domain/models/user-data/user_data.dart';
import 'package:selfeng/shared/exceptions/http_exception.dart';
import '../../../helpers/test_data_builders.dart';

void main() {
  group('Either Pattern Tests', () {
    group('Left (Error) Cases', () {
      test('should create Left with error value', () {
        final error = TestDataBuilders.appExceptionBuilder()
            .withMessage('Test error')
            .build();

        final either = Either<AppException, String>.left(error);

        expect(either.isLeft(), isTrue);
        expect(either.isRight(), isFalse);
        expect((either as Left).value, equals(error));
      });

      test('should handle Left with fold operation', () {
        final error = TestDataBuilders.appExceptionBuilder()
            .withMessage('Network error')
            .withStatusCode(500)
            .build();

        final either = Either<AppException, String>.left(error);

        final result = either.fold(
          (error) => 'Error: ${error.message}',
          (success) => 'Success: $success',
        );

        expect(result, equals('Error: Network error'));
      });

      test('should create Left using factory constructor', () {
        const errorMessage = 'Something went wrong';
        final either = Left<String, int>(errorMessage);

        expect(either.isLeft(), isTrue);
        expect(either.isRight(), isFalse);
        expect(either.value, equals(errorMessage));
      });
    });

    group('Right (Success) Cases', () {
      test('should create Right with success value', () {
        const successValue = 'Success data';
        final either = Either<AppException, String>.right(successValue);

        expect(either.isLeft(), isFalse);
        expect(either.isRight(), isTrue);
        expect((either as Right).value, equals(successValue));
      });

      test('should handle Right with fold operation', () {
        const successValue = 'Operation completed';
        final either = Either<AppException, String>.right(successValue);

        final result = either.fold(
          (error) => 'Error: ${error.message}',
          (success) => 'Success: $success',
        );

        expect(result, equals('Success: Operation completed'));
      });

      test('should create Right using factory constructor', () {
        const successValue = 42;
        final either = Right<String, int>(successValue);

        expect(either.isLeft(), isFalse);
        expect(either.isRight(), isTrue);
        expect(either.value, equals(successValue));
      });
    });

    group('Fold Operation', () {
      test('should execute left function for Left case', () {
        final error = TestDataBuilders.appExceptionBuilder()
            .withMessage('Database error')
            .withStatusCode(500)
            .build();

        final either = Either<AppException, String>.left(error);

        var leftExecuted = false;
        var rightExecuted = false;

        either.fold(
          (error) {
            leftExecuted = true;
            return 'Left executed';
          },
          (success) {
            rightExecuted = true;
            return 'Right executed';
          },
        );

        expect(leftExecuted, isTrue);
        expect(rightExecuted, isFalse);
      });

      test('should execute right function for Right case', () {
        const successValue = 'Success';
        final either = Either<AppException, String>.right(successValue);

        var leftExecuted = false;
        var rightExecuted = false;

        either.fold(
          (error) {
            leftExecuted = true;
            return 'Left executed';
          },
          (success) {
            rightExecuted = true;
            return 'Right executed';
          },
        );

        expect(leftExecuted, isFalse);
        expect(rightExecuted, isTrue);
      });

      test('should return correct value from fold functions', () {
        final leftEither = Either<String, int>.left('error');
        final rightEither = Either<String, int>.right(42);

        final leftResult = leftEither.fold(
          (error) => 'Error: $error',
          (value) => 'Value: $value',
        );

        final rightResult = rightEither.fold(
          (error) => 'Error: $error',
          (value) => 'Value: $value',
        );

        expect(leftResult, equals('Error: error'));
        expect(rightResult, equals('Value: 42'));
      });
    });

    group('Type Safety', () {
      test('should maintain type safety with different types', () {
        final stringError = Either<String, int>.left('string error');
        final intSuccess = Either<String, int>.right(123);

        expect(stringError.isLeft(), isTrue);
        expect(intSuccess.isRight(), isTrue);

        final stringResult = stringError.fold(
          (error) => error.length, // String method
          (value) => value * 2, // Int method
        );

        final intResult = intSuccess.fold(
          (error) => error.length, // String method
          (value) => value * 2, // Int method
        );

        expect(stringResult, equals(12)); // 'string error'.length
        expect(intResult, equals(246)); // 123 * 2
      });

      test('should work with complex types', () {
        final error = TestDataBuilders.appExceptionBuilder()
            .withMessage('Complex error')
            .build();

        final userData = TestDataBuilders.userDataBuilder()
            .withEmail('<EMAIL>')
            .build();

        final errorEither = Either<AppException, UserData>.left(error);
        final successEither = Either<AppException, UserData>.right(userData);

        final errorResult = errorEither.fold(
          (error) => error.message,
          (user) => user.email,
        );

        final successResult = successEither.fold(
          (error) => error.message,
          (user) => user.email,
        );

        expect(errorResult, equals('Complex error'));
        expect(successResult, equals('<EMAIL>'));
      });
    });

    group('Pattern Matching with Switch', () {
      test('should work with switch expressions', () {
        final leftEither = Either<String, int>.left('error');
        final rightEither = Either<String, int>.right(42);

        final leftResult = switch (leftEither) {
          Left(:final value) => 'Left: $value',
          Right(:final value) => 'Right: $value',
        };

        final rightResult = switch (rightEither) {
          Left(:final value) => 'Left: $value',
          Right(:final value) => 'Right: $value',
        };

        expect(leftResult, equals('Left: error'));
        expect(rightResult, equals('Right: 42'));
      });
    });

    group('Real-world Usage Scenarios', () {
      test('should handle API response scenarios', () {
        // Simulate successful API response
        final successResponse =
            Either<AppException, Map<String, dynamic>>.right({
              'id': '123',
              'name': 'Test User',
              'email': '<EMAIL>',
            });

        final successResult = successResponse.fold(
          (error) => 'API Error: ${error.message}',
          (data) => 'User: ${data['name']}',
        );

        expect(successResult, equals('User: Test User'));

        // Simulate API error response
        final errorResponse = Either<AppException, Map<String, dynamic>>.left(
          TestDataBuilders.appExceptionBuilder().asServerError().build(),
        );

        final errorResult = errorResponse.fold(
          (error) => 'API Error: ${error.message}',
          (data) => 'User: ${data['name']}',
        );

        expect(errorResult, equals('API Error: Internal server error'));
      });

      test('should handle validation scenarios', () {
        Either<String, String> validateEmail(String email) {
          if (email.isEmpty) {
            return Either.left('Email cannot be empty');
          }
          if (!email.contains('@')) {
            return Either.left('Invalid email format');
          }
          return Either.right(email);
        }

        final validEmail = validateEmail('<EMAIL>');
        final invalidEmail = validateEmail('invalid-email');
        final emptyEmail = validateEmail('');

        expect(validEmail.isRight(), isTrue);
        expect(invalidEmail.isLeft(), isTrue);
        expect(emptyEmail.isLeft(), isTrue);

        final validResult = validEmail.fold(
          (error) => 'Validation Error: $error',
          (email) => 'Valid Email: $email',
        );

        final invalidResult = invalidEmail.fold(
          (error) => 'Validation Error: $error',
          (email) => 'Valid Email: $email',
        );

        expect(validResult, equals('Valid Email: <EMAIL>'));
        expect(invalidResult, equals('Validation Error: Invalid email format'));
      });

      test('should chain operations with fold', () {
        Either<String, int> parseNumber(String input) {
          final number = int.tryParse(input);
          return number != null
              ? Either.right(number)
              : Either.left('Invalid number: $input');
        }

        Either<String, int> multiplyByTwo(int number) {
          return Either.right(number * 2);
        }

        final result = parseNumber('42').fold(
          (error) => Either<String, int>.left(error),
          (number) => multiplyByTwo(number),
        );

        expect(result.isRight(), isTrue);
        expect((result as Right).value, equals(84));

        final errorResult = parseNumber('invalid').fold(
          (error) => Either<String, int>.left(error),
          (number) => multiplyByTwo(number),
        );

        expect(errorResult.isLeft(), isTrue);
        expect((errorResult as Left).value, equals('Invalid number: invalid'));
      });
    });

    group('Equality and Hash Code', () {
      test('should have equal values when created with same data', () {
        final left1 = Either<String, int>.left('error');
        final left2 = Either<String, int>.left('error');
        final right1 = Either<String, int>.right(42);
        final right2 = Either<String, int>.right(42);

        // Compare the values since Either doesn't implement equality
        expect((left1 as Left).value, equals((left2 as Left).value));
        expect((right1 as Right).value, equals((right2 as Right).value));

        // Verify they are both Left/Right types
        expect(left1.isLeft(), equals(left2.isLeft()));
        expect(right1.isRight(), equals(right2.isRight()));
      });

      test('should not be equal when values are different', () {
        final left1 = Either<String, int>.left('error1');
        final left2 = Either<String, int>.left('error2');
        final right1 = Either<String, int>.right(42);
        final right2 = Either<String, int>.right(43);

        expect(left1, isNot(equals(left2)));
        expect(right1, isNot(equals(right2)));
      });

      test('should not be equal when types are different', () {
        final left = Either<String, int>.left('error');
        final right = Either<String, int>.right(42);

        expect(left, isNot(equals(right)));
      });
    });
  });
}
