import 'package:flutter_test/flutter_test.dart';
import 'package:selfeng/shared/domain/models/pronunciation/pronunciation_assessment_result.dart';

void main() {
  group('PronunciationAssessmentResult Tests', () {
    group('Constructor and Properties', () {
      test(
        'should create PronunciationAssessmentResult with all required properties',
        () {
          final pronunciationAssessment = PronunciationAssessmentValue(
            accuracyScore: 95.0,
            fluencyScore: 90.0,
            prosodyScore: 85.0,
            completenessScore: 100.0,
            pronScore: 92.5,
          );

          final phonemeAssessment = PhonemePronunciationAssessment(
            accuracyScore: 90.0,
          );

          final wordAssessment = WordPronunciationAssessment(
            errorType: 'None',
            accuracyScore: 95.0,
            feedback: Feedback(
              prosody: Prosody(
                prosodyBreak: Break(errorTypes: [], breakLength: 0),
                intonation: Intonation(
                  errorTypes: [],
                  monotone: Monotone(syllablePitchDeltaConfidence: 0.0),
                ),
              ),
            ),
          );

          final phoneme = PronunciationAssessmentPhoneme(
            phoneme: 'h',
            pronunciationAssessment: phonemeAssessment,
          );

          final word = PronunciationAssessmentWord(
            word: 'hello',
            pronunciationAssessment: wordAssessment,
            phonemes: [phoneme],
          );

          final priv = PronunciationAssessmentPriv(
            confidence: 0.95,
            lexical: 'hello world',
            itn: 'hello world',
            maskedItn: 'hello world',
            display: 'Hello world',
            pronunciationAssessment: pronunciationAssessment,
            words: [word],
          );

          final data = PronunciationAssessmentResultData(privPronJson: priv);

          final result = PronunciationAssessmentResult(result: data);

          expect(result.result, equals(data));
          expect(result.result.privPronJson.display, equals('Hello world'));
          expect(
            result.result.privPronJson.pronunciationAssessment.accuracyScore,
            equals(95.0),
          );
        },
      );
    });

    group('JSON Serialization', () {
      test('should serialize to JSON correctly', () {
        final pronunciationAssessment = PronunciationAssessmentValue(
          accuracyScore: 95.0,
          fluencyScore: 90.0,
          prosodyScore: 85.0,
          completenessScore: 100.0,
          pronScore: 92.5,
        );

        final phonemeAssessment = PhonemePronunciationAssessment(
          accuracyScore: 90.0,
        );

        final wordAssessment = WordPronunciationAssessment(
          errorType: 'None',
          accuracyScore: 95.0,
          feedback: Feedback(
            prosody: Prosody(
              prosodyBreak: Break(errorTypes: [], breakLength: 0),
              intonation: Intonation(
                errorTypes: [],
                monotone: Monotone(syllablePitchDeltaConfidence: 0.0),
              ),
            ),
          ),
        );

        final phoneme = PronunciationAssessmentPhoneme(
          phoneme: 'h',
          pronunciationAssessment: phonemeAssessment,
        );

        final word = PronunciationAssessmentWord(
          word: 'hello',
          pronunciationAssessment: wordAssessment,
          phonemes: [phoneme],
        );

        final priv = PronunciationAssessmentPriv(
          confidence: 0.95,
          lexical: 'hello world',
          itn: 'hello world',
          maskedItn: 'hello world',
          display: 'Hello world',
          pronunciationAssessment: pronunciationAssessment,
          words: [word],
        );

        final data = PronunciationAssessmentResultData(privPronJson: priv);

        final result = PronunciationAssessmentResult(result: data);

        final json = result.toJson();
        expect(json, isA<Map<String, dynamic>>());
        expect(json['result'], isNotNull);
      });

      test('should deserialize from JSON correctly', () {
        final json = {
          'result': {
            'privPronJson': {
              'Confidence': 0.95,
              'Lexical': 'hello world',
              'ITN': 'hello world',
              'MaskedITN': 'hello world',
              'Display': 'Hello world',
              'PronunciationAssessment': {
                'AccuracyScore': 95.0,
                'FluencyScore': 90.0,
                'ProsodyScore': 85.0,
                'CompletenessScore': 100.0,
                'PronScore': 92.5,
              },
              'Words': [
                {
                  'Word': 'hello',
                  'PronunciationAssessment': {
                    'ErrorType': 'None',
                    'AccuracyScore': 95.0,
                    'Feedback': {
                      'Prosody': {
                        'Break': {'ErrorTypes': [], 'BreakLength': 0},
                        'Intonation': {
                          'ErrorTypes': [],
                          'Monotone': {'SyllablePitchDeltaConfidence': 0.0},
                        },
                      },
                    },
                  },
                  'Phonemes': [
                    {
                      'Phoneme': 'h',
                      'PronunciationAssessment': {'AccuracyScore': 90.0},
                    },
                  ],
                },
              ],
            },
          },
        };

        final result = PronunciationAssessmentResult.fromJson(json);
        expect(result.result.privPronJson.display, equals('Hello world'));
        expect(
          result.result.privPronJson.pronunciationAssessment.accuracyScore,
          equals(95.0),
        );
        expect(result.result.privPronJson.words.length, equals(1));
        expect(result.result.privPronJson.words[0].word, equals('hello'));
      });
    });

    group('Equality and Hash Code', () {
      test('should be equal when all properties are the same', () {
        final pronunciationAssessment = PronunciationAssessmentValue(
          accuracyScore: 95.0,
          fluencyScore: 90.0,
          prosodyScore: 85.0,
          completenessScore: 100.0,
          pronScore: 92.5,
        );

        final phonemeAssessment = PhonemePronunciationAssessment(
          accuracyScore: 90.0,
        );

        final wordAssessment = WordPronunciationAssessment(
          errorType: 'None',
          accuracyScore: 95.0,
          feedback: Feedback(
            prosody: Prosody(
              prosodyBreak: Break(errorTypes: [], breakLength: 0),
              intonation: Intonation(
                errorTypes: [],
                monotone: Monotone(syllablePitchDeltaConfidence: 0.0),
              ),
            ),
          ),
        );

        final phoneme = PronunciationAssessmentPhoneme(
          phoneme: 'h',
          pronunciationAssessment: phonemeAssessment,
        );

        final word = PronunciationAssessmentWord(
          word: 'hello',
          pronunciationAssessment: wordAssessment,
          phonemes: [phoneme],
        );

        final priv1 = PronunciationAssessmentPriv(
          confidence: 0.95,
          lexical: 'hello world',
          itn: 'hello world',
          maskedItn: 'hello world',
          display: 'Hello world',
          pronunciationAssessment: pronunciationAssessment,
          words: [word],
        );

        final priv2 = PronunciationAssessmentPriv(
          confidence: 0.95,
          lexical: 'hello world',
          itn: 'hello world',
          maskedItn: 'hello world',
          display: 'Hello world',
          pronunciationAssessment: pronunciationAssessment,
          words: [word],
        );

        final data1 = PronunciationAssessmentResultData(privPronJson: priv1);

        final data2 = PronunciationAssessmentResultData(privPronJson: priv2);

        final result1 = PronunciationAssessmentResult(result: data1);

        final result2 = PronunciationAssessmentResult(result: data2);

        // Note: Since these are freezed classes, we need to compare the JSON representations
        expect(result1.toJson(), equals(result2.toJson()));
      });

      test('should not be equal when properties differ', () {
        final pronunciationAssessment1 = PronunciationAssessmentValue(
          accuracyScore: 95.0,
          fluencyScore: 90.0,
          prosodyScore: 85.0,
          completenessScore: 100.0,
          pronScore: 92.5,
        );

        final pronunciationAssessment2 = PronunciationAssessmentValue(
          accuracyScore: 90.0, // Different
          fluencyScore: 90.0,
          prosodyScore: 85.0,
          completenessScore: 100.0,
          pronScore: 92.5,
        );

        final phonemeAssessment = PhonemePronunciationAssessment(
          accuracyScore: 90.0,
        );

        final wordAssessment = WordPronunciationAssessment(
          errorType: 'None',
          accuracyScore: 95.0,
          feedback: Feedback(
            prosody: Prosody(
              prosodyBreak: Break(errorTypes: [], breakLength: 0),
              intonation: Intonation(
                errorTypes: [],
                monotone: Monotone(syllablePitchDeltaConfidence: 0.0),
              ),
            ),
          ),
        );

        final phoneme = PronunciationAssessmentPhoneme(
          phoneme: 'h',
          pronunciationAssessment: phonemeAssessment,
        );

        final word = PronunciationAssessmentWord(
          word: 'hello',
          pronunciationAssessment: wordAssessment,
          phonemes: [phoneme],
        );

        final priv1 = PronunciationAssessmentPriv(
          confidence: 0.95,
          lexical: 'hello world',
          itn: 'hello world',
          maskedItn: 'hello world',
          display: 'Hello world',
          pronunciationAssessment: pronunciationAssessment1,
          words: [word],
        );

        final priv2 = PronunciationAssessmentPriv(
          confidence: 0.95,
          lexical: 'hello world',
          itn: 'hello world',
          maskedItn: 'hello world',
          display: 'Hello world',
          pronunciationAssessment: pronunciationAssessment2,
          words: [word],
        );

        final data1 = PronunciationAssessmentResultData(privPronJson: priv1);
        final data2 = PronunciationAssessmentResultData(privPronJson: priv2);

        final result1 = PronunciationAssessmentResult(result: data1);
        final result2 = PronunciationAssessmentResult(result: data2);

        expect(result1.toJson(), isNot(equals(result2.toJson())));
      });
    });

    group('Optional Properties', () {
      test(
        'should handle optional properties in PronunciationAssessmentWord',
        () {
          final pronunciationAssessment = PronunciationAssessmentValue(
            accuracyScore: 95.0,
            fluencyScore: 90.0,
            prosodyScore: 85.0,
            completenessScore: 100.0,
            pronScore: 92.5,
          );

          final phonemeAssessment = PhonemePronunciationAssessment(
            accuracyScore: 90.0,
          );

          final wordAssessment = WordPronunciationAssessment(
            errorType: 'None',
            accuracyScore: 95.0,
            feedback: Feedback(
              prosody: Prosody(
                prosodyBreak: Break(errorTypes: [], breakLength: 0),
                intonation: Intonation(
                  errorTypes: [],
                  monotone: Monotone(syllablePitchDeltaConfidence: 0.0),
                ),
              ),
            ),
          );

          final phoneme = PronunciationAssessmentPhoneme(
            phoneme: 'h',
            pronunciationAssessment: phonemeAssessment,
          );

          final syllable = Syllable(
            pronunciationAssessment: phonemeAssessment,
            grapheme: 'hel',
            syllable: 'hel',
          );

          // Test with all optional properties
          final wordWithOptionals = PronunciationAssessmentWord(
            word: 'hello',
            pronunciationAssessment: wordAssessment,
            phonemes: [phoneme],
            offset: 100,
            duration: 500,
            syllables: [syllable],
          );

          final priv = PronunciationAssessmentPriv(
            confidence: 0.95,
            lexical: 'hello world',
            itn: 'hello world',
            maskedItn: 'hello world',
            display: 'Hello world',
            pronunciationAssessment: pronunciationAssessment,
            words: [wordWithOptionals],
          );

          final data = PronunciationAssessmentResultData(privPronJson: priv);
          final result = PronunciationAssessmentResult(result: data);

          expect(result.result.privPronJson.words[0].offset, equals(100));
          expect(result.result.privPronJson.words[0].duration, equals(500));
          expect(result.result.privPronJson.words[0].syllables, isNotNull);
          expect(
            result.result.privPronJson.words[0].syllables!.length,
            equals(1),
          );
          expect(
            result.result.privPronJson.words[0].syllables![0].grapheme,
            equals('hel'),
          );
        },
      );

      test(
        'should handle optional properties in PronunciationAssessmentPhoneme',
        () {
          final pronunciationAssessment = PronunciationAssessmentValue(
            accuracyScore: 95.0,
            fluencyScore: 90.0,
            prosodyScore: 85.0,
            completenessScore: 100.0,
            pronScore: 92.5,
          );

          final wordAssessment = WordPronunciationAssessment(
            errorType: 'None',
            accuracyScore: 95.0,
            feedback: Feedback(
              prosody: Prosody(
                prosodyBreak: Break(errorTypes: [], breakLength: 0),
                intonation: Intonation(
                  errorTypes: [],
                  monotone: Monotone(syllablePitchDeltaConfidence: 0.0),
                ),
              ),
            ),
          );

          // Test with all optional properties
          final phonemeWithOptionals = PronunciationAssessmentPhoneme(
            phoneme: 'h',
            pronunciationAssessment: PhonemePronunciationAssessment(
              accuracyScore: 90.0,
            ),
            offset: 50,
            duration: 200,
          );

          final word = PronunciationAssessmentWord(
            word: 'hello',
            pronunciationAssessment: wordAssessment,
            phonemes: [phonemeWithOptionals],
          );

          final priv = PronunciationAssessmentPriv(
            confidence: 0.95,
            lexical: 'hello world',
            itn: 'hello world',
            maskedItn: 'hello world',
            display: 'Hello world',
            pronunciationAssessment: pronunciationAssessment,
            words: [word],
          );

          final data = PronunciationAssessmentResultData(privPronJson: priv);
          final result = PronunciationAssessmentResult(result: data);

          expect(
            result.result.privPronJson.words[0].phonemes[0].offset,
            equals(50),
          );
          expect(
            result.result.privPronJson.words[0].phonemes[0].duration,
            equals(200),
          );
        },
      );

      test(
        'should handle optional accuracyScore in WordPronunciationAssessment',
        () {
          final pronunciationAssessment = PronunciationAssessmentValue(
            accuracyScore: 95.0,
            fluencyScore: 90.0,
            prosodyScore: 85.0,
            completenessScore: 100.0,
            pronScore: 92.5,
          );

          // Test with null accuracyScore
          final wordAssessmentWithoutScore = WordPronunciationAssessment(
            errorType: 'None',
            accuracyScore: null,
            feedback: null,
          );

          final phoneme = PronunciationAssessmentPhoneme(
            phoneme: 'h',
            pronunciationAssessment: PhonemePronunciationAssessment(
              accuracyScore: 90.0,
            ),
          );

          final word = PronunciationAssessmentWord(
            word: 'hello',
            pronunciationAssessment: wordAssessmentWithoutScore,
            phonemes: [phoneme],
          );

          final priv = PronunciationAssessmentPriv(
            confidence: 0.95,
            lexical: 'hello world',
            itn: 'hello world',
            maskedItn: 'hello world',
            display: 'Hello world',
            pronunciationAssessment: pronunciationAssessment,
            words: [word],
          );

          final data = PronunciationAssessmentResultData(privPronJson: priv);
          final result = PronunciationAssessmentResult(result: data);

          expect(
            result
                .result
                .privPronJson
                .words[0]
                .pronunciationAssessment
                .accuracyScore,
            isNull,
          );
          expect(
            result
                .result
                .privPronJson
                .words[0]
                .pronunciationAssessment
                .feedback,
            isNull,
          );
        },
      );
    });

    group('Edge Cases', () {
      test('should handle empty word list', () {
        final pronunciationAssessment = PronunciationAssessmentValue(
          accuracyScore: 95.0,
          fluencyScore: 90.0,
          prosodyScore: 85.0,
          completenessScore: 100.0,
          pronScore: 92.5,
        );

        final priv = PronunciationAssessmentPriv(
          confidence: 0.95,
          lexical: 'hello world',
          itn: 'hello world',
          maskedItn: 'hello world',
          display: 'Hello world',
          pronunciationAssessment: pronunciationAssessment,
          words: [], // Empty list
        );

        final data = PronunciationAssessmentResultData(privPronJson: priv);
        final result = PronunciationAssessmentResult(result: data);

        expect(result.result.privPronJson.words, isEmpty);
      });

      test('should handle empty phoneme list', () {
        final pronunciationAssessment = PronunciationAssessmentValue(
          accuracyScore: 95.0,
          fluencyScore: 90.0,
          prosodyScore: 85.0,
          completenessScore: 100.0,
          pronScore: 92.5,
        );

        final wordAssessment = WordPronunciationAssessment(
          errorType: 'None',
          accuracyScore: 95.0,
          feedback: Feedback(
            prosody: Prosody(
              prosodyBreak: Break(errorTypes: [], breakLength: 0),
              intonation: Intonation(
                errorTypes: [],
                monotone: Monotone(syllablePitchDeltaConfidence: 0.0),
              ),
            ),
          ),
        );

        final word = PronunciationAssessmentWord(
          word: 'hello',
          pronunciationAssessment: wordAssessment,
          phonemes: [], // Empty list
        );

        final priv = PronunciationAssessmentPriv(
          confidence: 0.95,
          lexical: 'hello world',
          itn: 'hello world',
          maskedItn: 'hello world',
          display: 'Hello world',
          pronunciationAssessment: pronunciationAssessment,
          words: [word],
        );

        final data = PronunciationAssessmentResultData(privPronJson: priv);
        final result = PronunciationAssessmentResult(result: data);

        expect(result.result.privPronJson.words[0].phonemes, isEmpty);
      });
    });

    group('Boundary Values', () {
      test('should handle boundary score values', () {
        // Test minimum scores (0.0)
        final minPronunciationAssessment = PronunciationAssessmentValue(
          accuracyScore: 0.0,
          fluencyScore: 0.0,
          prosodyScore: 0.0,
          completenessScore: 0.0,
          pronScore: 0.0,
        );

        // Test maximum scores (100.0)
        final maxPronunciationAssessment = PronunciationAssessmentValue(
          accuracyScore: 100.0,
          fluencyScore: 100.0,
          prosodyScore: 100.0,
          completenessScore: 100.0,
          pronScore: 100.0,
        );

        final phonemeAssessment = PhonemePronunciationAssessment(
          accuracyScore: 0.0,
        );

        final wordAssessment = WordPronunciationAssessment(
          errorType: 'None',
          accuracyScore: 100.0,
          feedback: Feedback(
            prosody: Prosody(
              prosodyBreak: Break(errorTypes: [], breakLength: 0),
              intonation: Intonation(
                errorTypes: [],
                monotone: Monotone(syllablePitchDeltaConfidence: 0.0),
              ),
            ),
          ),
        );

        final phoneme = PronunciationAssessmentPhoneme(
          phoneme: 'h',
          pronunciationAssessment: phonemeAssessment,
        );

        final word = PronunciationAssessmentWord(
          word: 'hello',
          pronunciationAssessment: wordAssessment,
          phonemes: [phoneme],
        );

        // Test with minimum scores
        final privMin = PronunciationAssessmentPriv(
          confidence: 0.0,
          lexical: 'hello world',
          itn: 'hello world',
          maskedItn: 'hello world',
          display: 'Hello world',
          pronunciationAssessment: minPronunciationAssessment,
          words: [word],
        );

        // Test with maximum scores
        final privMax = PronunciationAssessmentPriv(
          confidence: 1.0,
          lexical: 'hello world',
          itn: 'hello world',
          maskedItn: 'hello world',
          display: 'Hello world',
          pronunciationAssessment: maxPronunciationAssessment,
          words: [word],
        );

        final dataMin = PronunciationAssessmentResultData(
          privPronJson: privMin,
        );
        final dataMax = PronunciationAssessmentResultData(
          privPronJson: privMax,
        );

        final resultMin = PronunciationAssessmentResult(result: dataMin);
        final resultMax = PronunciationAssessmentResult(result: dataMax);

        expect(
          resultMin.result.privPronJson.pronunciationAssessment.accuracyScore,
          equals(0.0),
        );
        expect(
          resultMax.result.privPronJson.pronunciationAssessment.accuracyScore,
          equals(100.0),
        );
        expect(resultMin.result.privPronJson.confidence, equals(0.0));
        expect(resultMax.result.privPronJson.confidence, equals(1.0));
      });
    });

    group('Complex Nested Structures', () {
      test('should handle UnexpectedBreak and MissingBreak in Break', () {
        final pronunciationAssessment = PronunciationAssessmentValue(
          accuracyScore: 95.0,
          fluencyScore: 90.0,
          prosodyScore: 85.0,
          completenessScore: 100.0,
          pronScore: 92.5,
        );

        final unexpectedBreak = UnexpectedBreak(confidence: 0.8);
        final missingBreak = MissingBreak(confidence: 0.6);

        final breakWithBreaks = Break(
          errorTypes: ['UnexpectedBreak', 'MissingBreak'],
          breakLength: 200,
          unexpectedBreak: unexpectedBreak,
          missingBreak: missingBreak,
        );

        final wordAssessment = WordPronunciationAssessment(
          errorType: 'None',
          accuracyScore: 95.0,
          feedback: Feedback(
            prosody: Prosody(
              prosodyBreak: breakWithBreaks,
              intonation: Intonation(
                errorTypes: ['Monotone'],
                monotone: Monotone(syllablePitchDeltaConfidence: 0.3),
              ),
            ),
          ),
        );

        final phoneme = PronunciationAssessmentPhoneme(
          phoneme: 'h',
          pronunciationAssessment: PhonemePronunciationAssessment(
            accuracyScore: 90.0,
          ),
        );

        final word = PronunciationAssessmentWord(
          word: 'hello',
          pronunciationAssessment: wordAssessment,
          phonemes: [phoneme],
        );

        final priv = PronunciationAssessmentPriv(
          confidence: 0.95,
          lexical: 'hello world',
          itn: 'hello world',
          maskedItn: 'hello world',
          display: 'Hello world',
          pronunciationAssessment: pronunciationAssessment,
          words: [word],
        );

        final data = PronunciationAssessmentResultData(privPronJson: priv);
        final result = PronunciationAssessmentResult(result: data);

        expect(
          result
              .result
              .privPronJson
              .words[0]
              .pronunciationAssessment
              .feedback!
              .prosody
              .prosodyBreak
              .unexpectedBreak!
              .confidence,
          equals(0.8),
        );
        expect(
          result
              .result
              .privPronJson
              .words[0]
              .pronunciationAssessment
              .feedback!
              .prosody
              .prosodyBreak
              .missingBreak!
              .confidence,
          equals(0.6),
        );
        expect(
          result
              .result
              .privPronJson
              .words[0]
              .pronunciationAssessment
              .feedback!
              .prosody
              .prosodyBreak
              .errorTypes,
          contains('UnexpectedBreak'),
        );
        expect(
          result
              .result
              .privPronJson
              .words[0]
              .pronunciationAssessment
              .feedback!
              .prosody
              .prosodyBreak
              .errorTypes,
          contains('MissingBreak'),
        );
      });

      test('should handle Syllable with all optional properties', () {
        final pronunciationAssessment = PronunciationAssessmentValue(
          accuracyScore: 95.0,
          fluencyScore: 90.0,
          prosodyScore: 85.0,
          completenessScore: 100.0,
          pronScore: 92.5,
        );

        final phonemeAssessment = PhonemePronunciationAssessment(
          accuracyScore: 90.0,
        );

        final syllableWithOptionals = Syllable(
          pronunciationAssessment: phonemeAssessment,
          grapheme: 'hel',
          syllable: 'hel',
          offset: 100,
          duration: 300,
        );

        final wordAssessment = WordPronunciationAssessment(
          errorType: 'None',
          accuracyScore: 95.0,
          feedback: Feedback(
            prosody: Prosody(
              prosodyBreak: Break(errorTypes: [], breakLength: 0),
              intonation: Intonation(
                errorTypes: [],
                monotone: Monotone(syllablePitchDeltaConfidence: 0.0),
              ),
            ),
          ),
        );

        final phoneme = PronunciationAssessmentPhoneme(
          phoneme: 'h',
          pronunciationAssessment: phonemeAssessment,
        );

        final word = PronunciationAssessmentWord(
          word: 'hello',
          pronunciationAssessment: wordAssessment,
          phonemes: [phoneme],
          syllables: [syllableWithOptionals],
        );

        final priv = PronunciationAssessmentPriv(
          confidence: 0.95,
          lexical: 'hello world',
          itn: 'hello world',
          maskedItn: 'hello world',
          display: 'Hello world',
          pronunciationAssessment: pronunciationAssessment,
          words: [word],
        );

        final data = PronunciationAssessmentResultData(privPronJson: priv);
        final result = PronunciationAssessmentResult(result: data);

        expect(
          result.result.privPronJson.words[0].syllables![0].grapheme,
          equals('hel'),
        );
        expect(
          result.result.privPronJson.words[0].syllables![0].syllable,
          equals('hel'),
        );
        expect(
          result.result.privPronJson.words[0].syllables![0].offset,
          equals(100),
        );
        expect(
          result.result.privPronJson.words[0].syllables![0].duration,
          equals(300),
        );
      });
    });

    group('Error Types and Combinations', () {
      test(
        'should handle different error types in WordPronunciationAssessment',
        () {
          final pronunciationAssessment = PronunciationAssessmentValue(
            accuracyScore: 95.0,
            fluencyScore: 90.0,
            prosodyScore: 85.0,
            completenessScore: 100.0,
            pronScore: 92.5,
          );

          final errorTypes = ['Mispronunciation', 'Omission', 'Insertion'];

          for (final errorType in errorTypes) {
            final wordAssessment = WordPronunciationAssessment(
              errorType: errorType,
              accuracyScore: 95.0,
              feedback: Feedback(
                prosody: Prosody(
                  prosodyBreak: Break(errorTypes: [], breakLength: 0),
                  intonation: Intonation(
                    errorTypes: [],
                    monotone: Monotone(syllablePitchDeltaConfidence: 0.0),
                  ),
                ),
              ),
            );

            final phoneme = PronunciationAssessmentPhoneme(
              phoneme: 'h',
              pronunciationAssessment: PhonemePronunciationAssessment(
                accuracyScore: 90.0,
              ),
            );

            final word = PronunciationAssessmentWord(
              word: 'hello',
              pronunciationAssessment: wordAssessment,
              phonemes: [phoneme],
            );

            final priv = PronunciationAssessmentPriv(
              confidence: 0.95,
              lexical: 'hello world',
              itn: 'hello world',
              maskedItn: 'hello world',
              display: 'Hello world',
              pronunciationAssessment: pronunciationAssessment,
              words: [word],
            );

            final data = PronunciationAssessmentResultData(privPronJson: priv);
            final result = PronunciationAssessmentResult(result: data);

            expect(
              result
                  .result
                  .privPronJson
                  .words[0]
                  .pronunciationAssessment
                  .errorType,
              equals(errorType),
            );
          }
        },
      );

      test('should handle multiple error types in Intonation', () {
        final pronunciationAssessment = PronunciationAssessmentValue(
          accuracyScore: 95.0,
          fluencyScore: 90.0,
          prosodyScore: 85.0,
          completenessScore: 100.0,
          pronScore: 92.5,
        );

        final intonationErrorTypes = ['Monotone', 'PitchJump', 'VolumeJump'];

        final wordAssessment = WordPronunciationAssessment(
          errorType: 'None',
          accuracyScore: 95.0,
          feedback: Feedback(
            prosody: Prosody(
              prosodyBreak: Break(errorTypes: [], breakLength: 0),
              intonation: Intonation(
                errorTypes: intonationErrorTypes,
                monotone: Monotone(syllablePitchDeltaConfidence: 0.0),
              ),
            ),
          ),
        );

        final phoneme = PronunciationAssessmentPhoneme(
          phoneme: 'h',
          pronunciationAssessment: PhonemePronunciationAssessment(
            accuracyScore: 90.0,
          ),
        );

        final word = PronunciationAssessmentWord(
          word: 'hello',
          pronunciationAssessment: wordAssessment,
          phonemes: [phoneme],
        );

        final priv = PronunciationAssessmentPriv(
          confidence: 0.95,
          lexical: 'hello world',
          itn: 'hello world',
          maskedItn: 'hello world',
          display: 'Hello world',
          pronunciationAssessment: pronunciationAssessment,
          words: [word],
        );

        final data = PronunciationAssessmentResultData(privPronJson: priv);
        final result = PronunciationAssessmentResult(result: data);

        expect(
          result
              .result
              .privPronJson
              .words[0]
              .pronunciationAssessment
              .feedback!
              .prosody
              .intonation
              .errorTypes,
          equals(intonationErrorTypes),
        );
        expect(
          result
              .result
              .privPronJson
              .words[0]
              .pronunciationAssessment
              .feedback!
              .prosody
              .intonation
              .errorTypes
              .length,
          equals(3),
        );
      });
    });

    group('Freezed Generated Methods', () {
      test('should support copyWith method', () {
        final pronunciationAssessment = PronunciationAssessmentValue(
          accuracyScore: 95.0,
          fluencyScore: 90.0,
          prosodyScore: 85.0,
          completenessScore: 100.0,
          pronScore: 92.5,
        );

        final phonemeAssessment = PhonemePronunciationAssessment(
          accuracyScore: 90.0,
        );

        final wordAssessment = WordPronunciationAssessment(
          errorType: 'None',
          accuracyScore: 95.0,
          feedback: Feedback(
            prosody: Prosody(
              prosodyBreak: Break(errorTypes: [], breakLength: 0),
              intonation: Intonation(
                errorTypes: [],
                monotone: Monotone(syllablePitchDeltaConfidence: 0.0),
              ),
            ),
          ),
        );

        final phoneme = PronunciationAssessmentPhoneme(
          phoneme: 'h',
          pronunciationAssessment: phonemeAssessment,
        );

        final word = PronunciationAssessmentWord(
          word: 'hello',
          pronunciationAssessment: wordAssessment,
          phonemes: [phoneme],
        );

        final priv = PronunciationAssessmentPriv(
          confidence: 0.95,
          lexical: 'hello world',
          itn: 'hello world',
          maskedItn: 'hello world',
          display: 'Hello world',
          pronunciationAssessment: pronunciationAssessment,
          words: [word],
        );

        final data = PronunciationAssessmentResultData(privPronJson: priv);
        final result = PronunciationAssessmentResult(result: data);

        // Test copyWith on main result
        final updatedResult = result.copyWith(
          result: PronunciationAssessmentResultData(
            privPronJson: priv.copyWith(display: 'Updated Hello world'),
          ),
        );

        expect(
          updatedResult.result.privPronJson.display,
          equals('Updated Hello world'),
        );
        expect(
          updatedResult.result.privPronJson.confidence,
          equals(0.95),
        ); // Unchanged
      });

      test('should support toString method', () {
        final pronunciationAssessment = PronunciationAssessmentValue(
          accuracyScore: 95.0,
          fluencyScore: 90.0,
          prosodyScore: 85.0,
          completenessScore: 100.0,
          pronScore: 92.5,
        );

        final phonemeAssessment = PhonemePronunciationAssessment(
          accuracyScore: 90.0,
        );

        final wordAssessment = WordPronunciationAssessment(
          errorType: 'None',
          accuracyScore: 95.0,
          feedback: Feedback(
            prosody: Prosody(
              prosodyBreak: Break(errorTypes: [], breakLength: 0),
              intonation: Intonation(
                errorTypes: [],
                monotone: Monotone(syllablePitchDeltaConfidence: 0.0),
              ),
            ),
          ),
        );

        final phoneme = PronunciationAssessmentPhoneme(
          phoneme: 'h',
          pronunciationAssessment: phonemeAssessment,
        );

        final word = PronunciationAssessmentWord(
          word: 'hello',
          pronunciationAssessment: wordAssessment,
          phonemes: [phoneme],
        );

        final priv = PronunciationAssessmentPriv(
          confidence: 0.95,
          lexical: 'hello world',
          itn: 'hello world',
          maskedItn: 'hello world',
          display: 'Hello world',
          pronunciationAssessment: pronunciationAssessment,
          words: [word],
        );

        final data = PronunciationAssessmentResultData(privPronJson: priv);
        final result = PronunciationAssessmentResult(result: data);

        final resultString = result.toString();
        expect(resultString, isA<String>());
        expect(resultString, isNotEmpty);
        expect(resultString, contains('PronunciationAssessmentResult'));
      });
    });

    group('Error Handling', () {
      test('should handle invalid JSON structure gracefully', () {
        // Test with completely invalid JSON
        final invalidJson = {'invalid': 'structure'};

        expect(
          () => PronunciationAssessmentResult.fromJson(invalidJson),
          throwsA(isA<TypeError>()),
        );
      });

      test('should handle missing required fields in JSON', () {
        // Test with missing required result field
        final jsonWithoutResult = <String, dynamic>{};

        expect(
          () => PronunciationAssessmentResult.fromJson(jsonWithoutResult),
          throwsA(isA<TypeError>()),
        );
      });

      test('should handle malformed pronunciation assessment JSON', () {
        final malformedJson = {
          'result': {
            'privPronJson': {
              'Confidence': 0.95,
              'Lexical': 'hello world',
              'ITN': 'hello world',
              'MaskedITN': 'hello world',
              'Display': 'Hello world',
              // Missing PronunciationAssessment field
              'Words': [],
            },
          },
        };

        expect(
          () => PronunciationAssessmentResult.fromJson(malformedJson),
          throwsA(isA<TypeError>()),
        );
      });

      test('should handle invalid score values in JSON', () {
        final jsonWithInvalidScores = {
          'result': {
            'privPronJson': {
              'Confidence': 0.95,
              'Lexical': 'hello world',
              'ITN': 'hello world',
              'MaskedITN': 'hello world',
              'Display': 'Hello world',
              'PronunciationAssessment': {
                'AccuracyScore': 'invalid', // Should be a number
                'FluencyScore': 90.0,
                'ProsodyScore': 85.0,
                'CompletenessScore': 100.0,
                'PronScore': 92.5,
              },
              'Words': [],
            },
          },
        };

        expect(
          () => PronunciationAssessmentResult.fromJson(jsonWithInvalidScores),
          throwsA(isA<TypeError>()),
        );
      });

      test(
        'should handle null values in optional fields during JSON deserialization',
        () {
          final jsonWithNulls = {
            'result': {
              'privPronJson': {
                'Confidence': 0.95,
                'Lexical': 'hello world',
                'ITN': 'hello world',
                'MaskedITN': 'hello world',
                'Display': 'Hello world',
                'PronunciationAssessment': {
                  'AccuracyScore': 95.0,
                  'FluencyScore': 90.0,
                  'ProsodyScore': 85.0,
                  'CompletenessScore': 100.0,
                  'PronScore': 92.5,
                },
                'Words': [
                  {
                    'Word': 'hello',
                    'PronunciationAssessment': {
                      'ErrorType': 'None',
                      'AccuracyScore': null, // Explicit null
                      'Feedback': null, // Explicit null
                    },
                    'Phonemes': [
                      {
                        'Phoneme': 'h',
                        'PronunciationAssessment': {'AccuracyScore': 90.0},
                        'Offset': null, // Explicit null
                        'Duration': null, // Explicit null
                      },
                    ],
                    'Offset': null, // Explicit null
                    'Duration': null, // Explicit null
                    'Syllables': null, // Explicit null
                  },
                ],
              },
            },
          };

          final result = PronunciationAssessmentResult.fromJson(jsonWithNulls);

          expect(
            result
                .result
                .privPronJson
                .words[0]
                .pronunciationAssessment
                .accuracyScore,
            isNull,
          );
          expect(
            result
                .result
                .privPronJson
                .words[0]
                .pronunciationAssessment
                .feedback,
            isNull,
          );
          expect(
            result.result.privPronJson.words[0].phonemes[0].offset,
            isNull,
          );
          expect(
            result.result.privPronJson.words[0].phonemes[0].duration,
            isNull,
          );
          expect(result.result.privPronJson.words[0].offset, isNull);
          expect(result.result.privPronJson.words[0].duration, isNull);
          expect(result.result.privPronJson.words[0].syllables, isNull);
        },
      );

      test('should handle empty error types list', () {
        final jsonWithEmptyErrorTypes = {
          'result': {
            'privPronJson': {
              'Confidence': 0.95,
              'Lexical': 'hello world',
              'ITN': 'hello world',
              'MaskedITN': 'hello world',
              'Display': 'Hello world',
              'PronunciationAssessment': {
                'AccuracyScore': 95.0,
                'FluencyScore': 90.0,
                'ProsodyScore': 85.0,
                'CompletenessScore': 100.0,
                'PronScore': 92.5,
              },
              'Words': [
                {
                  'Word': 'hello',
                  'PronunciationAssessment': {
                    'ErrorType': 'None',
                    'AccuracyScore': 95.0,
                    'Feedback': {
                      'Prosody': {
                        'Break': {'ErrorTypes': [], 'BreakLength': 0},
                        'Intonation': {
                          'ErrorTypes': [], // Empty list
                          'Monotone': {'SyllablePitchDeltaConfidence': 0.0},
                        },
                      },
                    },
                  },
                  'Phonemes': [
                    {
                      'Phoneme': 'h',
                      'PronunciationAssessment': {'AccuracyScore': 90.0},
                    },
                  ],
                },
              ],
            },
          },
        };

        final result = PronunciationAssessmentResult.fromJson(
          jsonWithEmptyErrorTypes,
        );

        expect(
          result
              .result
              .privPronJson
              .words[0]
              .pronunciationAssessment
              .feedback!
              .prosody
              .prosodyBreak
              .errorTypes,
          isEmpty,
        );
        expect(
          result
              .result
              .privPronJson
              .words[0]
              .pronunciationAssessment
              .feedback!
              .prosody
              .intonation
              .errorTypes,
          isEmpty,
        );
      });
    });
  });
}
