import 'package:flutter_test/flutter_test.dart';
import 'package:selfeng/shared/domain/models/pronunciation/pronunciation_result_formatted.dart';

void main() {
  group('PronunciationResultFormatted Tests', () {
    group('Constructor and Properties', () {
      test(
        'should create PronunciationResultFormatted with all required properties',
        () {
          final formattedResults = [
            FormattedResult(
              text: 'hello',
              accuracyScore: 95.0,
              errorType: 'Mispronunciation',
            ),
          ];

          final pronunciationResult = PronunciationResultFormatted(
            originalText: 'hello world',
            recordedInput: 'hello world',
            accuracyScore: 95.0,
            fluencyScore: 90.0,
            prosodyScore: 85.0,
            completenessScore: 100.0,
            pronScore: 92.5,
            formattedResult: formattedResults,
          );

          expect(pronunciationResult.originalText, equals('hello world'));
          expect(pronunciationResult.recordedInput, equals('hello world'));
          expect(pronunciationResult.accuracyScore, equals(95.0));
          expect(pronunciationResult.fluencyScore, equals(90.0));
          expect(pronunciationResult.prosodyScore, equals(85.0));
          expect(pronunciationResult.completenessScore, equals(100.0));
          expect(pronunciationResult.pronScore, equals(92.5));
          expect(pronunciationResult.formattedResult, equals(formattedResults));
        },
      );
    });

    group('JSON Serialization', () {
      test('should serialize to JSON correctly', () {
        final formattedResults = [
          FormattedResult(
            text: 'hello',
            accuracyScore: 95.0,
            errorType: 'Mispronunciation',
          ),
        ];

        final pronunciationResult = PronunciationResultFormatted(
          originalText: 'hello world',
          recordedInput: 'hello world',
          accuracyScore: 95.0,
          fluencyScore: 90.0,
          prosodyScore: 85.0,
          completenessScore: 100.0,
          pronScore: 92.5,
          formattedResult: formattedResults,
        );

        final json = pronunciationResult.toJson();

        expect(json['originalText'], equals('hello world'));
        expect(json['recordedInput'], equals('hello world'));
        expect(json['accuracyScore'], equals(95.0));
        expect(json['fluencyScore'], equals(90.0));
        expect(json['prosodyScore'], equals(85.0));
        expect(json['completenessScore'], equals(100.0));
        expect(json['pronScore'], equals(92.5));
        expect(json['formattedResult'], isA<List<dynamic>>());
      });

      test('should deserialize from JSON correctly', () {
        final json = {
          'originalText': 'hello world',
          'recordedInput': 'hello world',
          'accuracyScore': 95.0,
          'fluencyScore': 90.0,
          'prosodyScore': 85.0,
          'completenessScore': 100.0,
          'pronScore': 92.5,
          'formattedResult': [
            {
              'text': 'hello',
              'accuracyScore': 95.0,
              'errorType': 'Mispronunciation',
            },
          ],
        };

        final pronunciationResult = PronunciationResultFormatted.fromJson(json);

        expect(pronunciationResult.originalText, equals('hello world'));
        expect(pronunciationResult.recordedInput, equals('hello world'));
        expect(pronunciationResult.accuracyScore, equals(95.0));
        expect(pronunciationResult.fluencyScore, equals(90.0));
        expect(pronunciationResult.prosodyScore, equals(85.0));
        expect(pronunciationResult.completenessScore, equals(100.0));
        expect(pronunciationResult.pronScore, equals(92.5));
        expect(
          pronunciationResult.formattedResult,
          isA<List<FormattedResult>>(),
        );
        expect(pronunciationResult.formattedResult.length, equals(1));
        expect(pronunciationResult.formattedResult[0].text, equals('hello'));
      });
    });

    group('Equality and Hash Code', () {
      test('should be equal when all properties are the same', () {
        final formattedResults = [
          FormattedResult(
            text: 'hello',
            accuracyScore: 95.0,
            errorType: 'Mispronunciation',
          ),
        ];

        final result1 = PronunciationResultFormatted(
          originalText: 'hello world',
          recordedInput: 'hello world',
          accuracyScore: 95.0,
          fluencyScore: 90.0,
          prosodyScore: 85.0,
          completenessScore: 100.0,
          pronScore: 92.5,
          formattedResult: formattedResults,
        );

        final result2 = PronunciationResultFormatted(
          originalText: 'hello world',
          recordedInput: 'hello world',
          accuracyScore: 95.0,
          fluencyScore: 90.0,
          prosodyScore: 85.0,
          completenessScore: 100.0,
          pronScore: 92.5,
          formattedResult: formattedResults,
        );

        expect(result1, equals(result2));
        expect(result1.hashCode, equals(result2.hashCode));
      });

      test('should not be equal when originalText is different', () {
        final formattedResults = [
          FormattedResult(
            text: 'hello',
            accuracyScore: 95.0,
            errorType: 'Mispronunciation',
          ),
        ];

        final result1 = PronunciationResultFormatted(
          originalText: 'hello world',
          recordedInput: 'hello world',
          accuracyScore: 95.0,
          fluencyScore: 90.0,
          prosodyScore: 85.0,
          completenessScore: 100.0,
          pronScore: 92.5,
          formattedResult: formattedResults,
        );

        final result2 = PronunciationResultFormatted(
          originalText: 'goodbye world',
          recordedInput: 'hello world',
          accuracyScore: 95.0,
          fluencyScore: 90.0,
          prosodyScore: 85.0,
          completenessScore: 100.0,
          pronScore: 92.5,
          formattedResult: formattedResults,
        );

        expect(result1, isNot(equals(result2)));
        expect(result1.hashCode, isNot(equals(result2.hashCode)));
      });
    });

    group('ToString Method', () {
      test('should provide readable string representation', () {
        final formattedResults = [
          FormattedResult(
            text: 'hello',
            accuracyScore: 95.0,
            errorType: 'Mispronunciation',
          ),
        ];

        final pronunciationResult = PronunciationResultFormatted(
          originalText: 'hello world',
          recordedInput: 'hello world',
          accuracyScore: 95.0,
          fluencyScore: 90.0,
          prosodyScore: 85.0,
          completenessScore: 100.0,
          pronScore: 92.5,
          formattedResult: formattedResults,
        );

        final stringRepresentation = pronunciationResult.toString();

        expect(stringRepresentation, contains('PronunciationResultFormatted'));
        expect(stringRepresentation, contains('originalText: hello world'));
        expect(stringRepresentation, contains('accuracyScore: 95.0'));
      });
    });

    group('Edge Cases', () {
      test('should handle empty strings', () {
        final formattedResults = [
          FormattedResult(text: '', accuracyScore: 0.0, errorType: ''),
        ];

        final pronunciationResult = PronunciationResultFormatted(
          originalText: '',
          recordedInput: '',
          accuracyScore: 0.0,
          fluencyScore: 0.0,
          prosodyScore: 0.0,
          completenessScore: 0.0,
          pronScore: 0.0,
          formattedResult: formattedResults,
        );

        expect(pronunciationResult.originalText, equals(''));
        expect(pronunciationResult.recordedInput, equals(''));
        expect(pronunciationResult.accuracyScore, equals(0.0));
      });

      test('should handle zero values', () {
        final formattedResults = [
          FormattedResult(text: 'test', accuracyScore: 0.0, errorType: 'None'),
        ];

        final pronunciationResult = PronunciationResultFormatted(
          originalText: 'test',
          recordedInput: 'test',
          accuracyScore: 0.0,
          fluencyScore: 0.0,
          prosodyScore: 0.0,
          completenessScore: 0.0,
          pronScore: 0.0,
          formattedResult: formattedResults,
        );

        expect(pronunciationResult.accuracyScore, equals(0.0));
        expect(pronunciationResult.fluencyScore, equals(0.0));
        expect(pronunciationResult.prosodyScore, equals(0.0));
        expect(pronunciationResult.completenessScore, equals(0.0));
        expect(pronunciationResult.pronScore, equals(0.0));
      });

      test('should handle maximum values', () {
        final formattedResults = [
          FormattedResult(
            text: 'test',
            accuracyScore: 100.0,
            errorType: 'Perfect',
          ),
        ];

        final pronunciationResult = PronunciationResultFormatted(
          originalText: 'test',
          recordedInput: 'test',
          accuracyScore: 100.0,
          fluencyScore: 100.0,
          prosodyScore: 100.0,
          completenessScore: 100.0,
          pronScore: 100.0,
          formattedResult: formattedResults,
        );

        expect(pronunciationResult.accuracyScore, equals(100.0));
        expect(pronunciationResult.fluencyScore, equals(100.0));
        expect(pronunciationResult.prosodyScore, equals(100.0));
        expect(pronunciationResult.completenessScore, equals(100.0));
        expect(pronunciationResult.pronScore, equals(100.0));
      });

      test('should handle empty formattedResult list', () {
        final pronunciationResult = PronunciationResultFormatted(
          originalText: 'hello world',
          recordedInput: 'hello world',
          accuracyScore: 95.0,
          fluencyScore: 90.0,
          prosodyScore: 85.0,
          completenessScore: 100.0,
          pronScore: 92.5,
          formattedResult: [],
        );

        expect(pronunciationResult.formattedResult, isEmpty);
        expect(pronunciationResult.formattedResult.length, equals(0));
      });

      test('should handle multiple formattedResult items', () {
        final formattedResults = [
          FormattedResult(
            text: 'hello',
            accuracyScore: 95.0,
            errorType: 'Mispronunciation',
          ),
          FormattedResult(
            text: 'world',
            accuracyScore: 90.0,
            errorType: 'Omission',
          ),
          FormattedResult(
            text: 'test',
            accuracyScore: 85.0,
            errorType: 'Insertion',
          ),
        ];

        final pronunciationResult = PronunciationResultFormatted(
          originalText: 'hello world test',
          recordedInput: 'hello world test',
          accuracyScore: 90.0,
          fluencyScore: 85.0,
          prosodyScore: 80.0,
          completenessScore: 95.0,
          pronScore: 87.5,
          formattedResult: formattedResults,
        );

        expect(pronunciationResult.formattedResult.length, equals(3));
        expect(pronunciationResult.formattedResult[0].text, equals('hello'));
        expect(pronunciationResult.formattedResult[1].text, equals('world'));
        expect(pronunciationResult.formattedResult[2].text, equals('test'));
      });
    });

    group('CopyWith Method', () {
      test('should copy with all new values', () {
        final formattedResults = [
          FormattedResult(
            text: 'hello',
            accuracyScore: 95.0,
            errorType: 'Mispronunciation',
          ),
        ];

        final original = PronunciationResultFormatted(
          originalText: 'hello world',
          recordedInput: 'hello world',
          accuracyScore: 95.0,
          fluencyScore: 90.0,
          prosodyScore: 85.0,
          completenessScore: 100.0,
          pronScore: 92.5,
          formattedResult: formattedResults,
        );

        final copy = original.copyWith(
          originalText: 'goodbye world',
          recordedInput: 'goodbye world',
          accuracyScore: 90.0,
          fluencyScore: 85.0,
          prosodyScore: 80.0,
          completenessScore: 95.0,
          pronScore: 87.5,
          formattedResult: [
            FormattedResult(
              text: 'goodbye',
              accuracyScore: 90.0,
              errorType: 'Omission',
            ),
          ],
        );

        expect(copy.originalText, equals('goodbye world'));
        expect(copy.accuracyScore, equals(90.0));
        expect(copy.formattedResult.length, equals(1));
        expect(copy.formattedResult[0].text, equals('goodbye'));
        expect(
          original.originalText,
          equals('hello world'),
        ); // original unchanged
      });

      test('should copy with partial new values', () {
        final formattedResults = [
          FormattedResult(
            text: 'hello',
            accuracyScore: 95.0,
            errorType: 'Mispronunciation',
          ),
        ];

        final original = PronunciationResultFormatted(
          originalText: 'hello world',
          recordedInput: 'hello world',
          accuracyScore: 95.0,
          fluencyScore: 90.0,
          prosodyScore: 85.0,
          completenessScore: 100.0,
          pronScore: 92.5,
          formattedResult: formattedResults,
        );

        final copy = original.copyWith(
          accuracyScore: 90.0,
          fluencyScore: 85.0,
          // only change some scores
        );

        expect(copy.accuracyScore, equals(90.0));
        expect(copy.fluencyScore, equals(85.0));
        expect(copy.originalText, equals('hello world')); // unchanged
        expect(copy.prosodyScore, equals(85.0)); // unchanged
      });
    });

    group('Invalid JSON Scenarios', () {
      test('should handle malformed JSON gracefully', () {
        // Test with missing required fields
        final incompleteJson = {
          'originalText': 'hello world',
          // missing recordedInput and other required fields
        };

        expect(
          () => PronunciationResultFormatted.fromJson(incompleteJson),
          throwsA(isA<TypeError>()),
        );
      });

      test('should handle null values in JSON', () {
        final jsonWithNulls = {
          'originalText': null,
          'recordedInput': null,
          'accuracyScore': null,
          'fluencyScore': null,
          'prosodyScore': null,
          'completenessScore': null,
          'pronScore': null,
          'formattedResult': null,
        };

        expect(
          () => PronunciationResultFormatted.fromJson(jsonWithNulls),
          throwsA(isA<TypeError>()),
        );
      });

      test('should handle invalid data types in JSON', () {
        final invalidJson = {
          'originalText': 'hello world',
          'recordedInput': 'hello world',
          'accuracyScore': 'invalid', // should be number
          'fluencyScore': 90.0,
          'prosodyScore': 85.0,
          'completenessScore': 100.0,
          'pronScore': 92.5,
          'formattedResult': [],
        };

        expect(
          () => PronunciationResultFormatted.fromJson(invalidJson),
          throwsA(isA<TypeError>()),
        );
      });
    });
  });

  group('FormattedResult Tests', () {
    group('Constructor and Properties', () {
      test('should create FormattedResult with all required properties', () {
        final formattedResult = FormattedResult(
          text: 'hello',
          accuracyScore: 95.0,
          errorType: 'Mispronunciation',
        );

        expect(formattedResult.text, equals('hello'));
        expect(formattedResult.accuracyScore, equals(95.0));
        expect(formattedResult.errorType, equals('Mispronunciation'));
      });
    });

    group('JSON Serialization', () {
      test('should serialize to JSON correctly', () {
        final formattedResult = FormattedResult(
          text: 'hello',
          accuracyScore: 95.0,
          errorType: 'Mispronunciation',
        );

        final json = formattedResult.toJson();

        expect(json['text'], equals('hello'));
        expect(json['accuracyScore'], equals(95.0));
        expect(json['errorType'], equals('Mispronunciation'));
      });

      test('should deserialize from JSON correctly', () {
        final json = {
          'text': 'hello',
          'accuracyScore': 85.0,
          'errorType': 'Omission',
        };

        final formattedResult = FormattedResult.fromJson(json);

        expect(formattedResult.text, equals('hello'));
        expect(formattedResult.accuracyScore, equals(85.0));
        expect(formattedResult.errorType, equals('Omission'));
      });
    });

    group('Equality and Hash Code', () {
      test('should be equal when all properties are the same', () {
        final result1 = FormattedResult(
          text: 'hello',
          accuracyScore: 95.0,
          errorType: 'Mispronunciation',
        );

        final result2 = FormattedResult(
          text: 'hello',
          accuracyScore: 95.0,
          errorType: 'Mispronunciation',
        );

        expect(result1, equals(result2));
        expect(result1.hashCode, equals(result2.hashCode));
      });

      test('should not be equal when text is different', () {
        final result1 = FormattedResult(
          text: 'hello',
          accuracyScore: 95.0,
          errorType: 'Mispronunciation',
        );

        final result2 = FormattedResult(
          text: 'world',
          accuracyScore: 95.0,
          errorType: 'Mispronunciation',
        );

        expect(result1, isNot(equals(result2)));
        expect(result1.hashCode, isNot(equals(result2.hashCode)));
      });
    });

    group('ToString Method', () {
      test('should provide readable string representation', () {
        final formattedResult = FormattedResult(
          text: 'hello',
          accuracyScore: 95.0,
          errorType: 'Mispronunciation',
        );

        final stringRepresentation = formattedResult.toString();

        expect(stringRepresentation, contains('FormattedResult'));
        expect(stringRepresentation, contains('text: hello'));
        expect(stringRepresentation, contains('accuracyScore: 95.0'));
        expect(stringRepresentation, contains('errorType: Mispronunciation'));
      });
    });

    group('Edge Cases', () {
      test('should handle empty text and errorType', () {
        final formattedResult = FormattedResult(
          text: '',
          accuracyScore: 0.0,
          errorType: '',
        );

        expect(formattedResult.text, equals(''));
        expect(formattedResult.accuracyScore, equals(0.0));
        expect(formattedResult.errorType, equals(''));
      });

      test('should handle zero accuracy score', () {
        final formattedResult = FormattedResult(
          text: 'test',
          accuracyScore: 0.0,
          errorType: 'No match',
        );

        expect(formattedResult.accuracyScore, equals(0.0));
        expect(formattedResult.text, equals('test'));
        expect(formattedResult.errorType, equals('No match'));
      });

      test('should handle maximum accuracy score', () {
        final formattedResult = FormattedResult(
          text: 'perfect',
          accuracyScore: 100.0,
          errorType: 'Perfect',
        );

        expect(formattedResult.accuracyScore, equals(100.0));
        expect(formattedResult.text, equals('perfect'));
        expect(formattedResult.errorType, equals('Perfect'));
      });

      test('should handle various error types', () {
        final errorTypes = [
          'Mispronunciation',
          'Omission',
          'Insertion',
          'Substitution',
          'Hesitation',
          'Repetition',
          'None',
          'Unknown',
        ];

        for (final errorType in errorTypes) {
          final formattedResult = FormattedResult(
            text: 'test',
            accuracyScore: 50.0,
            errorType: errorType,
          );

          expect(formattedResult.errorType, equals(errorType));
        }
      });
    });

    group('CopyWith Method', () {
      test('should copy with all new values', () {
        final original = FormattedResult(
          text: 'hello',
          accuracyScore: 95.0,
          errorType: 'Mispronunciation',
        );

        final copy = original.copyWith(
          text: 'world',
          accuracyScore: 90.0,
          errorType: 'Omission',
        );

        expect(copy.text, equals('world'));
        expect(copy.accuracyScore, equals(90.0));
        expect(copy.errorType, equals('Omission'));
        expect(original.text, equals('hello')); // original unchanged
      });

      test('should copy with partial new values', () {
        final original = FormattedResult(
          text: 'hello',
          accuracyScore: 95.0,
          errorType: 'Mispronunciation',
        );

        final copy = original.copyWith(
          accuracyScore: 90.0,
          // only change accuracyScore
        );

        expect(copy.text, equals('hello')); // unchanged
        expect(copy.accuracyScore, equals(90.0));
        expect(copy.errorType, equals('Mispronunciation')); // unchanged
      });
    });

    group('Invalid JSON Scenarios', () {
      test('should handle malformed JSON gracefully', () {
        // Test with missing required fields
        final incompleteJson = {
          'text': 'hello',
          // missing accuracyScore and errorType
        };

        expect(
          () => FormattedResult.fromJson(incompleteJson),
          throwsA(isA<TypeError>()),
        );
      });

      test('should handle null values in JSON', () {
        final jsonWithNulls = {
          'text': null,
          'accuracyScore': null,
          'errorType': null,
        };

        expect(
          () => FormattedResult.fromJson(jsonWithNulls),
          throwsA(isA<TypeError>()),
        );
      });

      test('should handle invalid data types in JSON', () {
        final invalidJson = {
          'text': 'hello',
          'accuracyScore': 'invalid', // should be number
          'errorType': 'Mispronunciation',
        };

        expect(
          () => FormattedResult.fromJson(invalidJson),
          throwsA(isA<TypeError>()),
        );
      });
    });
  });

  group('ScoreResult Tests', () {
    group('Constructor and Properties', () {
      test('should create ScoreResult with default values', () {
        final scoreResult = ScoreResult();

        expect(scoreResult.prosody, equals(0));
        expect(scoreResult.phoneme, equals(0));
        expect(scoreResult.completeness, equals(0));
      });

      test('should create ScoreResult with custom values', () {
        final scoreResult = ScoreResult(
          prosody: 85,
          phoneme: 90,
          completeness: 95,
        );

        expect(scoreResult.prosody, equals(85));
        expect(scoreResult.phoneme, equals(90));
        expect(scoreResult.completeness, equals(95));
      });

      test('should create ScoreResult with partial custom values', () {
        final scoreResult = ScoreResult(
          prosody: 75,
          // phoneme and completeness should default to 0
        );

        expect(scoreResult.prosody, equals(75));
        expect(scoreResult.phoneme, equals(0));
        expect(scoreResult.completeness, equals(0));
      });
    });

    group('JSON Serialization', () {
      test('should serialize to JSON correctly', () {
        final scoreResult = ScoreResult(
          prosody: 85,
          phoneme: 90,
          completeness: 95,
        );

        final json = scoreResult.toJson();

        expect(json['prosody'], equals(85));
        expect(json['phoneme'], equals(90));
        expect(json['completeness'], equals(95));
      });

      test('should serialize to JSON with default values', () {
        final scoreResult = ScoreResult();

        final json = scoreResult.toJson();

        expect(json['prosody'], equals(0));
        expect(json['phoneme'], equals(0));
        expect(json['completeness'], equals(0));
      });

      test('should deserialize from JSON correctly', () {
        final json = {'prosody': 80, 'phoneme': 85, 'completeness': 90};

        final scoreResult = ScoreResult.fromJson(json);

        expect(scoreResult.prosody, equals(80));
        expect(scoreResult.phoneme, equals(85));
        expect(scoreResult.completeness, equals(90));
      });

      test('should deserialize from JSON with missing fields (defaults)', () {
        final json = {
          'prosody': 75,
          // phoneme and completeness missing
        };

        final scoreResult = ScoreResult.fromJson(json);

        expect(scoreResult.prosody, equals(75));
        expect(scoreResult.phoneme, equals(0));
        expect(scoreResult.completeness, equals(0));
      });
    });

    group('Equality and Hash Code', () {
      test('should be equal when all properties are the same', () {
        final result1 = ScoreResult(prosody: 85, phoneme: 90, completeness: 95);

        final result2 = ScoreResult(prosody: 85, phoneme: 90, completeness: 95);

        expect(result1, equals(result2));
        expect(result1.hashCode, equals(result2.hashCode));
      });

      test('should not be equal when prosody is different', () {
        final result1 = ScoreResult(prosody: 85, phoneme: 90, completeness: 95);

        final result2 = ScoreResult(prosody: 80, phoneme: 90, completeness: 95);

        expect(result1, isNot(equals(result2)));
        expect(result1.hashCode, isNot(equals(result2.hashCode)));
      });

      test('should not be equal when phoneme is different', () {
        final result1 = ScoreResult(prosody: 85, phoneme: 90, completeness: 95);

        final result2 = ScoreResult(prosody: 85, phoneme: 85, completeness: 95);

        expect(result1, isNot(equals(result2)));
        expect(result1.hashCode, isNot(equals(result2.hashCode)));
      });

      test('should not be equal when completeness is different', () {
        final result1 = ScoreResult(prosody: 85, phoneme: 90, completeness: 95);

        final result2 = ScoreResult(prosody: 85, phoneme: 90, completeness: 90);

        expect(result1, isNot(equals(result2)));
        expect(result1.hashCode, isNot(equals(result2.hashCode)));
      });
    });

    group('ToString Method', () {
      test('should provide readable string representation', () {
        final scoreResult = ScoreResult(
          prosody: 85,
          phoneme: 90,
          completeness: 95,
        );

        final stringRepresentation = scoreResult.toString();

        expect(stringRepresentation, contains('ScoreResult'));
        expect(stringRepresentation, contains('prosody: 85'));
        expect(stringRepresentation, contains('phoneme: 90'));
        expect(stringRepresentation, contains('completeness: 95'));
      });
    });

    group('CopyWith Method', () {
      test('should copy with all new values', () {
        final original = ScoreResult(
          prosody: 85,
          phoneme: 90,
          completeness: 95,
        );

        final copy = original.copyWith(
          prosody: 80,
          phoneme: 85,
          completeness: 90,
        );

        expect(copy.prosody, equals(80));
        expect(copy.phoneme, equals(85));
        expect(copy.completeness, equals(90));
        expect(original.prosody, equals(85)); // original unchanged
      });

      test('should copy with partial new values', () {
        final original = ScoreResult(
          prosody: 85,
          phoneme: 90,
          completeness: 95,
        );

        final copy = original.copyWith(
          prosody: 80,
          // only change prosody
        );

        expect(copy.prosody, equals(80));
        expect(copy.phoneme, equals(90)); // unchanged
        expect(copy.completeness, equals(95)); // unchanged
      });
    });

    group('Invalid JSON Scenarios', () {
      test('should handle malformed JSON gracefully', () {
        // Test with missing fields (should use defaults)
        final incompleteJson = {
          'prosody': 85,
          // missing phoneme and completeness
        };

        final scoreResult = ScoreResult.fromJson(incompleteJson);
        expect(scoreResult.prosody, equals(85));
        expect(scoreResult.phoneme, equals(0)); // default
        expect(scoreResult.completeness, equals(0)); // default
      });

      test('should handle null values in JSON with defaults', () {
        final jsonWithNulls = {
          'prosody': null,
          'phoneme': null,
          'completeness': null,
        };

        // ScoreResult has default values, so nulls should result in defaults
        final scoreResult = ScoreResult.fromJson(jsonWithNulls);
        expect(scoreResult.prosody, equals(0)); // default
        expect(scoreResult.phoneme, equals(0)); // default
        expect(scoreResult.completeness, equals(0)); // default
      });

      test('should handle invalid data types in JSON', () {
        final invalidJson = {
          'prosody': 'invalid', // should be int
          'phoneme': 90,
          'completeness': 95,
        };

        expect(
          () => ScoreResult.fromJson(invalidJson),
          throwsA(isA<TypeError>()),
        );
      });
    });
  });
}
