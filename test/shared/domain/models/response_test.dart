import 'package:flutter_test/flutter_test.dart';
import 'package:selfeng/shared/domain/models/response.dart';
import 'package:selfeng/shared/domain/models/either.dart';
import 'package:selfeng/shared/exceptions/http_exception.dart';

void main() {
  group('Response Model Tests', () {
    group('Constructor Tests', () {
      test('should create Response with all required parameters', () {
        const statusCode = 200;
        const statusMessage = 'OK';
        const data = {'key': 'value'};
        const message = {'type': 'success'};
        const refreshToken = 'token123';

        final response = Response(
          statusCode: statusCode,
          statusMessage: statusMessage,
          data: data,
          message: message,
          refreshToken: refreshToken,
        );

        expect(response.statusCode, equals(statusCode));
        expect(response.statusMessage, equals(statusMessage));
        expect(response.data, equals(data));
        expect(response.message, equals(message));
        expect(response.refreshToken, equals(refreshToken));
      });

      test('should create Response with only required parameter', () {
        const statusCode = 404;

        final response = Response(statusCode: statusCode);

        expect(response.statusCode, equals(statusCode));
        expect(response.statusMessage, isNull);
        expect(response.data, equals(const {}));
        expect(response.message, equals(const {}));
        expect(response.refreshToken, isNull);
      });

      test(
        'should create Response with default values for optional parameters',
        () {
          const statusCode = 500;
          const statusMessage = 'Internal Server Error';

          final response = Response(
            statusCode: statusCode,
            statusMessage: statusMessage,
          );

          expect(response.statusCode, equals(statusCode));
          expect(response.statusMessage, equals(statusMessage));
          expect(response.data, equals(const {}));
          expect(response.message, equals(const {}));
          expect(response.refreshToken, isNull);
        },
      );

      test('should handle null values for optional parameters', () {
        const statusCode = 201;
        const data = {'created': true};
        const message = {'info': 'Resource created'};

        final response = Response(
          statusCode: statusCode,
          statusMessage: null,
          data: data,
          message: message,
          refreshToken: null,
        );

        expect(response.statusCode, equals(statusCode));
        expect(response.statusMessage, isNull);
        expect(response.data, equals(data));
        expect(response.message, equals(message));
        expect(response.refreshToken, isNull);
      });

      test('should handle empty strings and maps', () {
        const statusCode = 204;
        const statusMessage = '';
        const data = <String, dynamic>{};
        const message = <String, dynamic>{};
        const refreshToken = '';

        final response = Response(
          statusCode: statusCode,
          statusMessage: statusMessage,
          data: data,
          message: message,
          refreshToken: refreshToken,
        );

        expect(response.statusCode, equals(statusCode));
        expect(response.statusMessage, equals(statusMessage));
        expect(response.data, equals(data));
        expect(response.message, equals(message));
        expect(response.refreshToken, equals(refreshToken));
      });
    });

    group('toString Method Tests', () {
      test('should return correct string representation with all fields', () {
        const statusCode = 200;
        const statusMessage = 'Success';
        const data = {'user': 'John', 'id': 123};
        const message = {'type': 'info'};
        const refreshToken = 'refresh_abc';

        final response = Response(
          statusCode: statusCode,
          statusMessage: statusMessage,
          data: data,
          message: message,
          refreshToken: refreshToken,
        );

        final expectedString =
            'statusCode=200\nstatusMessage=Success\n data={user: John, id: 123}';
        expect(response.toString(), equals(expectedString));
      });

      test(
        'should return correct string representation with null statusMessage',
        () {
          const statusCode = 404;
          const data = {'error': 'Not found'};

          final response = Response(statusCode: statusCode, data: data);

          final expectedString =
              'statusCode=404\nstatusMessage=null\n data={error: Not found}';
          expect(response.toString(), equals(expectedString));
        },
      );

      test('should return correct string representation with complex data', () {
        const statusCode = 500;
        const statusMessage = 'Server Error';
        final data = {
          'errors': [
            {'field': 'email', 'message': 'Invalid format'},
            {'field': 'password', 'message': 'Too short'},
          ],
          'timestamp': '2023-01-01T00:00:00Z',
        };

        final response = Response(
          statusCode: statusCode,
          statusMessage: statusMessage,
          data: data,
        );

        final result = response.toString();
        expect(result, contains('statusCode=500'));
        expect(result, contains('statusMessage=Server Error'));
        expect(result, contains('data='));
        expect(result, contains('errors'));
        expect(result, contains('timestamp'));
      });

      test('should handle empty data and message maps', () {
        const statusCode = 204;
        const statusMessage = 'No Content';

        final response = Response(
          statusCode: statusCode,
          statusMessage: statusMessage,
        );

        final expectedString =
            'statusCode=204\nstatusMessage=No Content\n data={}';
        expect(response.toString(), equals(expectedString));
      });
    });

    group('ResponseExtension Tests', () {
      test('should convert Response to Right<AppException, Response>', () {
        const statusCode = 200;
        const statusMessage = 'OK';
        const data = {'result': 'success'};

        final response = Response(
          statusCode: statusCode,
          statusMessage: statusMessage,
          data: data,
        );

        final result = response.toRight;

        expect(result, isA<Right<AppException, Response>>());
        expect(result.isRight(), isTrue);
        expect(result.isLeft(), isFalse);

        result.fold((error) => fail('Should not be Left'), (successResponse) {
          expect(successResponse.statusCode, equals(statusCode));
          expect(successResponse.statusMessage, equals(statusMessage));
          expect(successResponse.data, equals(data));
        });
      });

      test('should work with different Response configurations', () {
        // Test with minimal Response
        final minimalResponse = Response(statusCode: 404);
        final minimalResult = minimalResponse.toRight;

        expect(minimalResult.isRight(), isTrue);
        minimalResult.fold(
          (error) => fail('Should not be Left'),
          (response) => expect(response.statusCode, equals(404)),
        );

        // Test with full Response
        final fullResponse = Response(
          statusCode: 201,
          statusMessage: 'Created',
          data: {'id': '123'},
          message: {'info': 'Resource created'},
          refreshToken: 'token_xyz',
        );
        final fullResult = fullResponse.toRight;

        expect(fullResult.isRight(), isTrue);
        fullResult.fold((error) => fail('Should not be Left'), (response) {
          expect(response.statusCode, equals(201));
          expect(response.statusMessage, equals('Created'));
          expect(response.data, equals({'id': '123'}));
          expect(response.message, equals({'info': 'Resource created'}));
          expect(response.refreshToken, equals('token_xyz'));
        });
      });

      test('should maintain Response object integrity through extension', () {
        final originalResponse = Response(
          statusCode: 500,
          statusMessage: 'Internal Error',
          data: {'error': 'Something went wrong'},
          message: {'type': 'error'},
          refreshToken: 'refresh_token_123',
        );

        final rightResponse = originalResponse.toRight;

        rightResponse.fold((error) => fail('Should not be Left'), (response) {
          // Verify all properties are preserved
          expect(response.statusCode, equals(originalResponse.statusCode));
          expect(
            response.statusMessage,
            equals(originalResponse.statusMessage),
          );
          expect(response.data, equals(originalResponse.data));
          expect(response.message, equals(originalResponse.message));
          expect(response.refreshToken, equals(originalResponse.refreshToken));

          // Verify toString works on the extracted response
          expect(response.toString(), equals(originalResponse.toString()));
        });
      });
    });

    group('Integration Tests', () {
      test('should work correctly with Either pattern', () {
        final response = Response(
          statusCode: 200,
          statusMessage: 'Success',
          data: {'user': '<EMAIL>'},
        );

        final either = response.toRight;

        // Simulate API response handling
        final result = either.fold(
          (error) => 'Error: ${error.message}',
          (success) => 'Success: ${success.statusMessage}',
        );

        expect(result, equals('Success: Success'));
      });

      test('should handle different HTTP status codes', () {
        final responses = [
          Response(statusCode: 200, statusMessage: 'OK'),
          Response(statusCode: 201, statusMessage: 'Created'),
          Response(statusCode: 400, statusMessage: 'Bad Request'),
          Response(statusCode: 401, statusMessage: 'Unauthorized'),
          Response(statusCode: 403, statusMessage: 'Forbidden'),
          Response(statusCode: 404, statusMessage: 'Not Found'),
          Response(statusCode: 500, statusMessage: 'Internal Server Error'),
        ];

        for (final response in responses) {
          final rightEither = response.toRight;
          expect(rightEither.isRight(), isTrue);

          rightEither.fold(
            (error) => fail('Should not be Left'),
            (success) =>
                expect(success.statusCode, equals(response.statusCode)),
          );
        }
      });

      test('should handle complex data structures', () {
        final complexData = {
          'users': [
            {'id': 1, 'name': 'John', 'email': '<EMAIL>'},
            {'id': 2, 'name': 'Jane', 'email': '<EMAIL>'},
          ],
          'pagination': {'page': 1, 'limit': 10, 'total': 2},
          'metadata': {'timestamp': '2023-01-01T00:00:00Z', 'version': '1.0.0'},
        };

        final response = Response(
          statusCode: 200,
          statusMessage: 'Data retrieved successfully',
          data: complexData,
          message: {'type': 'success', 'count': 2},
        );

        final rightEither = response.toRight;

        rightEither.fold((error) => fail('Should not be Left'), (success) {
          expect(success.statusCode, equals(200));
          expect(success.statusMessage, equals('Data retrieved successfully'));
          expect(success.data, equals(complexData));
          expect(success.message, equals({'type': 'success', 'count': 2}));
        });
      });
    });

    group('Edge Cases', () {
      test('should handle very large status codes', () {
        const statusCode = 999;
        final response = Response(statusCode: statusCode);

        expect(response.statusCode, equals(statusCode));
        expect(response.toString(), contains('statusCode=999'));
      });

      test('should handle negative status codes', () {
        const statusCode = -1;
        final response = Response(statusCode: statusCode);

        expect(response.statusCode, equals(statusCode));
        expect(response.toString(), contains('statusCode=-1'));
      });

      test('should handle null data and message explicitly set', () {
        final response = Response(statusCode: 200, data: null, message: null);

        expect(response.data, isNull);
        expect(response.message, isNull);
      });

      test('should handle very long strings', () {
        final longMessage = 'A' * 1000;
        final longToken = 'B' * 500;

        final response = Response(
          statusCode: 200,
          statusMessage: longMessage,
          refreshToken: longToken,
        );

        expect(response.statusMessage, equals(longMessage));
        expect(response.refreshToken, equals(longToken));
      });
    });
  });
}
