import 'package:flutter_test/flutter_test.dart';
import 'package:selfeng/shared/exceptions/http_exception.dart';
import 'package:selfeng/shared/domain/models/either.dart';
import 'package:selfeng/shared/domain/models/response.dart';

void main() {
  group('AppException Tests', () {
    test('should create AppException with correct properties', () {
      // Arrange & Act
      final exception = AppException(
        message: 'Test message',
        statusCode: 404,
        identifier: 'Test identifier',
      );

      // Assert
      expect(exception.message, 'Test message');
      expect(exception.statusCode, 404);
      expect(exception.identifier, 'Test identifier');
    });

    test('should return correct toString output', () {
      // Arrange
      final exception = AppException(
        message: 'Test message',
        statusCode: 404,
        identifier: 'Test identifier',
      );

      // Act
      final result = exception.toString();

      // Assert
      expect(
        result,
        'statusCode=404\nmessage=Test message\nidentifier=Test identifier',
      );
    });

    test('should implement Exception interface', () {
      // Arrange
      final exception = AppException(
        message: 'Test message',
        statusCode: 404,
        identifier: 'Test identifier',
      );

      // Assert
      expect(exception, isA<Exception>());
    });
  });

  group('CacheFailureException Tests', () {
    test('should have correct default properties', () {
      // Arrange & Act
      final exception = CacheFailureException();

      // Assert
      expect(exception.message, 'Unable to save user');
      expect(exception.statusCode, 100);
      expect(exception.identifier, 'Cache failure exception');
    });

    test('should implement AppException interface', () {
      // Arrange
      final exception = CacheFailureException();

      // Assert
      expect(exception, isA<AppException>());
    });

    test('should implement Equatable correctly', () {
      // Arrange
      final exception = CacheFailureException();

      // Assert
      expect(exception.props, [
        exception.message,
        exception.statusCode,
        exception.identifier,
      ]);
    });

    test('should be equal to another CacheFailureException instance', () {
      // Arrange
      final exception1 = CacheFailureException();
      final exception2 = CacheFailureException();

      // Assert
      expect(exception1, equals(exception2));
    });

    test('should not be equal to different exception types', () {
      // Arrange
      final cacheException = CacheFailureException();
      final appException = AppException(
        message: 'Different message',
        statusCode: 200,
        identifier: 'Different identifier',
      );

      // Assert
      expect(cacheException, isNot(equals(appException)));
    });

    test('should return correct toString output', () {
      // Arrange
      final exception = CacheFailureException();

      // Act
      final result = exception.toString();

      // Assert
      expect(
        result,
        'CacheFailureException(Unable to save user, 100, Cache failure exception)',
      );
    });
  });

  group('HttpExceptionExtension Tests', () {
    test('should convert AppException to Left<AppException, Response>', () {
      // Arrange
      final exception = AppException(
        message: 'Test message',
        statusCode: 500,
        identifier: 'Test identifier',
      );

      // Act
      final result = exception.toLeft;

      // Assert
      expect(result, isA<Left<AppException, Response>>());
      expect(result.value, equals(exception));
    });

    test('should work with CacheFailureException', () {
      // Arrange
      final exception = CacheFailureException();

      // Act
      final result = exception.toLeft;

      // Assert
      expect(result, isA<Left<AppException, Response>>());
      expect(result.value, equals(exception));
    });
  });
}
