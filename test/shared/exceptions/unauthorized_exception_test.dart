import 'package:flutter_test/flutter_test.dart';
import 'package:selfeng/shared/exceptions/unauthorized_exception.dart';

void main() {
  group('UnauthorizedException Tests', () {
    test('should create UnauthorizedException with message', () {
      // Arrange & Act
      const exception = UnauthorizedException('Access denied');

      // Assert
      expect(exception.message, 'Access denied');
    });

    test('should have correct props for equality', () {
      // Arrange
      const exception = UnauthorizedException('Test message');

      // Assert
      expect(exception.props, ['Test message']);
    });

    test('should return correct toString output', () {
      // Arrange
      const exception = UnauthorizedException('Unauthorized access');

      // Act
      final result = exception.toString();

      // Assert
      expect(result, 'UnauthorizedException: Unauthorized access');
    });

    test('should implement Exception interface', () {
      // Arrange
      const exception = UnauthorizedException('Test message');

      // Assert
      expect(exception, isA<Exception>());
    });

    test('should be equal when messages are the same', () {
      // Arrange
      const exception1 = UnauthorizedException('Same message');
      const exception2 = UnauthorizedException('Same message');

      // Assert
      expect(exception1, equals(exception2));
    });

    test('should not be equal when messages are different', () {
      // Arrange
      const exception1 = UnauthorizedException('Message 1');
      const exception2 = UnauthorizedException('Message 2');

      // Assert
      expect(exception1, isNot(equals(exception2)));
    });

    test('should handle empty message', () {
      // Arrange & Act
      const exception = UnauthorizedException('');

      // Assert
      expect(exception.message, '');
      expect(exception.props, ['']);
      expect(exception.toString(), 'UnauthorizedException: ');
    });

    test(
      'should handle null message in props (though constructor requires non-null)',
      () {
        // Note: Since the constructor takes a required String, this test ensures props handles it correctly
        // Arrange
        const exception = UnauthorizedException('Test');

        // Assert
        expect(exception.props, isNotNull);
        expect(exception.props.length, 1);
      },
    );
  });
}
