import 'dart:io';

import 'package:flutter_test/flutter_test.dart';
import 'package:selfeng/shared/globals.dart';

void main() {
  group('Global Constants Tests', () {
    test('kTestMode should be true when running in a test environment', () {
      // By definition, when running `flutter test`, the FLUTTER_TEST key is set.
      expect(
        kTestMode,
        isTrue,
        reason:
            'kTestMode should be true when Platform.environment.containsKey(\'FLUTTER_TEST\') is true.',
      );
    });

    test('Product and storage constants should have correct values', () {
      expect(PRODUCTS_PER_PAGE, 20, reason: 'PRODUCTS_PER_PAGE should be 20.');
      expect(
        USER_LOCAL_STORAGE_KEY,
        'user',
        reason: 'USER_LOCAL_STORAGE_KEY should be \'user\'.',
      );
      expect(
        Locale_LOCAL_STORAGE_KEY,
        'persistentLocale',
        reason: 'Locale_LOCAL_STORAGE_KEY should be \'persistentLocale\'.',
      );
      expect(
        APP_THEME_STORAGE_KEY,
        'AppTheme',
        reason: 'APP_THEME_STORAGE_KEY should be \'AppTheme\'.',
      );
      expect(
        GOOGLE_SIGNIN_V7_MIGRATION_KEY,
        'google_signin_v7_migrated',
        reason:
            'GOOGLE_SIGNIN_V7_MIGRATION_KEY should be \'google_signin_v7_migrated\'.',
      );
    });

    test('Asset path constants should point to the correct directories', () {
      expect(
        assetImageIcon,
        'assets/images/icons',
        reason: 'assetImageIcon path should be correct.',
      );
      expect(
        assetImageOnboarding,
        'assets/images/onboarding',
        reason: 'assetImageOnboarding path should be correct.',
      );
      expect(
        assetImageLanguage,
        'assets/images/language',
        reason: 'assetImageLanguage path should be correct.',
      );
      expect(
        assetImageSetting,
        'assets/images/setting',
        reason: 'assetImageSetting path should be correct.',
      );
      expect(
        assetImageDashboard,
        'assets/images/dashboard',
        reason: 'assetImageDashboard path should be correct.',
      );
      expect(
        assetImageQuestionnaire,
        'assets/images/questionnaire',
        reason: 'assetImageQuestionnaire path should be correct.',
      );
      expect(
        assetImageDiagnosticTest,
        'assets/images/diagnostic_test',
        reason: 'assetImageDiagnosticTest path should be correct.',
      );
      expect(
        assetImageMainLesson,
        'assets/images/main_lesson',
        reason: 'assetImageMainLesson path should be correct.',
      );
      expect(
        assetImageTOC,
        'assets/images/toc',
        reason: 'assetImageTOC path should be correct.',
      );
      expect(
        assetImageGames,
        'assets/images/games',
        reason: 'assetImageGames path should be correct.',
      );
    });

    test('All asset paths should be verified for existence', () {
      final assetPaths = [
        assetImageIcon,
        assetImageOnboarding,
        assetImageLanguage,
        assetImageSetting,
        assetImageDashboard,
        assetImageQuestionnaire,
        assetImageDiagnosticTest,
        assetImageMainLesson,
        assetImageTOC,
        assetImageGames,
      ];

      for (final path in assetPaths) {
        final directory = Directory(path);
        expect(
          directory.existsSync(),
          isTrue,
          reason: 'Asset directory "$path" should exist.',
        );
      }
    });
  });
}
